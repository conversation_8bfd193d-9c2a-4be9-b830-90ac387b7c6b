import common from '@ohos.app.ability.common';
import { CastComponent } from '../componnets/CastComponent';
import { HostNameComponent } from '../componnets/HostNameComponent';
import { MobileComputerCastComponent } from '../componnets/MobileComputerCastComponent';
import { ConnectComponent } from '../componnets/ConnectComponent';
import { ScreenCastComponent } from '../componnets/ScreenCastComponent';
import { WebCastComponent } from '../componnets/WebCastComponent';
import { BottomComponent } from '../componnets/BottomComponent';
import DispatcherManager from '../comm/glueLayer/DispatcherManager';
import { DeviceInfo, NetworkInfoOptions, NetworkType } from '@ohos/libbishare_napi';
import { OnEventCallback } from '../comm/listener/OnEventCallback';
import { EventCallbackImpl } from '../comm/listener/EventCallbackImpl';


const TAG = 'HomePage';

@Entry
@Component
@Preview
struct HomePage {

  private context: common.UIAbilityContext = getContext(this) as common.UIAbilityContext;
  private eventCallbackImpl: EventCallbackImpl = new EventCallbackImpl();

  aboutToAppear() {
    this.registerEventCallback();
  }

  registerEventCallback() {
    DispatcherManager.getInstance().registerEventCallback(this.eventCallbackImpl);
  }

  aboutToDisappear() {
    DispatcherManager.getInstance().unRegisterEventCallback();
  }

  build() {
    Column() {
      Column() {
        Image($r('app.media.bg_share_text'))
          .width($r('app.float.vp_173'))
          .height($r('app.float.vp_50'))

        Row() {
          // 投屏码
          CastComponent()
          // 主机名
          HostNameComponent()
        }.width('100%')
        .justifyContent(FlexAlign.SpaceBetween)

        Row() {
          Column() {
            // 手机/电脑投屏
            MobileComputerCastComponent()
          }
          .width($r('app.float.vp_900'))
          .height($r('app.float.vp_391'))
          .borderRadius($r('app.float.vp_16'))
          .backgroundColor($r('app.color.color_FAFAFA'))
          .margin({right: $r('app.float.vp_24')})

          Column() {
            // 设备连接(设备发现/连接)
            ConnectComponent()
          }.width($r('app.float.vp_476'))
          .height($r('app.float.vp_391'))
          .borderRadius($r('app.float.vp_16'))
          .backgroundColor($r('app.color.color_FAFAFA'))
          .padding($r('app.float.vp_30'))
        }
        .margin({
          bottom:$r('app.float.vp_24')
        })

        Row() {
          Column() {
            // 投屏器投屏
            ScreenCastComponent()
          }.width($r('app.float.vp_900'))
          .height($r('app.float.vp_391'))
          .borderRadius($r('app.float.vp_16'))
          .backgroundColor($r('app.color.color_FAFAFA'))
          .margin({right: $r('app.float.vp_24')})

          Column() {
            // 网页投屏
            WebCastComponent()
          }.width($r('app.float.vp_476'))
          .height($r('app.float.vp_391'))
          .borderRadius($r('app.float.vp_16'))
          .backgroundColor($r('app.color.color_FAFAFA'))
        }

        // 底部的文件接收/设置
        BottomComponent()
      }
      .alignItems(HorizontalAlign.Start)
    }
    .width('100%')
    .height('100%')
    .backgroundColor($r('app.color.color_B4B9C3'))
    .padding({
      top: $r('app.float.vp_26'),
      left: $r('app.float.vp_260'),
      right: $r('app.float.vp_260')
    })
  }

}

