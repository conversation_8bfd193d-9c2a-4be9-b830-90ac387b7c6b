import { BlogPriority, InitOptions } from '@ohos/libbishare_napi';
import common from '@ohos.app.ability.common';
import DispatcherManager from '../comm/glueLayer/DispatcherManager';
import { Log } from '@ohos/lib_info_sender';


const TAG = 'BiShareHelper';
/**
 * BiShare工具类
 */
export class BiShareHelper {

  private static sInstance: BiShareHelper;

  private constructor() {
  }

  public static getInstance() {
    if (!BiShareHelper.sInstance) {
      BiShareHelper.sInstance = new BiShareHelper();
    }
    return BiShareHelper.sInstance;
  }

  public async initBiShareService(context: common.ApplicationContext): Promise<boolean> {
    const initOptions: InitOptions = {
      isConsole: true,
      isFile: true,
      filePath: context.filesDir + "/" + "service_" + Date.parse(new Date().toString()) + ".log",
      priority: BlogPriority.INFO
    };

    try {
      const result = DispatcherManager.getInstance().initBiShareService(context, initOptions) as Promise<boolean>;
      const isInitSuccess = await result;
      Log.showInfo(TAG, `initBiShareService isInitSuccess: ${isInitSuccess}`);
      return isInitSuccess;
    } catch (error) {
      Log.showError(TAG, `initBiShareService failed: ${error}`);
      throw new Error("initBiShareService failed: " + error);
    }
  }

  public destroy() {
    DispatcherManager.getInstance().release();
  }
}