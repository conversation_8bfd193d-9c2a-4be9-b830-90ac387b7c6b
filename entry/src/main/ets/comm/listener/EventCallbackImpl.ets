import { OnEventCallback } from './OnEventCallback';
import { DeviceInfo } from '@ohos/libbishare_napi';
import { Log } from '@ohos/lib_info_sender';


const TAG = 'EventCallbackImpl';

@Observed
export class EventCallbackImpl implements OnEventCallback {

    addDevice(deviceBean: DeviceInfo) {
      Log.showInfo(TAG, 'Device added:', deviceBean);
    }

    removeDevice(deviceBean: DeviceInfo) {
      Log.showInfo(TAG, 'Device removed:', deviceBean);
    }

    connectDevice(ip: string, type: string) {
      Log.showInfo(TAG, 'Device connected:', ip, type);
    }

    disconnectDevice(ip: string, type: string) {
      Log.showInfo(TAG, 'Device disconnected:', ip, type);
    }

    sendMessage(ip: string, key: string, value: string) {
      Log.showInfo(TAG, 'Message sent:', ip, key, value);
    }

    receiveMessage(ip: string, key: string, value: string) {
      Log.showInfo(TAG, 'Message received:', ip, key, value);
    }

    startRecordScreen(ip: string) {
      Log.showInfo(TAG, 'Start record screen:', ip);
    }

    endRecordScreen(ip: string) {
      Log.showInfo(TAG, 'End record screen:', ip);
    }

    updateDevice(oldDeviceBean: DeviceInfo, newDeviceBean: DeviceInfo) {
      Log.showInfo(TAG, 'Device updated from:', oldDeviceBean, 'to:', newDeviceBean);
    }

    screenshot(path: string) {
      Log.showInfo(TAG, 'Screenshot taken:', path);
    }

    connectWifi(ssid: string, pwd: string) {
      Log.showInfo(TAG, 'Connecting to WiFi:', ssid, pwd);
    }

}