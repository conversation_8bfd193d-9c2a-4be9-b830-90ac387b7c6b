import { DeviceInfo } from '@ohos/libbishare_napi';

/**
 * 事件回调
 */
export interface OnEventCallback {
  addDevice(deviceBean: DeviceInfo);

  removeDevice(deviceBean: DeviceInfo);

  connectDevice(ip: string, type: string);

  disconnectDevice(ip: string, type: string);

  sendMessage(ip: string, key: string, value: string);

  receiveMessage(ip: string, key: string, value: string);

  startRecordScreen(ip: string);

  endRecordScreen(ip: string);

  updateDevice(oldDeviceBean: DeviceInfo, newDeviceBean: DeviceInfo);

  screenshot(path: string);

  connectWifi(ssid: string, pwd: string);
}