import common from '@ohos.app.ability.common';
import { InitOptions } from '@ohos/libbishare_napi';
import { BiShareService } from '../service/BiShareService';
import { 
  BiShareResult, 
  ExtendedDeviceInfo, 
  DeviceEventListener,
  RecordingEventListener,
  NetworkEventListener 
} from '../types/BiShareTypes';
import { Log } from '@ohos/lib_info_sender';

const TAG = 'UIAdapter';

/**
 * UI适配器
 * 为UI层提供简洁的API接口
 * 负责：
 * 1. 简化UI层的调用复杂度
 * 2. 提供响应式数据绑定支持
 * 3. 统一的错误处理和用户反馈
 */
export class UIAdapter {
  private static instance: UIAdapter | null = null;
  private biShareService: BiShareService;
  
  // 响应式状态数据
  private discoveredDevices: ExtendedDeviceInfo[] = [];
  private connectedDevices: ExtendedDeviceInfo[] = [];
  private isDiscovering: boolean = false;
  private isRecording: boolean = false;
  private isInitialized: boolean = false;

  private constructor() {
    this.biShareService = BiShareService.getInstance();
    this.setupEventListeners();
  }

  /**
   * 获取适配器实例
   */
  static getInstance(): UIAdapter {
    if (!UIAdapter.instance) {
      UIAdapter.instance = new UIAdapter();
    }
    return UIAdapter.instance;
  }

  /**
   * 初始化服务
   */
  async initialize(context: common.ApplicationContext, options: InitOptions): Promise<boolean> {
    try {
      const result = await this.biShareService.initialize(context, options);
      
      if (result.success) {
        this.isInitialized = true;
        Log.showInfo(TAG, '服务初始化成功');
        return true;
      } else {
        Log.showError(TAG, '服务初始化失败:', result.error?.message);
        return false;
      }
    } catch (error) {
      Log.showError(TAG, '服务初始化异常:', error);
      return false;
    }
  }

  /**
   * 释放服务
   */
  async release(): Promise<boolean> {
    try {
      const result = await this.biShareService.release();
      
      if (result.success) {
        this.isInitialized = false;
        this.discoveredDevices = [];
        this.connectedDevices = [];
        this.isDiscovering = false;
        this.isRecording = false;
        Log.showInfo(TAG, '服务释放成功');
        return true;
      } else {
        Log.showError(TAG, '服务释放失败:', result.error?.message);
        return false;
      }
    } catch (error) {
      Log.showError(TAG, '服务释放异常:', error);
      return false;
    }
  }

  /**
   * 开始设备发现
   */
  async startDeviceDiscovery(): Promise<boolean> {
    if (!this.isInitialized) {
      Log.showError(TAG, '服务未初始化');
      return false;
    }

    try {
      this.isDiscovering = true;
      const result = await this.biShareService.getDeviceManager().discoverDevices();
      
      if (result.success) {
        Log.showInfo(TAG, '设备发现启动成功');
        return true;
      } else {
        this.isDiscovering = false;
        Log.showError(TAG, '设备发现启动失败:', result.error?.message);
        return false;
      }
    } catch (error) {
      this.isDiscovering = false;
      Log.showError(TAG, '设备发现异常:', error);
      return false;
    }
  }

  /**
   * 停止设备发现
   */
  async stopDeviceDiscovery(): Promise<boolean> {
    if (!this.isInitialized) {
      return true;
    }

    try {
      const result = await this.biShareService.getDeviceManager().stopDiscovery();
      this.isDiscovering = false;
      
      if (result.success) {
        Log.showInfo(TAG, '设备发现停止成功');
        return true;
      } else {
        Log.showError(TAG, '设备发现停止失败:', result.error?.message);
        return false;
      }
    } catch (error) {
      this.isDiscovering = false;
      Log.showError(TAG, '设备发现停止异常:', error);
      return false;
    }
  }

  /**
   * 连接设备
   */
  async connectDevice(deviceId: string): Promise<boolean> {
    if (!this.isInitialized) {
      Log.showError(TAG, '服务未初始化');
      return false;
    }

    try {
      const result = await this.biShareService.getDeviceManager().connectDevice(deviceId);
      
      if (result.success) {
        Log.showInfo(TAG, `设备连接成功: ${deviceId}`);
        return true;
      } else {
        Log.showError(TAG, `设备连接失败: ${result.error?.message}`);
        return false;
      }
    } catch (error) {
      Log.showError(TAG, '设备连接异常:', error);
      return false;
    }
  }

  /**
   * 断开设备连接
   */
  async disconnectDevice(deviceId: string): Promise<boolean> {
    if (!this.isInitialized) {
      return true;
    }

    try {
      const result = await this.biShareService.getDeviceManager().disconnectDevice(deviceId);
      
      if (result.success) {
        Log.showInfo(TAG, `设备断开连接成功: ${deviceId}`);
        return true;
      } else {
        Log.showError(TAG, `设备断开连接失败: ${result.error?.message}`);
        return false;
      }
    } catch (error) {
      Log.showError(TAG, '设备断开连接异常:', error);
      return false;
    }
  }

  /**
   * 获取已发现的设备列表（响应式）
   */
  getDiscoveredDevices(): ExtendedDeviceInfo[] {
    return this.discoveredDevices;
  }

  /**
   * 获取已连接的设备列表（响应式）
   */
  getConnectedDevices(): ExtendedDeviceInfo[] {
    return this.connectedDevices;
  }

  /**
   * 获取发现状态（响应式）
   */
  getDiscoveryStatus(): boolean {
    return this.isDiscovering;
  }

  /**
   * 获取录制状态（响应式）
   */
  getRecordingStatus(): boolean {
    return this.isRecording;
  }

  /**
   * 获取初始化状态（响应式）
   */
  getInitializationStatus(): boolean {
    return this.isInitialized;
  }

  /**
   * 刷新设备列表
   */
  async refreshDeviceList(): Promise<void> {
    if (!this.isInitialized) {
      return;
    }

    try {
      const result = await this.biShareService.getDeviceManager().getDiscoveredDevices();
      
      if (result.success && result.data) {
        this.discoveredDevices = result.data;
        this.connectedDevices = this.biShareService.getDeviceManager().getConnectedDevices();
      }
    } catch (error) {
      Log.showError(TAG, '刷新设备列表失败:', error);
    }
  }

  /**
   * 获取事件管理器
   */
  getEventManager() {
    return this.biShareService.getEventManager();
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 设备事件监听器
    class UIDeviceEventListener extends DeviceEventListener {
      private adapter: UIAdapter;

      constructor(adapter: UIAdapter) {
        super();
        this.adapter = adapter;
      }

      onDeviceDiscovered(device: ExtendedDeviceInfo): void {
        Log.showInfo(TAG, '设备发现:', device.id);

        // 更新发现设备列表
        const index = this.adapter.discoveredDevices.findIndex(d => d.id === device.id);
        if (index >= 0) {
          this.adapter.discoveredDevices[index] = device;
        } else {
          this.adapter.discoveredDevices.push(device);
        }
      }

      onDeviceConnected(device: ExtendedDeviceInfo): void {
        Log.showInfo(TAG, '设备连接:', device.id);

        // 更新连接设备列表
        const index = this.adapter.connectedDevices.findIndex(d => d.id === device.id);
        if (index >= 0) {
          this.adapter.connectedDevices[index] = device;
        } else {
          this.adapter.connectedDevices.push(device);
        }

        // 更新发现设备列表中的状态
        const discoveredIndex = this.adapter.discoveredDevices.findIndex(d => d.id === device.id);
        if (discoveredIndex >= 0) {
          this.adapter.discoveredDevices[discoveredIndex] = device;
        }
      }

      onDeviceDisconnected(device: ExtendedDeviceInfo): void {
        Log.showInfo(TAG, '设备断开连接:', device.id);

        // 从连接设备列表中移除
        this.adapter.connectedDevices = this.adapter.connectedDevices.filter(d => d.id !== device.id);

        // 更新发现设备列表中的状态
        const discoveredIndex = this.adapter.discoveredDevices.findIndex(d => d.id === device.id);
        if (discoveredIndex >= 0) {
          this.adapter.discoveredDevices[discoveredIndex] = device;
        }
      }
    }

    const deviceListener = new UIDeviceEventListener(this);

    // 注册事件监听器
    this.biShareService.getEventManager().addDeviceEventListener(deviceListener);
  }

  /**
   * 销毁适配器（用于测试或特殊情况）
   */
  static destroy(): void {
    if (UIAdapter.instance) {
      UIAdapter.instance.release();
      UIAdapter.instance = null;
    }
  }
}
