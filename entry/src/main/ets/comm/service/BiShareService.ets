import common from '@ohos.app.ability.common';
import { BiShareManager, InitOptions, Callback } from '@ohos/libbishare_napi';
import { DeviceManager } from '../managers/DeviceManager';
import { RecordingManager } from '../managers/RecordingManager';
import { NetworkManager } from '../managers/NetworkManager';
import { EventManager } from '../managers/EventManager';
import { BiShareError, BiShareResult } from '@ohos/libbishare_napi';
import { Log } from '@ohos/lib_info_sender';

const TAG = 'BiShareService';

/**
 * BiShare统一服务层
 * 负责：
 * 1. 服务初始化和生命周期管理
 * 2. 各功能管理器的协调
 * 3. 统一的错误处理和状态管理
 * 4. 为UI层提供简洁的API
 */
export class BiShareService {
  private static instance: BiShareService | null = null;
  private biShareManager: BiShareManager;
  private deviceManager: DeviceManager;
  private recordingManager: RecordingManager;
  private networkManager: NetworkManager;
  private eventManager: EventManager;
  
  private isInitialized: boolean = false;
  private context: common.ApplicationContext | null = null;

  private constructor() {
    this.biShareManager = BiShareManager.getInstance();
    this.deviceManager = new DeviceManager();
    this.recordingManager = new RecordingManager();
    this.networkManager = new NetworkManager();
    this.eventManager = new EventManager();
  }

  /**
   * 获取服务实例
   */
  static getInstance(): BiShareService {
    if (!BiShareService.instance) {
      BiShareService.instance = new BiShareService();
    }
    return BiShareService.instance;
  }

  /**
   * 初始化服务
   */
  async initialize(context: common.ApplicationContext, options: InitOptions): Promise<BiShareResult<boolean>> {
    try {
      if (this.isInitialized) {
        Log.showWarn(TAG, '服务已经初始化');
        return { success: true, data: true };
      }

      this.context = context;
      
      // 初始化底层服务
      const result = await this.biShareManager.initialize(
        options.isConsole,
        options.isFile,
        options.filePath,
        options.priority || 4
      );
      if (!result) {
        throw new Error('底层服务初始化失败');
      }

      // 初始化各功能管理器
      await this.deviceManager.initialize();
      await this.recordingManager.initialize();
      await this.networkManager.initialize();
      await this.eventManager.initialize();

      this.isInitialized = true;
      Log.showInfo(TAG, '服务初始化成功');
      
      return { success: true, data: true };
    } catch (error) {
      Log.showError(TAG, '服务初始化失败:', error);
      return { 
        success: false, 
        error: { 
          code: 'INIT_FAILED', 
          message: `服务初始化失败: ${error.message}` 
        } 
      };
    }
  }

  /**
   * 释放服务
   */
  async release(): Promise<BiShareResult<boolean>> {
    try {
      if (!this.isInitialized) {
        Log.showWarn(TAG, '服务未初始化');
        return { success: true, data: true };
      }

      // 释放各功能管理器
      await this.eventManager.release();
      await this.networkManager.release();
      await this.recordingManager.release();
      await this.deviceManager.release();

      // 释放底层服务
      await this.biShareManager.release();

      this.isInitialized = false;
      this.context = null;
      
      Log.showInfo(TAG, '服务释放成功');
      return { success: true, data: true };
    } catch (error) {
      Log.showError(TAG, '服务释放失败:', error);
      return { 
        success: false, 
        error: { 
          code: 'RELEASE_FAILED', 
          message: `服务释放失败: ${error.message}` 
        } 
      };
    }
  }

  /**
   * 检查服务是否已初始化
   */
  isServiceInitialized(): boolean {
    return this.isInitialized;
  }

  /**
   * 获取设备管理器
   */
  getDeviceManager(): DeviceManager {
    this.checkInitialized();
    return this.deviceManager;
  }

  /**
   * 获取录制管理器
   */
  getRecordingManager(): RecordingManager {
    this.checkInitialized();
    return this.recordingManager;
  }

  /**
   * 获取网络管理器
   */
  getNetworkManager(): NetworkManager {
    this.checkInitialized();
    return this.networkManager;
  }

  /**
   * 获取事件管理器
   */
  getEventManager(): EventManager {
    // 注意：事件管理器在初始化前也可以使用，用于注册监听器
    return this.eventManager;
  }

  /**
   * 获取应用上下文
   */
  getContext(): common.ApplicationContext {
    if (!this.context) {
      throw new Error('应用上下文未设置');
    }
    return this.context;
  }

  /**
   * 检查服务是否已初始化
   */
  private checkInitialized(): void {
    if (!this.isInitialized) {
      throw new Error('服务未初始化，请先调用initialize方法');
    }
  }

  /**
   * 销毁服务实例（用于测试或特殊情况）
   */
  static destroy(): void {
    if (BiShareService.instance) {
      BiShareService.instance.release();
      BiShareService.instance = null;
    }
  }
}
