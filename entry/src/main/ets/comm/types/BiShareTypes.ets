import { DeviceInfo } from '@ohos/libbishare_napi';

/**
 * BiShare错误类型
 */
export interface BiShareError {
  code: string;
  message: string;
  details?: any;
}

/**
 * BiShare统一返回结果
 */
export interface BiShareResult<T> {
  success: boolean;
  data?: T;
  error?: BiShareError;
}

/**
 * 设备连接状态
 */
export enum DeviceConnectionStatus {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  FAILED = 'failed'
}

/**
 * 扩展的设备信息
 */
export interface ExtendedDeviceInfo extends DeviceInfo {
  connectionStatus: DeviceConnectionStatus;
  lastConnectedTime?: number;
  connectionCount?: number;
}

/**
 * 录制状态
 */
export enum RecordingStatus {
  IDLE = 'idle',
  RECORDING = 'recording',
  PAUSED = 'paused',
  STOPPED = 'stopped'
}

/**
 * 录制信息
 */
export interface RecordingInfo {
  status: RecordingStatus;
  filePath?: string;
  duration?: number;
  startTime?: number;
}

/**
 * 网络状态
 */
export enum NetworkStatus {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected'
}

/**
 * 网络信息
 */
export interface NetworkInfo {
  status: NetworkStatus;
  ssid?: string;
  ipAddress?: string;
  macAddress?: string;
}

/**
 * 事件类型枚举
 */
export enum BiShareEventType {
  // 设备事件
  DEVICE_DISCOVERED = 'device_discovered',
  DEVICE_CONNECTED = 'device_connected',
  DEVICE_DISCONNECTED = 'device_disconnected',
  DEVICE_UPDATED = 'device_updated',
  
  // 录制事件
  RECORDING_STARTED = 'recording_started',
  RECORDING_STOPPED = 'recording_stopped',
  RECORDING_PAUSED = 'recording_paused',
  RECORDING_RESUMED = 'recording_resumed',
  
  // 网络事件
  NETWORK_CONNECTED = 'network_connected',
  NETWORK_DISCONNECTED = 'network_disconnected',
  
  // 文件事件
  FILE_RECEIVED = 'file_received',
  FILE_SENT = 'file_sent',
  
  // 消息事件
  MESSAGE_RECEIVED = 'message_received',
  MESSAGE_SENT = 'message_sent'
}

/**
 * 事件数据
 */
export interface BiShareEventData {
  type: BiShareEventType;
  timestamp: number;
  data: any;
}

/**
 * 事件监听器
 */
export interface BiShareEventListener {
  (eventData: BiShareEventData): void;
}

/**
 * 设备事件监听器
 */
export interface DeviceEventListener {
  onDeviceDiscovered?(device: ExtendedDeviceInfo): void;
  onDeviceConnected?(device: ExtendedDeviceInfo): void;
  onDeviceDisconnected?(device: ExtendedDeviceInfo): void;
  onDeviceUpdated?(oldDevice: ExtendedDeviceInfo, newDevice: ExtendedDeviceInfo): void;
}

/**
 * 录制事件监听器
 */
export interface RecordingEventListener {
  onRecordingStarted?(info: RecordingInfo): void;
  onRecordingStopped?(info: RecordingInfo): void;
  onRecordingPaused?(info: RecordingInfo): void;
  onRecordingResumed?(info: RecordingInfo): void;
}

/**
 * 网络事件监听器
 */
export interface NetworkEventListener {
  onNetworkConnected?(info: NetworkInfo): void;
  onNetworkDisconnected?(info: NetworkInfo): void;
}

/**
 * 文件传输事件监听器
 */
export interface FileEventListener {
  onFileReceived?(filePath: string, fromDevice: string): void;
  onFileSent?(filePath: string, toDevice: string): void;
}

/**
 * 消息事件监听器
 */
export interface MessageEventListener {
  onMessageReceived?(message: string, fromDevice: string): void;
  onMessageSent?(message: string, toDevice: string): void;
}
