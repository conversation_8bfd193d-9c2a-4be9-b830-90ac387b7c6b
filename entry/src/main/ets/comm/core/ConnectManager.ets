import ShareApi from '../glueLayer/ShareApi';
import { OnEventCallback } from '../listener/OnEventCallback';
import { EventData, DeviceInfo, EventCallback, EventType, Callback, DeviceInfoOptions } from '@ohos/libbishare_napi';
import { Log } from '@ohos/lib_info_sender';


const TAG = 'ConnectManager';
/**
 * 设备连接管理类
 */
export default class ConnectManager {
  private static instance: ConnectManager;
  private shareApi: ShareApi;
  private eventCallback: OnEventCallback | null = null;
  private internalHandler: EventCallback | null = null;

  private constructor() {
    this.shareApi = ShareApi.getInstance();
  }

  static getInstance(): ConnectManager {
    if (!ConnectManager.instance) {
      ConnectManager.instance = new ConnectManager();
    }
    return ConnectManager.instance;
  }

  registerEventCallback(eventCallback: OnEventCallback) {
    // 如果已经注册过，先注销旧的
    if (this.internalHandler && this.eventCallback) {
      this.unRegisterEventCallback();
    }
    this.eventCallback = eventCallback;

    // 创建内部处理器
    this.internalHandler = (eventData: EventData) => {
      if (!this.eventCallback) {
        Log.showWarn(TAG, "internalHandler called but no OnEventCallback registered.");
        return;
      }

      Log.showInfo(TAG, `Received event type ${EventType[eventData.type]}, data: ${eventData.data}`);

      try {
        // 根据 EventType 调用 OnEventCallback 的不同方法
        switch (eventData.type) {
          case EventType.DEVICE_INFO:
            const deviceInfo = JSON.parse(eventData.data) as DeviceInfo;
            this.eventCallback.addDevice(deviceInfo);
            break;

          case EventType.DEVICE_INFO_LIST:

            break;

        }
      } catch (error) {
        Log.showError(TAG, `Error processing event data for type ${EventType[eventData.type]}:`, error, "Raw data:", eventData.data);
      }
    }
    // 向底层系统注册内部处理器，监听所有我们关心的事件类型
    this.on(EventType.DEVICE_INFO, this.internalHandler);
    this.on(EventType.DEVICE_INFO_LIST, this.internalHandler);
  }

  unRegisterEventCallback() {
    if (this.internalHandler) {
      // 从底层系统注销内部处理器
      this.off(EventType.DEVICE_INFO, this.internalHandler);

      this.internalHandler = null;
      Log.showInfo(TAG, "Internal handler detached by off.");
    }
    this.eventCallback = null;
    Log.showInfo(TAG, "OnEventCallback unregistered.");
  }

  // 通过ShareApi调用EventManager的核心方法
  on(eventType: EventType, callback: EventCallback): void {
    this.shareApi.getEventManager().on(eventType, callback);
  }

  off(eventType: EventType, callback: EventCallback): void {
    this.shareApi.getEventManager().off(eventType, callback);
  }

  once(eventType: EventType, callback: EventCallback): void {
    this.shareApi.getEventManager().once(eventType, callback);
  }

  // 通过ShareApi调用DeviceManager的核心方法
  discoverDevices(callback?: Callback<boolean>): Promise<boolean> | void {
    return this.shareApi.getDeviceManager().discoverDevices(callback);
  }

  getDiscoveredDevices(callback?: Callback<DeviceInfo[]>): Promise<DeviceInfo[]> | void {
    return this.shareApi.getDeviceManager().getDiscoveredDevices(callback);
  }

  setDeviceInfo(options: DeviceInfoOptions, callback?: Callback<boolean>): Promise<boolean> | void {
    return this.shareApi.getDeviceManager().setDeviceInfo(options, callback);
  }

}