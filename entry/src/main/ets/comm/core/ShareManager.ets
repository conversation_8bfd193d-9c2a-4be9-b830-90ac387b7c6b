import ConnectManager from './ConnectManager';
import PlayManager from './PlayManager';
import ScreenManager from './ScreenManager';
import FileManager from './FileManager';
import ShareApi from '../glueLayer/ShareApi';
import { EventType, EventCallback, Callback, InitOptions, DeviceInfoOptions, DeviceInfo,
  RecordingOptions,
  NetworkInfoOptions
} from '@ohos/libbishare_napi';
import common from '@ohos.app.ability.common';
import { OnEventCallback } from '../listener/OnEventCallback';


/**
 * 与DispatcherManager交互，内部包含ConnectManager、PlayManager等管理者
 */
export default class ShareManager {
  private static instance: ShareManager;
  private connectManager: ConnectManager;
  private playManager: PlayManager;
  private screenManager: ScreenManager;
  private fileManager: FileManager;
  private shareApi: ShareApi;
  private context: common.ApplicationContext | undefined = undefined;

  private constructor() {
    this.shareApi = ShareApi.getInstance();
    this.connectManager = ConnectManager.getInstance();
    this.playManager = PlayManager.getInstance();
    this.screenManager = ScreenManager.getInstance();
    this.fileManager = FileManager.getInstance();
  }

  static getInstance(): ShareManager {
    if (!ShareManager.instance) {
      ShareManager.instance = new ShareManager();
    }
    return ShareManager.instance;
  }

  getConnectManager(): ConnectManager {
    return this.connectManager;
  }

  getPlayManager(): PlayManager {
    return this.playManager;
  }

  getScreenManager(): ScreenManager {
    return this.screenManager;
  }

  getFileManager(): FileManager {
    return this.fileManager;
  }

  // 通过ShareApi调用BiShareManager的核心方法
  initBiShareService(context: common.ApplicationContext, options: InitOptions, callback?: Callback<boolean>): Promise<boolean> | void {
    this.context = context;
    return this.shareApi.initialize(options, callback);
  }

  // 通过ShareApi调用RecordingManager的核心方法
  startScreenRecord(options: RecordingOptions, callback?: Callback<boolean>): Promise<boolean> | void {
    return this.shareApi.getRecordingManager().startScreenRecord(options, callback);
  }

  stopScreenRecord(options: RecordingOptions, callback?: Callback<boolean>): Promise<boolean> | void {
    return this.shareApi.getRecordingManager().stopScreenRecord(options, callback);
  }

  // 通过ShareApi调用DeviceManager的核心方法
  discoverDevices(callback?: Callback<boolean>): Promise<boolean> | void {
    return this.getConnectManager().discoverDevices(callback);
  }

  getDiscoveredDevices(callback?: Callback<DeviceInfo[]>): Promise<DeviceInfo[]> | void {
    return this.shareApi.getDeviceManager().getDiscoveredDevices(callback);
  }

  setDeviceInfo(options: DeviceInfoOptions, callback?: Callback<boolean>): Promise<boolean> | void {
    return this.shareApi.getDeviceManager().setDeviceInfo(options, callback);
  }

  setNetworkInfo(options: NetworkInfoOptions, callback?: Callback<boolean>): Promise<boolean> | void {
    return this.shareApi.setNetworkInfo(options, callback);
  }

  registerEventCallback(eventCallback: OnEventCallback) {
    this.getConnectManager().registerEventCallback(eventCallback);
  }

  unRegisterEventCallback() {
    this.getConnectManager().unRegisterEventCallback();
  }

  release(callback?: Callback<boolean>): Promise<boolean> | void {
    return this.shareApi.getBiShareManager().release(callback);
  }

  destroy() {

  }

} 