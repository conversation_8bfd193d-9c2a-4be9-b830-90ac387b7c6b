import common from '@ohos.app.ability.common';
import { InitOptions, Callback, DeviceInfo, NetworkInfoOptions } from '@ohos/libbishare_napi';
import ShareManager from '../core/ShareManager';
import { OnEventCallback } from '../listener/OnEventCallback';

/**
 * UI分发胶水层
 * 上层是UI层，下层是ShareManager
 */
export default class DispatcherManager {
  private static instance: DispatcherManager;
  private shareManager: ShareManager;

  private constructor() {
    this.shareManager = ShareManager.getInstance();
  }

  static getInstance(): DispatcherManager {
    if (!DispatcherManager.instance) {
      DispatcherManager.instance = new DispatcherManager();
    }
    return DispatcherManager.instance;
  }

  public initBiShareService(context: common.ApplicationContext, options: InitOptions, callback?: Callback<boolean>): Promise<boolean> | void {
    return this.shareManager.initBiShareService(context, options, callback);
  }

  // 通过ShareApi调用DeviceManager的核心方法
  discoverDevices(callback?: Callback<boolean>): Promise<boolean> | void {
    return this.shareManager.discoverDevices(callback);
  }

  getDiscoveredDevices(callback?: Callback<DeviceInfo[]>): Promise<DeviceInfo[]> | void {
    return this.shareManager.getDiscoveredDevices(callback);
  }

  setNetworkInfo(options: NetworkInfoOptions, callback?: Callback<boolean>): Promise<boolean> | void {
    return this.shareManager.setNetworkInfo(options, callback);
  }

  release(callback?: Callback<boolean>): Promise<boolean> | void {
    return this.shareManager.release(callback);
  }

  registerEventCallback(eventCallback: OnEventCallback) {
    this.shareManager.registerEventCallback(eventCallback);
  }

  unRegisterEventCallback() {
    this.shareManager.unRegisterEventCallback();
  }

} 