import { BiShareManager, RecordingManager, EventManager, DeviceManager, InitOptions,
  Callback,
  NetworkInfoOptions
} from '@ohos/libbishare_napi';

/**
 * 业务胶水层
 * 上层是ShareManager，下层是BiShareManager
 */
export default class ShareApi {
  private static instance: ShareApi;
  private biShareManager: BiShareManager;

  private constructor() {
    this.biShareManager = BiShareManager.getInstance();
  }

  static getInstance(): ShareApi {
    if (!ShareApi.instance) {
      ShareApi.instance = new ShareApi();
    }
    return ShareApi.instance;
  }

  getBiShareManager(): BiShareManager {
    return this.biShareManager;
  }

  getRecordingManager(): RecordingManager {
    return RecordingManager.getInstance();
  }

  getEventManager(): EventManager {
    return EventManager.getInstance();
  }

  getDeviceManager(): DeviceManager {
    return DeviceManager.getInstance();
  }

  initialize(options: InitOptions, callback?: Callback<boolean>): Promise<boolean> | void {
    return this.getBiShareManager().initialize(options, callback);
  }

  setNetworkInfo(options: NetworkInfoOptions, callback?: Callback<boolean>): Promise<boolean> | void {
    return this.getBiShareManager().setNetworkInfo(options, callback);
  }

} 