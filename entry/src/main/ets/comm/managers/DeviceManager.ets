import { EtsDeviceManager as NativeDeviceManager, DeviceInfo as NapiDeviceInfo } from '@ohos/libbishare_napi';
import {
  BiShareResult,
  BiShareError,
  ExtendedDeviceInfo,
  DeviceConnectionStatus,
  DeviceEventListener,
  DeviceAuthInfo,
  DeviceDetailInfo,
  DeviceInfoOptions
} from '@ohos/libbishare_napi';
import { Log } from '@ohos/lib_info_sender';

const TAG = 'DeviceManager';

/**
 * 设备管理器
 * 负责：
 * 1. 设备发现和连接管理
 * 2. 设备状态维护
 * 3. 设备事件处理
 */
export class DeviceManager {
  private nativeDeviceManager: NativeDeviceManager;
  private discoveredDevices: Map<string, ExtendedDeviceInfo> = new Map();
  private connectedDevices: Map<string, ExtendedDeviceInfo> = new Map();
  private eventListeners: Set<DeviceEventListener> = new Set();
  private isDiscovering: boolean = false;

  constructor() {
    this.nativeDeviceManager = NativeDeviceManager.getInstance();
  }

  /**
   * 初始化设备管理器
   */
  async initialize(): Promise<void> {
    Log.showInfo(TAG, '设备管理器初始化');
    // 这里可以添加初始化逻辑
  }

  /**
   * 释放设备管理器
   */
  async release(): Promise<void> {
    Log.showInfo(TAG, '设备管理器释放');
    this.discoveredDevices.clear();
    this.connectedDevices.clear();
    this.eventListeners.clear();
    this.isDiscovering = false;
  }

  /**
   * 开始设备发现
   */
  async discoverDevices(): Promise<BiShareResult<boolean>> {
    try {
      if (this.isDiscovering) {
        Log.showWarn(TAG, '设备发现已在进行中');
        return { success: true, data: true };
      }

      Log.showInfo(TAG, '开始设备发现');
      this.isDiscovering = true;

      const result = await this.nativeDeviceManager.discoverDevices();
      
      if (result) {
        Log.showInfo(TAG, '设备发现启动成功');
        return { success: true, data: true };
      } else {
        throw new Error('设备发现启动失败');
      }
    } catch (error) {
      Log.showError(TAG, '设备发现失败:', error);
      this.isDiscovering = false;
      return {
        success: false,
        error: {
          code: 'DISCOVER_FAILED',
          message: `设备发现失败: ${error.message}`
        }
      };
    }
  }

  /**
   * 停止设备发现
   */
  async stopDiscovery(): Promise<BiShareResult<boolean>> {
    try {
      if (!this.isDiscovering) {
        return { success: true, data: true };
      }

      // 这里应该调用原生的停止发现方法
      // await this.nativeDeviceManager.stopDiscovery();
      
      this.isDiscovering = false;
      Log.showInfo(TAG, '设备发现已停止');
      
      return { success: true, data: true };
    } catch (error) {
      Log.showError(TAG, '停止设备发现失败:', error);
      return {
        success: false,
        error: {
          code: 'STOP_DISCOVER_FAILED',
          message: `停止设备发现失败: ${error.message}`
        }
      };
    }
  }

  /**
   * 获取已发现的设备列表
   */
  async getDiscoveredDevices(): Promise<BiShareResult<ExtendedDeviceInfo[]>> {
    try {
      const result = await this.nativeDeviceManager.getDiscoveredDevices();
      const nativeDevices = result || [];

      // 转换为扩展设备信息
      const extendedDevices: ExtendedDeviceInfo[] = [];
      for (const device of nativeDevices) {
        const existing = this.discoveredDevices.get(device.id);
        const extendedDevice: ExtendedDeviceInfo = {
          id: device.id,
          name: device.name,
          address: device.address,
          model: device.model,
          pincode: device.pincode,
          status: device.status,
          connected: device.connected,
          connectionStatus: existing?.connectionStatus || DeviceConnectionStatus.DISCONNECTED,
          lastConnectedTime: existing?.lastConnectedTime,
          connectionCount: existing?.connectionCount || 0
        };
        extendedDevices.push(extendedDevice);
      }

      // 更新本地缓存
      for (const device of extendedDevices) {
        this.discoveredDevices.set(device.id, device);
      }

      Log.showInfo(TAG, `获取到${extendedDevices.length}个已发现设备`);
      return { success: true, data: extendedDevices };
    } catch (error) {
      Log.showError(TAG, '获取已发现设备失败:', error);
      return {
        success: false,
        error: {
          code: 'GET_DEVICES_FAILED',
          message: `获取已发现设备失败: ${error.message}`
        }
      };
    }
  }

  /**
   * 获取已连接的设备列表
   */
  getConnectedDevices(): ExtendedDeviceInfo[] {
    return Array.from(this.connectedDevices.values());
  }

  /**
   * 连接设备
   */
  async connectDevice(deviceId: string): Promise<BiShareResult<boolean>> {
    try {
      const device = this.discoveredDevices.get(deviceId);
      if (!device) {
        throw new Error(`设备${deviceId}未找到`);
      }

      Log.showInfo(TAG, `连接设备: ${deviceId}`);
      
      // 更新设备状态
      device.connectionStatus = DeviceConnectionStatus.CONNECTING;
      this.discoveredDevices.set(deviceId, device);

      // 这里应该调用原生的连接方法
      // const result = await this.nativeDeviceManager.connectDevice(deviceId);
      
      // 模拟连接成功
      device.connectionStatus = DeviceConnectionStatus.CONNECTED;
      device.lastConnectedTime = Date.now();
      device.connectionCount = (device.connectionCount || 0) + 1;
      
      this.connectedDevices.set(deviceId, device);
      this.discoveredDevices.set(deviceId, device);

      // 通知监听器
      this.notifyDeviceConnected(device);

      Log.showInfo(TAG, `设备连接成功: ${deviceId}`);
      return { success: true, data: true };
    } catch (error) {
      Log.showError(TAG, '设备连接失败:', error);
      
      // 恢复设备状态
      const device = this.discoveredDevices.get(deviceId);
      if (device) {
        device.connectionStatus = DeviceConnectionStatus.FAILED;
        this.discoveredDevices.set(deviceId, device);
      }

      return {
        success: false,
        error: {
          code: 'CONNECT_FAILED',
          message: `设备连接失败: ${error.message}`
        }
      };
    }
  }

  /**
   * 断开设备连接
   */
  async disconnectDevice(deviceId: string): Promise<BiShareResult<boolean>> {
    try {
      const device = this.connectedDevices.get(deviceId);
      if (!device) {
        Log.showWarn(TAG, `设备${deviceId}未连接`);
        return { success: true, data: true };
      }

      Log.showInfo(TAG, `断开设备连接: ${deviceId}`);
      
      // 这里应该调用原生的断开连接方法
      // await this.nativeDeviceManager.disconnectDevice(deviceId);

      // 更新设备状态
      device.connectionStatus = DeviceConnectionStatus.DISCONNECTED;
      this.connectedDevices.delete(deviceId);
      this.discoveredDevices.set(deviceId, device);

      // 通知监听器
      this.notifyDeviceDisconnected(device);

      Log.showInfo(TAG, `设备断开连接成功: ${deviceId}`);
      return { success: true, data: true };
    } catch (error) {
      Log.showError(TAG, '设备断开连接失败:', error);
      return {
        success: false,
        error: {
          code: 'DISCONNECT_FAILED',
          message: `设备断开连接失败: ${error.message}`
        }
      };
    }
  }

  /**
   * 设置设备认证信息
   */
  async setDeviceInfo(options: DeviceInfoOptions): Promise<BiShareResult<boolean>> {
    try {
      Log.showInfo(TAG, `设置设备认证信息: ${options.name}`);

      // 调用NAPI接口，传递name和password
      const result = await this.biShareManager.setDeviceInfo(options.name, options.password);

      if (result) {
        Log.showInfo(TAG, '设备认证信息设置成功');
        return { success: true, data: true };
      } else {
        throw new Error('设备认证信息设置失败');
      }
    } catch (error) {
      Log.showError(TAG, '设置设备认证信息失败:', error);
      return {
        success: false,
        error: {
          code: 'SET_DEVICE_INFO_FAILED',
          message: `设置设备认证信息失败: ${error.message}`
        }
      };
    }
  }

  /**
   * 添加设备事件监听器
   */
  addDeviceEventListener(listener: DeviceEventListener): void {
    this.eventListeners.add(listener);
  }

  /**
   * 移除设备事件监听器
   */
  removeDeviceEventListener(listener: DeviceEventListener): void {
    this.eventListeners.delete(listener);
  }

  /**
   * 处理设备发现事件
   */
  handleDeviceDiscovered(deviceData: Record<string, Object>): void {
    const extendedDevice: ExtendedDeviceInfo = {
      id: (deviceData.id as string) || '',
      name: (deviceData.name as string) || '',
      address: (deviceData.address as string) || '',
      model: (deviceData.model as string) || '',
      pincode: (deviceData.pincode as string) || '',
      status: (deviceData.status as number) || 0,
      connected: (deviceData.connected as boolean) || false,
      connectionStatus: DeviceConnectionStatus.DISCONNECTED,
      connectionCount: 0
    };

    this.discoveredDevices.set(extendedDevice.id, extendedDevice);
    this.notifyDeviceDiscovered(extendedDevice);
  }

  /**
   * 通知设备发现
   */
  private notifyDeviceDiscovered(device: ExtendedDeviceInfo): void {
    this.eventListeners.forEach(listener => {
      try {
        listener.onDeviceDiscovered?.(device);
      } catch (error) {
        Log.showError(TAG, '设备发现事件通知失败:', error);
      }
    });
  }

  /**
   * 通知设备连接
   */
  private notifyDeviceConnected(device: ExtendedDeviceInfo): void {
    this.eventListeners.forEach(listener => {
      try {
        listener.onDeviceConnected?.(device);
      } catch (error) {
        Log.showError(TAG, '设备连接事件通知失败:', error);
      }
    });
  }

  /**
   * 通知设备断开连接
   */
  private notifyDeviceDisconnected(device: ExtendedDeviceInfo): void {
    this.eventListeners.forEach(listener => {
      try {
        listener.onDeviceDisconnected?.(device);
      } catch (error) {
        Log.showError(TAG, '设备断开连接事件通知失败:', error);
      }
    });
  }
}
