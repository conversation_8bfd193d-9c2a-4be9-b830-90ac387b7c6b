import { EventManager as NativeEventManager, EventType, EventCallback, EventData } from '@ohos/libbishare_napi';
import { 
  BiShareEventType, 
  BiShareEventData, 
  BiShareEventListener,
  DeviceEventListener,
  RecordingEventListener,
  NetworkEventListener,
  FileEventListener,
  MessageEventListener
} from '../types/BiShareTypes';
import { Log } from '@ohos/lib_info_sender';

const TAG = 'EventManager';

/**
 * 事件管理器
 * 负责：
 * 1. 统一的事件监听和分发
 * 2. 事件类型转换和路由
 * 3. 事件监听器管理
 */
export class EventManager {
  private nativeEventManager: NativeEventManager;
  private eventListeners: Map<BiShareEventType, Set<BiShareEventListener>> = new Map();
  private deviceListeners: Set<DeviceEventListener> = new Set();
  private recordingListeners: Set<RecordingEventListener> = new Set();
  private networkListeners: Set<NetworkEventListener> = new Set();
  private fileListeners: Set<FileEventListener> = new Set();
  private messageListeners: Set<MessageEventListener> = new Set();
  
  private nativeEventCallback: EventCallback;

  constructor() {
    this.nativeEventManager = NativeEventManager.getInstance();
    this.nativeEventCallback = this.handleNativeEvent.bind(this);
  }

  /**
   * 初始化事件管理器
   */
  async initialize(): Promise<void> {
    Log.showInfo(TAG, '事件管理器初始化');
    
    // 注册原生事件监听
    this.registerNativeEventListeners();
  }

  /**
   * 释放事件管理器
   */
  async release(): Promise<void> {
    Log.showInfo(TAG, '事件管理器释放');
    
    // 注销原生事件监听
    this.unregisterNativeEventListeners();
    
    // 清理所有监听器
    this.eventListeners.clear();
    this.deviceListeners.clear();
    this.recordingListeners.clear();
    this.networkListeners.clear();
    this.fileListeners.clear();
    this.messageListeners.clear();
  }

  /**
   * 添加通用事件监听器
   */
  addEventListener(eventType: BiShareEventType, listener: BiShareEventListener): void {
    if (!this.eventListeners.has(eventType)) {
      this.eventListeners.set(eventType, new Set());
    }
    this.eventListeners.get(eventType)!.add(listener);
    Log.showInfo(TAG, `添加事件监听器: ${eventType}`);
  }

  /**
   * 移除通用事件监听器
   */
  removeEventListener(eventType: BiShareEventType, listener: BiShareEventListener): void {
    const listeners = this.eventListeners.get(eventType);
    if (listeners) {
      listeners.delete(listener);
      if (listeners.size === 0) {
        this.eventListeners.delete(eventType);
      }
    }
    Log.showInfo(TAG, `移除事件监听器: ${eventType}`);
  }

  /**
   * 添加设备事件监听器
   */
  addDeviceEventListener(listener: DeviceEventListener): void {
    this.deviceListeners.add(listener);
  }

  /**
   * 移除设备事件监听器
   */
  removeDeviceEventListener(listener: DeviceEventListener): void {
    this.deviceListeners.delete(listener);
  }

  /**
   * 添加录制事件监听器
   */
  addRecordingEventListener(listener: RecordingEventListener): void {
    this.recordingListeners.add(listener);
  }

  /**
   * 移除录制事件监听器
   */
  removeRecordingEventListener(listener: RecordingEventListener): void {
    this.recordingListeners.delete(listener);
  }

  /**
   * 添加网络事件监听器
   */
  addNetworkEventListener(listener: NetworkEventListener): void {
    this.networkListeners.add(listener);
  }

  /**
   * 移除网络事件监听器
   */
  removeNetworkEventListener(listener: NetworkEventListener): void {
    this.networkListeners.delete(listener);
  }

  /**
   * 添加文件事件监听器
   */
  addFileEventListener(listener: FileEventListener): void {
    this.fileListeners.add(listener);
  }

  /**
   * 移除文件事件监听器
   */
  removeFileEventListener(listener: FileEventListener): void {
    this.fileListeners.delete(listener);
  }

  /**
   * 添加消息事件监听器
   */
  addMessageEventListener(listener: MessageEventListener): void {
    this.messageListeners.add(listener);
  }

  /**
   * 移除消息事件监听器
   */
  removeMessageEventListener(listener: MessageEventListener): void {
    this.messageListeners.delete(listener);
  }

  /**
   * 发送自定义事件
   */
  emitEvent(eventType: BiShareEventType, data: any): void {
    const eventData: BiShareEventData = {
      type: eventType,
      timestamp: Date.now(),
      data: data
    };

    this.dispatchEvent(eventData);
  }

  /**
   * 注册原生事件监听器
   */
  private registerNativeEventListeners(): void {
    // 注册所有需要的原生事件类型
    this.nativeEventManager.on(EventType.DEVICE_INFO, this.nativeEventCallback);
    this.nativeEventManager.on(EventType.DEVICE_INFO_LIST, this.nativeEventCallback);
    // 可以根据需要添加更多事件类型
  }

  /**
   * 注销原生事件监听器
   */
  private unregisterNativeEventListeners(): void {
    this.nativeEventManager.off(EventType.DEVICE_INFO, this.nativeEventCallback);
    this.nativeEventManager.off(EventType.DEVICE_INFO_LIST, this.nativeEventCallback);
  }

  /**
   * 处理原生事件
   */
  private handleNativeEvent(eventData: EventData): void {
    try {
      Log.showInfo(TAG, `收到原生事件: ${EventType[eventData.type]}`);
      
      // 将原生事件转换为BiShare事件
      const biShareEvent = this.convertNativeEvent(eventData);
      if (biShareEvent) {
        this.dispatchEvent(biShareEvent);
      }
    } catch (error) {
      Log.showError(TAG, '处理原生事件失败:', error);
    }
  }

  /**
   * 转换原生事件为BiShare事件
   */
  private convertNativeEvent(nativeEvent: EventData): BiShareEventData | null {
    let biShareEventType: BiShareEventType;
    let eventData: any;

    switch (nativeEvent.type) {
      case EventType.DEVICE_INFO:
        biShareEventType = BiShareEventType.DEVICE_DISCOVERED;
        try {
          eventData = JSON.parse(nativeEvent.data);
        } catch (error) {
          Log.showError(TAG, '解析设备信息失败:', error);
          return null;
        }
        break;
      
      case EventType.DEVICE_INFO_LIST:
        biShareEventType = BiShareEventType.DEVICE_DISCOVERED;
        try {
          eventData = JSON.parse(nativeEvent.data);
        } catch (error) {
          Log.showError(TAG, '解析设备列表失败:', error);
          return null;
        }
        break;
      
      default:
        Log.showWarn(TAG, `未处理的原生事件类型: ${EventType[nativeEvent.type]}`);
        return null;
    }

    return {
      type: biShareEventType,
      timestamp: Date.now(),
      data: eventData
    };
  }

  /**
   * 分发事件
   */
  private dispatchEvent(eventData: BiShareEventData): void {
    // 分发给通用事件监听器
    const listeners = this.eventListeners.get(eventData.type);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(eventData);
        } catch (error) {
          Log.showError(TAG, '事件监听器执行失败:', error);
        }
      });
    }

    // 分发给特定类型的监听器
    this.dispatchToSpecificListeners(eventData);
  }

  /**
   * 分发给特定类型的监听器
   */
  private dispatchToSpecificListeners(eventData: BiShareEventData): void {
    switch (eventData.type) {
      case BiShareEventType.DEVICE_DISCOVERED:
      case BiShareEventType.DEVICE_CONNECTED:
      case BiShareEventType.DEVICE_DISCONNECTED:
      case BiShareEventType.DEVICE_UPDATED:
        this.dispatchDeviceEvent(eventData);
        break;
      
      case BiShareEventType.RECORDING_STARTED:
      case BiShareEventType.RECORDING_STOPPED:
      case BiShareEventType.RECORDING_PAUSED:
      case BiShareEventType.RECORDING_RESUMED:
        this.dispatchRecordingEvent(eventData);
        break;
      
      case BiShareEventType.NETWORK_CONNECTED:
      case BiShareEventType.NETWORK_DISCONNECTED:
        this.dispatchNetworkEvent(eventData);
        break;
      
      case BiShareEventType.FILE_RECEIVED:
      case BiShareEventType.FILE_SENT:
        this.dispatchFileEvent(eventData);
        break;
      
      case BiShareEventType.MESSAGE_RECEIVED:
      case BiShareEventType.MESSAGE_SENT:
        this.dispatchMessageEvent(eventData);
        break;
    }
  }

  /**
   * 分发设备事件
   */
  private dispatchDeviceEvent(eventData: BiShareEventData): void {
    this.deviceListeners.forEach(listener => {
      try {
        switch (eventData.type) {
          case BiShareEventType.DEVICE_DISCOVERED:
            listener.onDeviceDiscovered?.(eventData.data);
            break;
          case BiShareEventType.DEVICE_CONNECTED:
            listener.onDeviceConnected?.(eventData.data);
            break;
          case BiShareEventType.DEVICE_DISCONNECTED:
            listener.onDeviceDisconnected?.(eventData.data);
            break;
          case BiShareEventType.DEVICE_UPDATED:
            listener.onDeviceUpdated?.(eventData.data.oldDevice, eventData.data.newDevice);
            break;
        }
      } catch (error) {
        Log.showError(TAG, '设备事件监听器执行失败:', error);
      }
    });
  }

  /**
   * 分发录制事件
   */
  private dispatchRecordingEvent(eventData: BiShareEventData): void {
    this.recordingListeners.forEach(listener => {
      try {
        switch (eventData.type) {
          case BiShareEventType.RECORDING_STARTED:
            listener.onRecordingStarted?.(eventData.data);
            break;
          case BiShareEventType.RECORDING_STOPPED:
            listener.onRecordingStopped?.(eventData.data);
            break;
          case BiShareEventType.RECORDING_PAUSED:
            listener.onRecordingPaused?.(eventData.data);
            break;
          case BiShareEventType.RECORDING_RESUMED:
            listener.onRecordingResumed?.(eventData.data);
            break;
        }
      } catch (error) {
        Log.showError(TAG, '录制事件监听器执行失败:', error);
      }
    });
  }

  /**
   * 分发网络事件
   */
  private dispatchNetworkEvent(eventData: BiShareEventData): void {
    this.networkListeners.forEach(listener => {
      try {
        switch (eventData.type) {
          case BiShareEventType.NETWORK_CONNECTED:
            listener.onNetworkConnected?.(eventData.data);
            break;
          case BiShareEventType.NETWORK_DISCONNECTED:
            listener.onNetworkDisconnected?.(eventData.data);
            break;
        }
      } catch (error) {
        Log.showError(TAG, '网络事件监听器执行失败:', error);
      }
    });
  }

  /**
   * 分发文件事件
   */
  private dispatchFileEvent(eventData: BiShareEventData): void {
    this.fileListeners.forEach(listener => {
      try {
        switch (eventData.type) {
          case BiShareEventType.FILE_RECEIVED:
            listener.onFileReceived?.(eventData.data.filePath, eventData.data.fromDevice);
            break;
          case BiShareEventType.FILE_SENT:
            listener.onFileSent?.(eventData.data.filePath, eventData.data.toDevice);
            break;
        }
      } catch (error) {
        Log.showError(TAG, '文件事件监听器执行失败:', error);
      }
    });
  }

  /**
   * 分发消息事件
   */
  private dispatchMessageEvent(eventData: BiShareEventData): void {
    this.messageListeners.forEach(listener => {
      try {
        switch (eventData.type) {
          case BiShareEventType.MESSAGE_RECEIVED:
            listener.onMessageReceived?.(eventData.data.message, eventData.data.fromDevice);
            break;
          case BiShareEventType.MESSAGE_SENT:
            listener.onMessageSent?.(eventData.data.message, eventData.data.toDevice);
            break;
        }
      } catch (error) {
        Log.showError(TAG, '消息事件监听器执行失败:', error);
      }
    });
  }
}
