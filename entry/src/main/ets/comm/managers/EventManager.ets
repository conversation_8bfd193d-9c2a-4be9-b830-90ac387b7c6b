import { EventManager as NativeEventManager, EventType } from '@ohos/libbishare_napi';
import {
  BiShareEventType,
  BiShareEventData,
  DeviceEventListener,
  RecordingEventListener,
  NetworkEventListener,
  FileEventListener,
  MessageEventListener
} from '../types/BiShareTypes';
import { Log } from '@ohos/lib_info_sender';

const TAG = 'EventManager';

/**
 * 事件管理器
 * 负责：
 * 1. 统一的事件监听和分发
 * 2. 事件类型转换和路由
 * 3. 事件监听器管理
 */
export class EventManager {
  private nativeEventManager: NativeEventManager;
  private deviceListeners: Set<DeviceEventListener> = new Set();
  private recordingListeners: Set<RecordingEventListener> = new Set();
  private networkListeners: Set<NetworkEventListener> = new Set();
  private fileListeners: Set<FileEventListener> = new Set();
  private messageListeners: Set<MessageEventListener> = new Set();

  constructor() {
    this.nativeEventManager = NativeEventManager.getInstance();
  }

  /**
   * 初始化事件管理器
   */
  async initialize(): Promise<void> {
    Log.showInfo(TAG, '事件管理器初始化');
  }

  /**
   * 释放事件管理器
   */
  async release(): Promise<void> {
    Log.showInfo(TAG, '事件管理器释放');

    // 清理所有监听器
    this.deviceListeners.clear();
    this.recordingListeners.clear();
    this.networkListeners.clear();
    this.fileListeners.clear();
    this.messageListeners.clear();
  }



  /**
   * 添加设备事件监听器
   */
  addDeviceEventListener(listener: DeviceEventListener): void {
    this.deviceListeners.add(listener);
    Log.showInfo(TAG, '添加设备事件监听器');
  }

  /**
   * 移除设备事件监听器
   */
  removeDeviceEventListener(listener: DeviceEventListener): void {
    this.deviceListeners.delete(listener);
    Log.showInfo(TAG, '移除设备事件监听器');
  }

  /**
   * 添加录制事件监听器
   */
  addRecordingEventListener(listener: RecordingEventListener): void {
    this.recordingListeners.add(listener);
  }

  /**
   * 移除录制事件监听器
   */
  removeRecordingEventListener(listener: RecordingEventListener): void {
    this.recordingListeners.delete(listener);
  }

  /**
   * 添加网络事件监听器
   */
  addNetworkEventListener(listener: NetworkEventListener): void {
    this.networkListeners.add(listener);
  }

  /**
   * 移除网络事件监听器
   */
  removeNetworkEventListener(listener: NetworkEventListener): void {
    this.networkListeners.delete(listener);
  }

  /**
   * 添加文件事件监听器
   */
  addFileEventListener(listener: FileEventListener): void {
    this.fileListeners.add(listener);
  }

  /**
   * 移除文件事件监听器
   */
  removeFileEventListener(listener: FileEventListener): void {
    this.fileListeners.delete(listener);
  }

  /**
   * 添加消息事件监听器
   */
  addMessageEventListener(listener: MessageEventListener): void {
    this.messageListeners.add(listener);
  }

  /**
   * 移除消息事件监听器
   */
  removeMessageEventListener(listener: MessageEventListener): void {
    this.messageListeners.delete(listener);
  }

  /**
   * 模拟设备发现事件
   */
  simulateDeviceDiscovered(deviceId: string, deviceName: string): void {
    // 这里可以模拟设备发现事件，用于测试
    Log.showInfo(TAG, `模拟设备发现: ${deviceId} - ${deviceName}`);
  }
}
