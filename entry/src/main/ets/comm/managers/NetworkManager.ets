import { BiShareManager, NetworkType, NetworkInfoOptions } from '@ohos/libbishare_napi';
import { 
  BiShareResult, 
  BiShareError, 
  NetworkInfo, 
  NetworkStatus,
  NetworkEventListener 
} from '../types/BiShareTypes';
import { Log } from '@ohos/lib_info_sender';

const TAG = 'NetworkManager';

/**
 * 网络管理器
 * 负责：
 * 1. 网络连接管理
 * 2. 网络状态监控
 * 3. 网络事件处理
 */
export class NetworkManager {
  private biShareManager: BiShareManager;
  private currentNetworkInfo: NetworkInfo | null = null;
  private eventListeners: Set<NetworkEventListener> = new Set();

  constructor() {
    this.biShareManager = BiShareManager.getInstance();
  }

  /**
   * 初始化网络管理器
   */
  async initialize(): Promise<void> {
    Log.showInfo(TAG, '网络管理器初始化');
    this.currentNetworkInfo = {
      status: NetworkStatus.DISCONNECTED
    };
  }

  /**
   * 释放网络管理器
   */
  async release(): Promise<void> {
    Log.showInfo(TAG, '网络管理器释放');
    this.currentNetworkInfo = null;
    this.eventListeners.clear();
  }

  /**
   * 设置网络信息
   */
  async setNetworkInfo(options: NetworkInfoOptions): Promise<BiShareResult<boolean>> {
    try {
      Log.showInfo(TAG, `设置网络信息: 类型=${options.networkType}, IP=${options.addr}`);

      const result = await this.biShareManager.setNetworkInfo(options);

      if (result) {
        this.currentNetworkInfo = {
          status: NetworkStatus.CONNECTED,
          ipAddress: options.addr,
          macAddress: options.mac
        };

        // 通知监听器
        this.notifyNetworkConnected(this.currentNetworkInfo);

        Log.showInfo(TAG, '网络信息设置成功');
        return { success: true, data: true };
      } else {
        throw new Error('网络信息设置失败');
      }
    } catch (error) {
      Log.showError(TAG, '网络信息设置失败:', error);
      return {
        success: false,
        error: {
          code: 'SET_NETWORK_INFO_FAILED',
          message: `网络信息设置失败: ${error.message}`
        }
      };
    }
  }

  /**
   * 获取当前网络信息
   */
  getCurrentNetworkInfo(): NetworkInfo | null {
    return this.currentNetworkInfo;
  }

  /**
   * 获取网络状态
   */
  getNetworkStatus(): NetworkStatus {
    return this.currentNetworkInfo?.status || NetworkStatus.DISCONNECTED;
  }

  /**
   * 是否已连接网络
   */
  isNetworkConnected(): boolean {
    return this.currentNetworkInfo?.status === NetworkStatus.CONNECTED;
  }

  /**
   * 获取根路径
   */
  async getRootPath(): Promise<BiShareResult<string>> {
    try {
      const rootPath = await this.biShareManager.getRootPath();

      if (rootPath) {
        Log.showInfo(TAG, `获取根路径成功: ${rootPath}`);
        return { success: true, data: rootPath };
      } else {
        throw new Error('获取根路径失败');
      }
    } catch (error) {
      Log.showError(TAG, '获取根路径失败:', error);
      return {
        success: false,
        error: {
          code: 'GET_ROOT_PATH_FAILED',
          message: `获取根路径失败: ${error}`
        }
      };
    }
  }

  /**
   * 获取当前目录
   */
  async getCurrentDirectory(): Promise<BiShareResult<string>> {
    try {
      const currentDir = await this.biShareManager.getCurrentDirector();

      if (currentDir) {
        Log.showInfo(TAG, `获取当前目录成功: ${currentDir}`);
        return { success: true, data: currentDir };
      } else {
        throw new Error('获取当前目录失败');
      }
    } catch (error) {
      Log.showError(TAG, '获取当前目录失败:', error);
      return {
        success: false,
        error: {
          code: 'GET_CURRENT_DIR_FAILED',
          message: `获取当前目录失败: ${error}`
        }
      };
    }
  }

  /**
   * 断开网络连接
   */
  async disconnectNetwork(): Promise<BiShareResult<boolean>> {
    try {
      if (this.currentNetworkInfo?.status === NetworkStatus.DISCONNECTED) {
        Log.showWarn(TAG, '网络已断开');
        return { success: true, data: true };
      }

      Log.showInfo(TAG, '断开网络连接');

      // 这里应该调用原生的断开网络方法
      // const result = await this.biShareManager.disconnect();

      const oldNetworkInfo = this.currentNetworkInfo;
      this.currentNetworkInfo = {
        status: NetworkStatus.DISCONNECTED
      };

      // 通知监听器
      if (oldNetworkInfo) {
        this.notifyNetworkDisconnected(oldNetworkInfo);
      }

      Log.showInfo(TAG, '网络连接断开成功');
      return { success: true, data: true };
    } catch (error) {
      Log.showError(TAG, '网络连接断开失败:', error);
      return {
        success: false,
        error: {
          code: 'DISCONNECT_NETWORK_FAILED',
          message: `网络连接断开失败: ${error.message}`
        }
      };
    }
  }

  /**
   * 检查网络连接状态
   */
  async checkNetworkStatus(): Promise<BiShareResult<NetworkStatus>> {
    try {
      // 这里应该调用原生的网络状态检查方法
      // const status = await this.biShareManager.getNetworkStatus();

      // 模拟网络状态检查
      const status = this.currentNetworkInfo?.status || NetworkStatus.DISCONNECTED;

      Log.showInfo(TAG, `网络状态检查: ${status}`);
      return { success: true, data: status };
    } catch (error) {
      Log.showError(TAG, '网络状态检查失败:', error);
      return {
        success: false,
        error: {
          code: 'CHECK_NETWORK_STATUS_FAILED',
          message: `网络状态检查失败: ${error}`
        }
      };
    }
  }

  /**
   * 添加网络事件监听器
   */
  addNetworkEventListener(listener: NetworkEventListener): void {
    this.eventListeners.add(listener);
  }

  /**
   * 移除网络事件监听器
   */
  removeNetworkEventListener(listener: NetworkEventListener): void {
    this.eventListeners.delete(listener);
  }

  /**
   * 处理网络连接事件
   */
  handleNetworkConnected(networkInfo: NetworkInfo): void {
    this.currentNetworkInfo = {
      status: NetworkStatus.CONNECTED,
      ssid: networkInfo.ssid,
      ipAddress: networkInfo.ipAddress,
      macAddress: networkInfo.macAddress
    };
    this.notifyNetworkConnected(this.currentNetworkInfo);
  }

  /**
   * 处理网络断开事件
   */
  handleNetworkDisconnected(): void {
    const oldNetworkInfo = this.currentNetworkInfo;
    this.currentNetworkInfo = {
      status: NetworkStatus.DISCONNECTED
    };
    
    if (oldNetworkInfo) {
      this.notifyNetworkDisconnected(oldNetworkInfo);
    }
  }

  /**
   * 通知网络连接
   */
  private notifyNetworkConnected(info: NetworkInfo): void {
    this.eventListeners.forEach(listener => {
      try {
        listener.onNetworkConnected?.(info);
      } catch (error) {
        Log.showError(TAG, '网络连接事件通知失败:', error);
      }
    });
  }

  /**
   * 通知网络断开连接
   */
  private notifyNetworkDisconnected(info: NetworkInfo): void {
    this.eventListeners.forEach(listener => {
      try {
        listener.onNetworkDisconnected?.(info);
      } catch (error) {
        Log.showError(TAG, '网络断开连接事件通知失败:', error);
      }
    });
  }
}
