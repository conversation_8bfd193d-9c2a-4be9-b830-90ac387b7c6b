```
bishare/
├── oh-package.json5               # Package manifest
├── src/                           # ArkTS implementation
│   ├── main/                      # Main module code
│   │   ├── ets/                   # ETS source files
│   │   │   ├── core/              # Core implementations
│   │   │   │   ├── BiShareManager.ets    # Main manager
│   │   │   │   ├── DeviceManager.ets     # Device management
│   │   │   │   ├── RecordingManager.ets  # Recording management
│   │   │   │   └── EventManager.ets      # Event management
│   │   │   ├── interfaces/        # Public interfaces
│   │   │   │   ├── BiShareTypes.ets      # Type definitions
│   │   │   │   └── IBiShare.ets          # Interface definition
│   │   │   ├── utils/             # Utilities
│   │   │   │   └── Logger.ets            # Logging utility
│   │   │   └── index.ets          # Entry point
│   │   ├── cpp/                   
│   │   │   ├── include/
│   │   │   │   ├── bishare_callbacks.h              
│   │   │   │   ├── bishare_device.h              
│   │   │   │   ├── bishare_napi.h              
│   │   │   │   ├── bishare_recording.h              
│   │   │   │   ├── bishare_utils.h              
│   │   │   ├── thirdparty/    
│   │   │   │   ├── biservice/              
│   │   │   │       ├── arm64-v8a/              
│   │   │   │           ├── include/              
│   │   │   │               ├── bishare-define.h              
│   │   │   │               ├── bishare-service.h              
│   │   │   │           ├── lib/              
│   │   │   │               ├── libbishare-crypto-plugin.so              
│   │   │   │               ├── libbishare-service.so              
│   │   │   ├── types/      
│   │   │   │   ├── bishare_napi/               
│   │   │   │       ├── index.d.ts               
│   │   │   │       ├── oh-package.json5               
│   │   │   ├── utils/  
│   │   │   │   ├── bishare_utils.cpp               
│   │   │   ├── bishare_callbacks.cpp               
│   │   │   ├── bishare_device.cpp               
│   │   │   ├── bishare_napi.cpp               
│   │   │   ├── bishare_recording.cpp               
│   │   │   ├── CMakeLists.txt               
│   │   └── resources/             # Resources
│   └── test/                      # Unit tests
└── index.ets                      # Package entry point
```