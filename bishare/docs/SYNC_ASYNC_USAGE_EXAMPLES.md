# BiShare 同步/异步双模式使用指南

## 📋 **功能概述**

BiShare NAPI接口现在支持同步和异步两种调用模式：

- **同步模式**: 直接返回结果，适合快速操作
- **异步模式**: 支持Promise和回调，适合耗时操作
- **自动检测**: 根据参数自动选择模式

## 🎯 **使用方式**

### **1. 同步调用（无回调参数）**

```javascript
// 同步调用 - 直接返回结果
try {
    const devices = biShare.getDiscoveredDevices();
    console.log('发现的设备:', devices);
} catch (error) {
    console.error('获取设备失败:', error);
}

// 同步设备发现
try {
    const result = biShare.discoverDevices();
    console.log('设备发现启动:', result);
} catch (error) {
    console.error('设备发现失败:', error);
}
```

### **2. 异步调用 - Promise模式**

```javascript
// Promise模式 - 现代JavaScript推荐方式
async function discoverDevicesAsync() {
    try {
        // 异步设备发现
        const result = await biShare.discoverDevices();
        console.log('设备发现启动成功:', result);
        
        // 异步获取设备列表
        const devices = await biShare.getDiscoveredDevices();
        console.log('发现的设备:', devices);
        
        // 异步录制
        const recordResult = await biShare.startScreenRecord({
            session: 1,
            displayId: 0,
            direction: 0
        });
        console.log('录制启动成功:', recordResult);
        
    } catch (error) {
        console.error('异步操作失败:', error);
    }
}

// 调用异步函数
discoverDevicesAsync();
```

### **3. 异步调用 - 回调模式**

```javascript
// 回调模式 - 传统JavaScript方式
biShare.discoverDevices((error, result) => {
    if (error) {
        console.error('设备发现失败:', error);
        return;
    }
    console.log('设备发现启动成功:', result);
    
    // 获取设备列表
    biShare.getDiscoveredDevices((error, devices) => {
        if (error) {
            console.error('获取设备失败:', error);
            return;
        }
        console.log('发现的设备:', devices);
    });
});

// 录制操作回调
biShare.startScreenRecord({
    session: 1,
    displayId: 0,
    direction: 0
}, (error, result) => {
    if (error) {
        console.error('录制启动失败:', error);
        return;
    }
    console.log('录制启动成功:', result);
});
```

### **4. 混合使用模式**

```javascript
// 在同一个应用中混合使用同步和异步
class BiShareManager {
    constructor() {
        this.isInitialized = false;
    }
    
    // 初始化使用同步模式（快速操作）
    initialize() {
        try {
            const result = biShare.initialize({
                isConsole: true,
                isFile: false,
                priority: biShare.LOG_PRIORITY_INFO
            });
            this.isInitialized = result;
            return result;
        } catch (error) {
            console.error('初始化失败:', error);
            return false;
        }
    }
    
    // 设备发现使用异步模式（可能耗时）
    async discoverDevices() {
        if (!this.isInitialized) {
            throw new Error('服务未初始化');
        }
        
        try {
            await biShare.discoverDevices();
            const devices = await biShare.getDiscoveredDevices();
            return devices;
        } catch (error) {
            console.error('设备发现失败:', error);
            throw error;
        }
    }
    
    // 录制操作使用异步模式（耗时操作）
    async startRecording(options) {
        if (!this.isInitialized) {
            throw new Error('服务未初始化');
        }
        
        return new Promise((resolve, reject) => {
            biShare.startScreenRecord(options, (error, result) => {
                if (error) {
                    reject(new Error(error));
                } else {
                    resolve(result);
                }
            });
        });
    }
    
    // 快速操作使用同步模式
    getCurrentDirectory() {
        try {
            return biShare.getCurrentDirector();
        } catch (error) {
            console.error('获取当前目录失败:', error);
            return null;
        }
    }
}

// 使用示例
const manager = new BiShareManager();

// 同步初始化
if (manager.initialize()) {
    console.log('BiShare初始化成功');
    
    // 异步设备发现
    manager.discoverDevices()
        .then(devices => {
            console.log('发现设备:', devices);
            
            // 异步录制
            return manager.startRecording({
                session: 1,
                displayId: 0,
                direction: 0
            });
        })
        .then(result => {
            console.log('录制启动成功:', result);
        })
        .catch(error => {
            console.error('操作失败:', error);
        });
    
    // 同步获取目录
    const currentDir = manager.getCurrentDirectory();
    console.log('当前目录:', currentDir);
}
```

## 🔧 **性能建议**

### **何时使用同步模式**
- ✅ 快速的配置操作（如设置参数）
- ✅ 简单的查询操作（如获取状态）
- ✅ 初始化和清理操作
- ✅ 不涉及网络或文件I/O的操作

### **何时使用异步模式**
- ✅ 设备发现和连接操作
- ✅ 录制和截图操作
- ✅ 文件传输操作
- ✅ 网络相关操作
- ✅ 任何可能耗时的操作

### **最佳实践**

```javascript
// ✅ 推荐：根据操作特性选择模式
class BiShareBestPractices {
    // 同步：快速配置
    setDeviceInfo(info) {
        return biShare.setDeviceInfo(info);
    }
    
    // 异步：耗时操作
    async discoverAndConnect(deviceId) {
        await biShare.discoverDevices();
        const devices = await biShare.getDiscoveredDevices();
        const targetDevice = devices.find(d => d.id === deviceId);
        
        if (targetDevice) {
            return await biShare.connectDevice(deviceId);
        } else {
            throw new Error('设备未找到');
        }
    }
    
    // 异步：录制操作
    async recordScreen(duration = 10000) {
        try {
            await biShare.startScreenRecord({
                session: 1,
                displayId: 0,
                direction: 0
            });
            
            // 等待指定时间
            await new Promise(resolve => setTimeout(resolve, duration));
            
            return await biShare.stopScreenRecord({
                session: 1,
                displayId: 0,
                direction: 0
            });
        } catch (error) {
            console.error('录制失败:', error);
            throw error;
        }
    }
}
```

## 📊 **性能对比**

| 操作类型 | 同步模式 | 异步模式 | 推荐 |
|----------|----------|----------|------|
| **初始化** | 快速返回 | 额外开销 | 同步 ✅ |
| **设备发现** | 可能阻塞UI | 不阻塞UI | 异步 ✅ |
| **获取配置** | 快速返回 | 额外开销 | 同步 ✅ |
| **录制操作** | 阻塞UI | 不阻塞UI | 异步 ✅ |
| **文件操作** | 可能阻塞 | 不阻塞UI | 异步 ✅ |

## 🎉 **总结**

BiShare的同步/异步双模式支持让您可以：

1. **灵活选择** - 根据操作特性选择最适合的模式
2. **向后兼容** - 现有同步代码无需修改
3. **性能优化** - 耗时操作不阻塞UI线程
4. **开发便利** - 支持现代Promise和传统回调两种风格

选择合适的模式，让您的应用既高效又响应迅速！
