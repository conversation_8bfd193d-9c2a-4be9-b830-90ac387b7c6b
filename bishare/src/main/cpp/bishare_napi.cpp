#include "bishare-define.h"
#include "bishare_callbacks.h"
#include "bishare_logger.h"
#include "bishare_device.h"
#include "bishare_napi.h"
#include "bishare_recording.h"
#include "bishare_utils.h"

#include <napi/native_api.h>
#include <string>
#include <vector>
#include <map>
#include <memory>
#include <unistd.h>
#include <functional>

static constexpr const char *BISHARE_MODULE_NAME = "bishare_napi";

namespace OHOS {
    namespace BiShare {

        // 定义日志标签
        static constexpr const char *NAPI_TAG = "BiShareNapi";

        // 初始化静态成员
        std::unique_ptr<BiShareNapi> BiShareNapi::instance_ = nullptr;
        std::atomic<bool> BiShareNapi::isInitialized_(false);

        // 创建字符串辅助函数
        napi_value CreateStringUtf8(napi_env env, const char *str, size_t length) {
            napi_value result;
            napi_create_string_utf8(env, str, length, &result);
            return result;
        }

        // 创建整数辅助函数
        napi_value CreateInt32(napi_env env, int32_t value) {
            napi_value result;
            napi_create_int32(env, value, &result);
            return result;
        }

        // 异步工作数据的基础结构
        struct AsyncWorkData {
            napi_env env;
            napi_deferred deferred;
            napi_async_work work;
            napi_ref callback;
            bstatus_t result;
            std::string errorMessage;
            void *customData; // 用于存储自定义数据

            // 所有可能的数据字段
            struct {
                // 通用字符串参数
                std::string stringParam1;
                std::string stringParam2;
                std::string stringParam3;

                // 通用整数参数
                int intParam1 = 0;
                int intParam2 = 0;
                int intParam3 = 0;
                int intParam4 = 0;

                // 通用布尔参数
                bool boolParam1 = false;
                bool boolParam2 = false;

                // 通用长整型参数
                long longParam1 = 0;
                long longParam2 = 0;
                long longParam3 = 0;
                long longParam4 = 0;

                // 特殊类型参数
                bool_type_t enableFlag = BOOL_FALSE;
                log_priority_t priority = LOG_INFO;
                network_type_t networkType = NotNetwork;
            } data;

            AsyncWorkData(napi_env e)
                : env(e), deferred(nullptr), work(nullptr), callback(nullptr), result(BS_NOT_INIT), errorMessage(""),
                  customData(nullptr) {}

            ~AsyncWorkData() {
                if (work != nullptr) {
                    napi_delete_async_work(env, work);
                }
                if (callback != nullptr) {
                    napi_delete_reference(env, callback);
                }
            }
        };

        // 用于存储异步回调的结构
        using ExecuteCallback = std::function<void(napi_env, AsyncWorkData *)>;
        using CompleteCallback = std::function<void(napi_env, napi_status, AsyncWorkData *)>;
        using ParseCallback = std::function<bool(napi_env, napi_callback_info, AsyncWorkData **)>;

        // NAPI 模块初始化
        napi_value BiShareNapi::Init(napi_env env, napi_value exports) {
            // 定义常量和方法的属性描述数组
            napi_property_descriptor properties[] = {
                // 版本常量
                {"VERSION", nullptr, nullptr, nullptr, nullptr, CreateStringUtf8(env, VERSION, NAPI_AUTO_LENGTH),
                 napi_default, nullptr},

                // 日志优先级常量
                {"LOG_PRIORITY_EMERG", nullptr, nullptr, nullptr, nullptr, CreateInt32(env, LOG_EMERG), napi_default,
                 nullptr},
                {"LOG_PRIORITY_FATAL", nullptr, nullptr, nullptr, nullptr, CreateInt32(env, LOG_FATAL), napi_default,
                 nullptr},
                {"LOG_PRIORITY_ALERT", nullptr, nullptr, nullptr, nullptr, CreateInt32(env, LOG_ALERT), napi_default,
                 nullptr},
                {"LOG_PRIORITY_CRIT", nullptr, nullptr, nullptr, nullptr, CreateInt32(env, LOG_CRIT), napi_default,
                 nullptr},
                {"LOG_PRIORITY_ERROR", nullptr, nullptr, nullptr, nullptr, CreateInt32(env, LOG_ERROR), napi_default,
                 nullptr},
                {"LOG_PRIORITY_WARN", nullptr, nullptr, nullptr, nullptr, CreateInt32(env, LOG_WARN), napi_default,
                 nullptr},
                {"LOG_PRIORITY_NOTICE", nullptr, nullptr, nullptr, nullptr, CreateInt32(env, LOG_NOTICE), napi_default,
                 nullptr},
                {"LOG_PRIORITY_INFO", nullptr, nullptr, nullptr, nullptr, CreateInt32(env, LOG_INFO), napi_default,
                 nullptr},
                {"LOG_PRIORITY_DEBUG", nullptr, nullptr, nullptr, nullptr, CreateInt32(env, LOG_DEBUG), napi_default,
                 nullptr},

                // 事件类型常量
                {"EVENT_DEVICE_INFO", nullptr, nullptr, nullptr, nullptr, CreateInt32(env, EVENT_DEVICE_INFO),
                 napi_default, nullptr},
                {"EVENT_DEVICE_STATUS", nullptr, nullptr, nullptr, nullptr, CreateInt32(env, EVENT_DEVICE_STATUS),
                 napi_default, nullptr},
                {"EVENT_CONNECT_STATUS", nullptr, nullptr, nullptr, nullptr, CreateInt32(env, EVENT_CONNECT_STATUS),
                 napi_default, nullptr},
                {"EVENT_KEY_VALUE", nullptr, nullptr, nullptr, nullptr, CreateInt32(env, EVENT_KEY_VALUE), napi_default,
                 nullptr},
                {"EVENT_ERROR_INFO", nullptr, nullptr, nullptr, nullptr, CreateInt32(env, EVENT_ERROR_INFO),
                 napi_default, nullptr},
                {"EVENT_MEDIA_CENTER", nullptr, nullptr, nullptr, nullptr, CreateInt32(env, EVENT_MEDIA_CENTER),
                 napi_default, nullptr},
                {"EVENT_SCREEN_RECORD", nullptr, nullptr, nullptr, nullptr, CreateInt32(env, EVENT_SCREEN_RECORD),
                 napi_default, nullptr},
                {"EVENT_FILE_ACTION", nullptr, nullptr, nullptr, nullptr, CreateInt32(env, EVENT_FILE_ACTION),
                 napi_default, nullptr},
                {"EVENT_WFD_EVENT", nullptr, nullptr, nullptr, nullptr, CreateInt32(env, EVENT_WFD_EVENT), napi_default,
                 nullptr},
                {"EVENT_ABILITY_ACTION", nullptr, nullptr, nullptr, nullptr, CreateInt32(env, EVENT_ABILITY_ACTION),
                 napi_default, nullptr},
                {"EVENT_DEVICE_INFO_LIST", nullptr, nullptr, nullptr, nullptr, CreateInt32(env, EVENT_DEVICE_INFO_LIST),
                 napi_default, nullptr},
                {"EVENT_WINDOWS_INFO_LIST", nullptr, nullptr, nullptr, nullptr,
                 CreateInt32(env, EVENT_WINDOWS_INFO_LIST), napi_default, nullptr},
                {"EVENT_MONITORS_INFO_LIST", nullptr, nullptr, nullptr, nullptr,
                 CreateInt32(env, EVENT_MONITORS_INFO_LIST), napi_default, nullptr},
                {"EVENT_LOG_INFO", nullptr, nullptr, nullptr, nullptr, CreateInt32(env, EVENT_LOG_INFO), napi_default,
                 nullptr},

                // 方向常量
                {"DIRECTION_NULL", nullptr, nullptr, nullptr, nullptr, CreateInt32(env, DIR_NULL), napi_default,
                 nullptr},
                {"DIRECTION_SEND", nullptr, nullptr, nullptr, nullptr, CreateInt32(env, DIR_SEND), napi_default,
                 nullptr},
                {"DIRECTION_RECV", nullptr, nullptr, nullptr, nullptr, CreateInt32(env, DIR_RECV), napi_default,
                 nullptr},

                // 缓冲区类型常量
                {"BUFFER_TYPE_VIDEO", nullptr, nullptr, nullptr, nullptr, CreateInt32(env, TYPE_VIDEO), napi_default,
                 nullptr},
                {"BUFFER_TYPE_AUDIO", nullptr, nullptr, nullptr, nullptr, CreateInt32(env, TYPE_AUDIO), napi_default,
                 nullptr},

                // API 方法定义
                {"initialize", nullptr, Initialize, nullptr, nullptr, nullptr, napi_default, nullptr},
                {"release", nullptr, Release, nullptr, nullptr, nullptr, napi_default, nullptr},
                {"discoverDevices", nullptr, DiscoverDevices, nullptr, nullptr, nullptr, napi_default, nullptr},
                {"clearDiscoveredDevices", nullptr, ClearDiscoveredDevices, nullptr, nullptr, nullptr, napi_default,
                 nullptr},
                {"getDiscoveredDevices", nullptr, GetDiscoveredDevices, nullptr, nullptr, nullptr, napi_default,
                 nullptr},
                {"startScreenRecord", nullptr, StartScreenRecord, nullptr, nullptr, nullptr, napi_default, nullptr},
                {"stopScreenRecord", nullptr, StopScreenRecord, nullptr, nullptr, nullptr, napi_default, nullptr},
                {"startCapture", nullptr, StartCapture, nullptr, nullptr, nullptr, napi_default, nullptr},
                {"setDeviceInfo", nullptr, SetDeviceInfo, nullptr, nullptr, nullptr, napi_default, nullptr},
                {"setDeviceModel", nullptr, SetDeviceModel, nullptr, nullptr, nullptr, napi_default, nullptr},
                {"getDeviceModel", nullptr, GetDeviceModel, nullptr, nullptr, nullptr, napi_default, nullptr},
                {"resetDeviceModel", nullptr, ResetDeviceModel, nullptr, nullptr, nullptr, napi_default, nullptr},
                {"getRootPath", nullptr, GetRootPath, nullptr, nullptr, nullptr, napi_default, nullptr},
                {"getCurrentDirector", nullptr, GetCurrentDirector, nullptr, nullptr, nullptr, napi_default, nullptr},
                {"setSize", nullptr, SetSize, nullptr, nullptr, nullptr, napi_default, nullptr},
                {"setDefaultAudioOutputDevice", nullptr, SetDefaultAudioOutputDevice, nullptr, nullptr, nullptr,
                 napi_default, nullptr},
                {"screenshot", nullptr, Screenshot, nullptr, nullptr, nullptr, napi_default, nullptr},
                {"findRemoteDevice", nullptr, FindRemoteDevice, nullptr, nullptr, nullptr, napi_default, nullptr},
                {"setNetworkInfo", nullptr, SetNetworkInfo, nullptr, nullptr, nullptr, napi_default, nullptr},
    

                // 事件处理方法
                {"on", nullptr, On, nullptr, nullptr, nullptr, napi_default, nullptr},
                {"off", nullptr, Off, nullptr, nullptr, nullptr, napi_default, nullptr},
                {"once", nullptr, Once, nullptr, nullptr, nullptr, napi_default, nullptr},
            };

            // 创建 NAPI 类
            napi_define_properties(env, exports, sizeof(properties) / sizeof(properties[0]), properties);

            // 如果未创建，则创建单例实例
            if (instance_ == nullptr) {
                instance_ = std::unique_ptr<BiShareNapi>(new BiShareNapi());
            }

            return exports;
        }

        // 构造函数
        BiShareNapi::BiShareNapi() {
            // 初始化管理器
            callbacks_ = std::make_shared<BiShareCallbacks>(std::weak_ptr<BiShareNapi>());
            deviceManager_ = std::make_shared<BiShareDeviceManager>();
            recordManager_ = std::make_shared<BiShareRecordManager>();

            // 初始化互斥锁
            pthread_mutex_init(&eventCallbacksMutex_, nullptr);
        }

        // 析构函数
        BiShareNapi::~BiShareNapi() {
            // 清理资源
            if (isInitialized_.load()) {
                bishare_service_release();
                isInitialized_.store(false);
            }

            // 销毁互斥锁
            pthread_mutex_destroy(&eventCallbacksMutex_);
        }

        // 统一的异步工作处理函数 - 所有异步方法的核心
        static napi_value CreateAsyncWork(napi_env env, napi_callback_info info, const std::string &workName,
                                          ExecuteCallback executeCallback, CompleteCallback completeCallback,
                                          ParseCallback parseCallback) {
            // 解析参数并创建工作数据
            AsyncWorkData *workData = nullptr;
            if (!parseCallback(env, info, &workData) || !workData) {
                return nullptr;
            }

            // 如果没有提供回调，则创建 Promise
            napi_value promise = nullptr;
            if (workData->callback == nullptr) {
                napi_create_promise(env, &workData->deferred, &promise);
            }

            // 创建资源名称
            napi_value resourceName;
            napi_create_string_utf8(env, workName.c_str(), NAPI_AUTO_LENGTH, &resourceName);

            // 存储回调函数的结构体
            struct WorkCallbacks {
                ExecuteCallback execute;
                CompleteCallback complete;
                WorkCallbacks(ExecuteCallback exec, CompleteCallback comp) : execute(exec), complete(comp) {}
            };

            // 在堆上创建回调对象以确保生命周期
            auto callbacks = new WorkCallbacks(executeCallback, completeCallback);

            // 创建异步工作
            napi_status status = napi_create_async_work(
                env, nullptr, resourceName,
                // 执行回调
                [](napi_env env, void *data) {
                    auto workData = static_cast<AsyncWorkData *>(data);
                    // 获取存储在workData中的回调
                    void *callbacksPtr = workData->customData; // 使用自定义字段存储回调对象
                    auto callbacks = static_cast<WorkCallbacks *>(callbacksPtr);
                    callbacks->execute(env, workData);
                },
                // 完成回调
                [](napi_env env, napi_status status, void *data) {
                    auto workData = static_cast<AsyncWorkData *>(data);
                    // 获取存储在workData中的回调
                    void *callbacksPtr = workData->customData; // 使用自定义字段存储回调对象
                    auto callbacks = static_cast<WorkCallbacks *>(callbacksPtr);
                    callbacks->complete(env, status, workData);
                    delete callbacks; // 清理回调结构
                },
                workData, &workData->work);

            // 存储回调数据
            workData->customData = callbacks; // 使用自定义字段存储回调对象

            if (status != napi_ok) {
                delete callbacks;
                delete workData;
                napi_throw_error(env, nullptr, "Failed to create async work");
                return nullptr;
            }

            // 队列化异步工作
            status = napi_queue_async_work(env, workData->work);
            if (status != napi_ok) {
                delete callbacks;
                delete workData;
                napi_throw_error(env, nullptr, "Failed to queue async work");
                return nullptr;
            }

            // 返回 Promise（如果创建）或 undefined
            if (promise) {
                return promise;
            } else {
                napi_value undefined;
                napi_get_undefined(env, &undefined);
                return undefined;
            }
        }

        // 标准的结果处理函数
        static void HandleResult(napi_env env, AsyncWorkData *workData, napi_value result) {
            // 解决 Promise 或调用回调
            if (workData->deferred) {
                if (workData->result == BS_OK) {
                    napi_resolve_deferred(env, workData->deferred, result);
                } else {
                    napi_reject_deferred(env, workData->deferred, result);
                }
            } else if (workData->callback) {
                // 准备回调参数
                napi_value callbackArgs[2];
                if (workData->result == BS_OK) {
                    napi_get_null(env, &callbackArgs[0]);
                    callbackArgs[1] = result;
                } else {
                    callbackArgs[0] = result;
                    napi_get_undefined(env, &callbackArgs[1]);
                }

                // a调用 JavaScript 回调
                napi_value global, callbackResult, callback;
                napi_get_global(env, &global);
                napi_get_reference_value(env, workData->callback, &callback);
                napi_call_function(env, global, callback, 2, callbackArgs, &callbackResult);
            }
        }

        // 标准的完成回调 - 返回布尔值结果
        static void StandardCompleteCallback(napi_env env, napi_status status, AsyncWorkData *workData) {
            // 创建基于结果的返回值
            napi_value result;
            if (workData->result == BS_OK) {
                napi_get_boolean(env, true, &result);
            } else {
                // 创建错误对象
                napi_value error, message;
                napi_create_string_utf8(env, workData->errorMessage.c_str(), NAPI_AUTO_LENGTH, &message);
                napi_create_error(env, nullptr, message, &error);
                result = error;
            }

            HandleResult(env, workData, result);
            delete workData; // 清理资源
        }

        // 字符串结果完成回调 - 用于返回字符串的方法
        static void StringCompleteCallback(napi_env env, napi_status status, AsyncWorkData *workData) {
            // 创建基于结果的返回值
            napi_value result;
            if (workData->result == BS_OK) {
                napi_create_string_utf8(env, workData->data.stringParam1.c_str(), NAPI_AUTO_LENGTH, &result);
            } else {
                // 创建错误对象
                napi_value error, message;
                napi_create_string_utf8(env, workData->errorMessage.c_str(), NAPI_AUTO_LENGTH, &message);
                napi_create_error(env, nullptr, message, &error);
                result = error;
            }

            HandleResult(env, workData, result);
            delete workData; // 清理资源
        }

        // Initialize 方法实现
        napi_value BiShareNapi::Initialize(napi_env env, napi_callback_info info) {
            return CreateAsyncWork(
                env, info, "Initialize",
                // 执行回调
                [](napi_env env, AsyncWorkData *workData) {
                    // 检查是否已初始化
                    if (BiShareNapi::isInitialized_.load()) {
                        BiShareLogger::Info(NAPI_TAG, "已经初始化过了...");
                        workData->result = BS_OK;
                        return;
                    }

                    // 转换参数
                    bool_type_t isConsole = workData->data.boolParam1 ? BOOL_TRUE : BOOL_FALSE;
                    bool_type_t isFile = workData->data.boolParam2 ? BOOL_TRUE : BOOL_FALSE;

                    const char *isConsoleString = (isConsole == BOOL_TRUE) ? "TRUE" : "FALSE";
                    BiShareLogger::Info(NAPI_TAG, "Value of isConsole (string): %s", isConsoleString);
                    BiShareLogger::Info(NAPI_TAG, "Value of isFile (integer): %d", static_cast<int>(isFile));
                    
                    BiShareLogger::Info(NAPI_TAG, "开始进行初始化...");
                    // 调用原生函数
                    workData->result = bishare_service_init(isConsole, isFile, workData->data.stringParam1.c_str(),
                                                            workData->data.priority);

                    BiShareLogger::Info(NAPI_TAG, "初始化是否成功 (integer): %d",
                                        static_cast<int>(workData->result));

                    // 如果初始化成功，注册回调
                    if (workData->result == BS_OK) {
                        // 设置初始化标志
                        BiShareNapi::isInitialized_.store(true);

                        // 注册回调
                        bishare_service_register_event_callback(BiShareCallbacks::OnEventCallback);
                        bishare_service_register_packet_callback(BiShareCallbacks::OnPacketCallback);
                    } else {
                        workData->errorMessage = std::string("Failed to initialize BiShare service: ") +
                                                 std::string(err2str(workData->result));
                    }
                },
                // 完成回调
                StandardCompleteCallback,
                // 参数解析
                [](napi_env env, napi_callback_info info, AsyncWorkData **workData) {
                    // 创建工作数据
                    *workData = new AsyncWorkData(env);

                    // 获取回调信息
                    size_t argc = 4;
                    napi_value argv[5]; // 包括回调
                    napi_value thisArg;
                    void *data;
                    napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);

                    // 检查所需的参数
                    if (argc < 3) {
                        napi_throw_error(env, "EINVAL", "Wrong number of arguments, expected at least 3");
                        return false;
                    }

                    // 解析 isConsole（布尔值）
                    bool isConsole;
                    napi_get_value_bool(env, argv[0], &isConsole);
                    (*workData)->data.boolParam1 = isConsole;

                    // 解析 isFile（布尔值）
                    bool isFile;
                    napi_get_value_bool(env, argv[1], &isFile);
                    (*workData)->data.boolParam2 = isFile;

                    // 解析 filePath（字符串）
                    size_t filePathLength;
                    napi_get_value_string_utf8(env, argv[2], nullptr, 0, &filePathLength);
                    if (filePathLength > 0) {
                        std::vector<char> filePathBuffer(filePathLength + 1);
                        napi_get_value_string_utf8(env, argv[2], filePathBuffer.data(), filePathLength + 1,
                                                   &filePathLength);
                        (*workData)->data.stringParam1 = std::string(filePathBuffer.data(), filePathLength);
                    } else {
                        (*workData)->data.stringParam1 = "";
                    }

                    // 解析 priority（数字），如果提供
                    if (argc > 3) {
                        napi_valuetype valueType;
                        napi_typeof(env, argv[3], &valueType);

                        if (valueType == napi_number) {
                            int32_t priority;
                            napi_get_value_int32(env, argv[3], &priority);
                            (*workData)->data.priority = static_cast<log_priority_t>(priority);
                        } else if (valueType == napi_function) {
                            // 这是回调而不是优先级
                            napi_create_reference(env, argv[3], 1, &(*workData)->callback);
                            (*workData)->data.priority = LOG_INFO; // 默认
                        } else {
                            (*workData)->data.priority = LOG_INFO; // 默认
                        }
                    } else {
                        (*workData)->data.priority = LOG_INFO; // 默认
                    }

                    // 解析回调（如果提供）
                    if (argc > 4) {
                        napi_valuetype valueType;
                        napi_typeof(env, argv[4], &valueType);

                        if (valueType == napi_function) {
                            napi_create_reference(env, argv[4], 1, &(*workData)->callback);
                        }
                    }

                    return true;
                });
        }

        // Release 方法实现
        napi_value BiShareNapi::Release(napi_env env, napi_callback_info info) {
            return CreateAsyncWork(
                env, info, "Release",
                // 执行回调
                [](napi_env env, AsyncWorkData *workData) {
                    // 检查是否已初始化
                    if (!BiShareNapi::isInitialized_.load()) {
                        workData->result = BS_NOT_INIT;
                        workData->errorMessage = "BiShare service is not initialized";
                        return;
                    }

                    // 调用原生函数
                    workData->result = bishare_service_release();

                    // 设置初始化标志
                    if (workData->result == BS_OK) {
                        BiShareNapi::isInitialized_.store(false);
                    } else {
                        workData->errorMessage =
                            std::string("Failed to release BiShare service: ") + std::string(err2str(workData->result));
                    }
                },
                // 完成回调
                StandardCompleteCallback,
                // 参数解析
                [](napi_env env, napi_callback_info info, AsyncWorkData **workData) {
                    // 创建工作数据
                    *workData = new AsyncWorkData(env);

                    // 获取回调信息
                    size_t argc = 1;
                    napi_value argv[1];
                    napi_value thisArg;
                    void *data;
                    napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);

                    // 解析回调（如果提供）
                    if (argc > 0) {
                        napi_valuetype valueType;
                        napi_typeof(env, argv[0], &valueType);

                        if (valueType == napi_function) {
                            napi_create_reference(env, argv[0], 1, &(*workData)->callback);
                        }
                    }

                    return true;
                });
        }

        // DiscoverDevices 方法实现
        napi_value BiShareNapi::DiscoverDevices(napi_env env, napi_callback_info info) {
            return CreateAsyncWork(
                env, info, "DiscoverDevices",
                // 执行回调
                [](napi_env env, AsyncWorkData *workData) {
                    // 检查是否已初始化
                    if (!BiShareNapi::isInitialized_.load()) {
                        workData->result = BS_NOT_INIT;
                        workData->errorMessage = "BiShare service is not initialized";
                        return;
                    }

                    // 调用原生函数
                    workData->result = bishare_service_discovery_device();
                    BiShareLogger::Info(NAPI_TAG, "DiscoverDevices (integer): %d", static_cast<int>(workData->result));

                    if (workData->result != BS_OK) {
                        workData->errorMessage =
                            std::string("Failed to discover devices: ") + std::string(err2str(workData->result));
                    }
                },
                // 完成回调
                StandardCompleteCallback,
                // 参数解析
                [](napi_env env, napi_callback_info info, AsyncWorkData **workData) {
                    // 创建工作数据
                    *workData = new AsyncWorkData(env);

                    // 获取回调信息
                    size_t argc = 1;
                    napi_value argv[1];
                    napi_value thisArg;
                    void *data;
                    napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);

                    // 解析回调（如果提供）
                    if (argc > 0) {
                        napi_valuetype valueType;
                        napi_typeof(env, argv[0], &valueType);

                        if (valueType == napi_function) {
                            napi_create_reference(env, argv[0], 1, &(*workData)->callback);
                        }
                    }

                    return true;
                });
        }

        // ClearDiscoveredDevices 方法实现
        napi_value BiShareNapi::ClearDiscoveredDevices(napi_env env, napi_callback_info info) {
            return CreateAsyncWork(
                env, info, "ClearDiscoveredDevices",
                // 执行回调
                [](napi_env env, AsyncWorkData *workData) {
                    // 检查是否已初始化
                    if (!BiShareNapi::isInitialized_.load()) {
                        workData->result = BS_NOT_INIT;
                        workData->errorMessage = "BiShare service is not initialized";
                        return;
                    }

                    // 调用原生函数
                    workData->result = bishare_service_clear_discovery_device();

                    if (workData->result != BS_OK) {
                        workData->errorMessage = std::string("Failed to clear discovered devices: ") +
                                                 std::string(err2str(workData->result));
                    }
                },
                // 完成回调
                StandardCompleteCallback,
                // 参数解析
                [](napi_env env, napi_callback_info info, AsyncWorkData **workData) {
                    // 创建工作数据
                    *workData = new AsyncWorkData(env);

                    // 获取回调信息
                    size_t argc = 1;
                    napi_value argv[1];
                    napi_value thisArg;
                    void *data;
                    napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);

                    // 解析回调（如果提供）
                    if (argc > 0) {
                        napi_valuetype valueType;
                        napi_typeof(env, argv[0], &valueType);

                        if (valueType == napi_function) {
                            napi_create_reference(env, argv[0], 1, &(*workData)->callback);
                        }
                    }

                    return true;
                });
        }

        // GetDiscoveredDevices 方法实现
        napi_value BiShareNapi::GetDiscoveredDevices(napi_env env, napi_callback_info info) {
            return CreateAsyncWork(
                env, info, "GetDiscoveredDevices",
                // 执行回调
                [](napi_env env, AsyncWorkData *workData) {
                    // 检查是否已初始化
                    if (!BiShareNapi::isInitialized_.load()) {
                        workData->result = BS_NOT_INIT;
                        workData->errorMessage = "BiShare service is not initialized";
                        return;
                    }

                    // 调用原生函数以触发设备列表回调
                    workData->result = bishare_service_get_discovery_device();
                    BiShareLogger::Info(NAPI_TAG, "GetDiscoveredDevices (integer): %d", static_cast<int>(workData->result));

                    if (workData->result != BS_OK) {
                        workData->errorMessage =
                            std::string("Failed to get discovered devices: ") + std::string(err2str(workData->result));
                    }
                },
                // 完成回调
                StandardCompleteCallback,
                // 参数解析
                [](napi_env env, napi_callback_info info, AsyncWorkData **workData) {
                    // 创建工作数据
                    *workData = new AsyncWorkData(env);

                    // 获取回调信息
                    size_t argc = 1;
                    napi_value argv[1];
                    napi_value thisArg;
                    void *data;
                    napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);

                    // 解析回调（如果提供）
                    if (argc > 0) {
                        napi_valuetype valueType;
                        napi_typeof(env, argv[0], &valueType);

                        if (valueType == napi_function) {
                            napi_create_reference(env, argv[0], 1, &(*workData)->callback);
                        }
                    }

                    return true;
                });
        }

        // StartScreenRecord 方法实现
        napi_value BiShareNapi::StartScreenRecord(napi_env env, napi_callback_info info) {
            return CreateAsyncWork(
                env, info, "StartScreenRecord",
                // 执行回调
                [](napi_env env, AsyncWorkData *workData) {
                    // 检查是否已初始化
                    if (!BiShareNapi::isInitialized_.load()) {
                        workData->result = BS_NOT_INIT;
                        workData->errorMessage = "BiShare service is not initialized";
                        return;
                    }

                    // 调用原生函数
                    workData->result = bishare_service_start_screen_record(
                        workData->data.intParam1, workData->data.intParam2, workData->data.intParam3);

                    if (workData->result != BS_OK) {
                        workData->errorMessage =
                            std::string("Failed to start screen recording: ") + std::string(err2str(workData->result));
                    }
                },
                // 完成回调
                StandardCompleteCallback,
                // 参数解析
                [](napi_env env, napi_callback_info info, AsyncWorkData **workData) {
                    // 创建工作数据
                    *workData = new AsyncWorkData(env);

                    // 获取回调信息
                    size_t argc = 4;
                    napi_value argv[4];
                    napi_value thisArg;
                    void *data;
                    napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);

                    // 检查所需的参数
                    if (argc < 3) {
                        napi_throw_error(env, "EINVAL", "Wrong number of arguments, expected at least 3");
                        return false;
                    }

                    // 解析 session（数字）
                    int32_t session;
                    napi_get_value_int32(env, argv[0], &session);
                    (*workData)->data.intParam1 = session;

                    // 解析 displayId（数字）
                    int32_t displayId;
                    napi_get_value_int32(env, argv[1], &displayId);
                    (*workData)->data.intParam2 = displayId;

                    // 解析 direction（数字）
                    int32_t direction;
                    napi_get_value_int32(env, argv[2], &direction);
                    (*workData)->data.intParam3 = direction;

                    // 解析回调（如果提供）
                    if (argc > 3) {
                        napi_valuetype valueType;
                        napi_typeof(env, argv[3], &valueType);

                        if (valueType == napi_function) {
                            napi_create_reference(env, argv[3], 1, &(*workData)->callback);
                        }
                    }

                    return true;
                });
        }

        // StopScreenRecord 方法实现
        napi_value BiShareNapi::StopScreenRecord(napi_env env, napi_callback_info info) {
            return CreateAsyncWork(
                env, info, "StopScreenRecord",
                // 执行回调
                [](napi_env env, AsyncWorkData *workData) {
                    // 检查是否已初始化
                    if (!BiShareNapi::isInitialized_.load()) {
                        workData->result = BS_NOT_INIT;
                        workData->errorMessage = "BiShare service is not initialized";
                        return;
                    }

                    // 调用原生函数
                    workData->result = bishare_service_stop_screen_record(
                        workData->data.intParam1, workData->data.intParam2, workData->data.intParam3);

                    if (workData->result != BS_OK) {
                        workData->errorMessage =
                            std::string("Failed to stop screen recording: ") + std::string(err2str(workData->result));
                    }
                },
                // 完成回调
                StandardCompleteCallback,
                // 参数解析
                [](napi_env env, napi_callback_info info, AsyncWorkData **workData) {
                    // 创建工作数据
                    *workData = new AsyncWorkData(env);

                    // 获取回调信息
                    size_t argc = 4;
                    napi_value argv[4];
                    napi_value thisArg;
                    void *data;
                    napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);

                    // 检查所需的参数
                    if (argc < 3) {
                        napi_throw_error(env, "EINVAL", "Wrong number of arguments, expected at least 3");
                        return false;
                    }

                    // 解析 session（数字）
                    int32_t session;
                    napi_get_value_int32(env, argv[0], &session);
                    (*workData)->data.intParam1 = session;

                    // 解析 displayId（数字）
                    int32_t displayId;
                    napi_get_value_int32(env, argv[1], &displayId);
                    (*workData)->data.intParam2 = displayId;

                    // 解析 direction（数字）
                    int32_t direction;
                    napi_get_value_int32(env, argv[2], &direction);
                    (*workData)->data.intParam3 = direction;

                    // 解析回调（如果提供）
                    if (argc > 3) {
                        napi_valuetype valueType;
                        napi_typeof(env, argv[3], &valueType);

                        if (valueType == napi_function) {
                            napi_create_reference(env, argv[3], 1, &(*workData)->callback);
                        }
                    }

                    return true;
                });
        }

        // StartCapture 方法实现
        napi_value BiShareNapi::StartCapture(napi_env env, napi_callback_info info) {
            return CreateAsyncWork(
                env, info, "StartCapture",
                // 执行回调
                [](napi_env env, AsyncWorkData *workData) {
                    // 检查是否已初始化
                    if (!BiShareNapi::isInitialized_.load()) {
                        workData->result = BS_NOT_INIT;
                        workData->errorMessage = "BiShare service is not initialized";
                        return;
                    }

                    // 调用原生函数
                    workData->result = bishare_service_start_capture();

                    if (workData->result != BS_OK) {
                        workData->errorMessage =
                            std::string("Failed to start capture: ") + std::string(err2str(workData->result));
                    }
                },
                // 完成回调
                StandardCompleteCallback,
                // 参数解析
                [](napi_env env, napi_callback_info info, AsyncWorkData **workData) {
                    // 创建工作数据
                    *workData = new AsyncWorkData(env);

                    // 获取回调信息
                    size_t argc = 1;
                    napi_value argv[1];
                    napi_value thisArg;
                    void *data;
                    napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);

                    // 解析回调（如果提供）
                    if (argc > 0) {
                        napi_valuetype valueType;
                        napi_typeof(env, argv[0], &valueType);

                        if (valueType == napi_function) {
                            napi_create_reference(env, argv[0], 1, &(*workData)->callback);
                        }
                    }

                    return true;
                });
        }

        // SetDeviceInfo 方法实现
        napi_value BiShareNapi::SetDeviceInfo(napi_env env, napi_callback_info info) {
            return CreateAsyncWork(
                env, info, "SetDeviceInfo",
                // 执行回调
                [](napi_env env, AsyncWorkData *workData) {
                    // 检查是否已初始化
                    if (!BiShareNapi::isInitialized_.load()) {
                        workData->result = BS_NOT_INIT;
                        workData->errorMessage = "BiShare service is not initialized";
                        return;
                    }

                    // 调用原生函数
                    workData->result = bishare_service_set_device_info(workData->data.stringParam1.c_str(),
                                                                       workData->data.stringParam2.c_str());

                    if (workData->result != BS_OK) {
                        workData->errorMessage =
                            std::string("Failed to set device info: ") + std::string(err2str(workData->result));
                    }
                },
                // 完成回调
                StandardCompleteCallback,
                // 参数解析
                [](napi_env env, napi_callback_info info, AsyncWorkData **workData) {
                    // 创建工作数据
                    *workData = new AsyncWorkData(env);

                    // 获取回调信息
                    size_t argc = 3;
                    napi_value argv[3];
                    napi_value thisArg;
                    void *data;
                    napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);

                    // 检查所需的参数
                    if (argc < 2) {
                        napi_throw_error(env, "EINVAL", "Wrong number of arguments, expected at least 2");
                        return false;
                    }

                    // 解析 deviceName（字符串）
                    size_t deviceNameLength;
                    napi_get_value_string_utf8(env, argv[0], nullptr, 0, &deviceNameLength);
                    if (deviceNameLength > 0) {
                        std::vector<char> deviceNameBuffer(deviceNameLength + 1);
                        napi_get_value_string_utf8(env, argv[0], deviceNameBuffer.data(), deviceNameLength + 1,
                                                   &deviceNameLength);
                        (*workData)->data.stringParam1 = std::string(deviceNameBuffer.data(), deviceNameLength);
                    } else {
                        (*workData)->data.stringParam1 = "";
                    }

                    // 解析 devicePassword（字符串）
                    size_t devicePasswordLength;
                    napi_get_value_string_utf8(env, argv[1], nullptr, 0, &devicePasswordLength);
                    if (devicePasswordLength > 0) {
                        std::vector<char> devicePasswordBuffer(devicePasswordLength + 1);
                        napi_get_value_string_utf8(env, argv[1], devicePasswordBuffer.data(), devicePasswordLength + 1,
                                                   &devicePasswordLength);
                        (*workData)->data.stringParam2 = std::string(devicePasswordBuffer.data(), devicePasswordLength);
                    } else {
                        (*workData)->data.stringParam2 = "";
                    }

                    // 解析回调（如果提供）
                    if (argc > 2) {
                        napi_valuetype valueType;
                        napi_typeof(env, argv[2], &valueType);

                        if (valueType == napi_function) {
                            napi_create_reference(env, argv[2], 1, &(*workData)->callback);
                        }
                    }

                    return true;
                });
        }

        // SetDeviceModel 方法实现
        napi_value BiShareNapi::SetDeviceModel(napi_env env, napi_callback_info info) {
            return CreateAsyncWork(
                env, info, "SetDeviceModel",
                // 执行回调
                [](napi_env env, AsyncWorkData *workData) {
                    // 检查是否已初始化
                    if (!BiShareNapi::isInitialized_.load()) {
                        workData->result = BS_NOT_INIT;
                        workData->errorMessage = "BiShare service is not initialized";
                        return;
                    }

                    // 调用原生函数
                    workData->result = bishare_service_set_device_model(workData->data.stringParam1.c_str());

                    if (workData->result != BS_OK) {
                        workData->errorMessage =
                            std::string("Failed to set device model: ") + std::string(err2str(workData->result));
                    }
                },
                // 完成回调
                StandardCompleteCallback,
                // 参数解析
                [](napi_env env, napi_callback_info info, AsyncWorkData **workData) {
                    // 创建工作数据
                    *workData = new AsyncWorkData(env);

                    // 获取回调信息
                    size_t argc = 2;
                    napi_value argv[2];
                    napi_value thisArg;
                    void *data;
                    napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);

                    // 检查所需的参数
                    if (argc < 1) {
                        napi_throw_error(env, "EINVAL", "Wrong number of arguments, expected at least 1");
                        return false;
                    }

                    // 解析 deviceModel（字符串）
                    size_t deviceModelLength;
                    napi_get_value_string_utf8(env, argv[0], nullptr, 0, &deviceModelLength);
                    if (deviceModelLength > 0) {
                        std::vector<char> deviceModelBuffer(deviceModelLength + 1);
                        napi_get_value_string_utf8(env, argv[0], deviceModelBuffer.data(), deviceModelLength + 1,
                                                   &deviceModelLength);
                        (*workData)->data.stringParam1 = std::string(deviceModelBuffer.data(), deviceModelLength);
                    } else {
                        (*workData)->data.stringParam1 = "";
                    }

                    // 解析回调（如果提供）
                    if (argc > 1) {
                        napi_valuetype valueType;
                        napi_typeof(env, argv[1], &valueType);

                        if (valueType == napi_function) {
                            napi_create_reference(env, argv[1], 1, &(*workData)->callback);
                        }
                    }

                    return true;
                });
        }

        // GetDeviceModel 方法实现
        napi_value BiShareNapi::GetDeviceModel(napi_env env, napi_callback_info info) {
            return CreateAsyncWork(
                env, info, "GetDeviceModel",
                // 执行回调
                [](napi_env env, AsyncWorkData *workData) {
                    // 检查是否已初始化
                    if (!BiShareNapi::isInitialized_.load()) {
                        workData->result = BS_NOT_INIT;
                        workData->errorMessage = "BiShare service is not initialized";
                        return;
                    }

                    // 获取设备型号
                    // 直接调用函数获取设备模型字符串指针
                    const char *model = bishare_service_get_device_model();

                    // 验证返回的指针
                    if (model != nullptr) {
                        // 将返回的字符串保存到结构中
                        workData->data.stringParam1 = std::string(model);
                        workData->result = BS_OK;
                    } else {
                        // 设置错误状态
                        workData->result = BS_PARAMS_ERROR;
                        workData->errorMessage = "Failed to get device model: null pointer returned";
                    }
                },
                // 完成回调
                StringCompleteCallback,
                // 参数解析
                [](napi_env env, napi_callback_info info, AsyncWorkData **workData) {
                    // 创建工作数据
                    *workData = new AsyncWorkData(env);

                    // 获取回调信息
                    size_t argc = 1;
                    napi_value argv[1];
                    napi_value thisArg;
                    void *data;
                    napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);

                    // 解析回调（如果提供）
                    if (argc > 0) {
                        napi_valuetype valueType;
                        napi_typeof(env, argv[0], &valueType);

                        if (valueType == napi_function) {
                            napi_create_reference(env, argv[0], 1, &(*workData)->callback);
                        }
                    }

                    return true;
                });
        }

        // ResetDeviceModel 方法实现
        napi_value BiShareNapi::ResetDeviceModel(napi_env env, napi_callback_info info) {
            return CreateAsyncWork(
                env, info, "ResetDeviceModel",
                // 执行回调
                [](napi_env env, AsyncWorkData *workData) {
                    // 检查是否已初始化
                    if (!BiShareNapi::isInitialized_.load()) {
                        workData->result = BS_NOT_INIT;
                        workData->errorMessage = "BiShare service is not initialized";
                        return;
                    }

                    // 调用原生函数
                    workData->result = bishare_service_reset_device_model();

                    if (workData->result != BS_OK) {
                        workData->errorMessage =
                            std::string("Failed to reset device model: ") + std::string(err2str(workData->result));
                    }
                },
                // 完成回调
                StandardCompleteCallback,
                // 参数解析
                [](napi_env env, napi_callback_info info, AsyncWorkData **workData) {
                    // 创建工作数据
                    *workData = new AsyncWorkData(env);

                    // 获取回调信息
                    size_t argc = 1;
                    napi_value argv[1];
                    napi_value thisArg;
                    void *data;
                    napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);

                    // 解析回调（如果提供）
                    if (argc > 0) {
                        napi_valuetype valueType;
                        napi_typeof(env, argv[0], &valueType);

                        if (valueType == napi_function) {
                            napi_create_reference(env, argv[0], 1, &(*workData)->callback);
                        }
                    }

                    return true;
                });
        }

        // GetRootPath 方法实现 - 直接返回方式
        napi_value BiShareNapi::GetRootPath(napi_env env, napi_callback_info info) {
            // 获取回调信息
            size_t argc = 1;
            napi_value argv[1];
            napi_value thisArg;
            void *data;
            napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);

            // 检查是否已初始化
            if (!BiShareNapi::isInitialized_.load()) {
                napi_value error, message;
                napi_create_string_utf8(env, "BiShare service is not initialized", NAPI_AUTO_LENGTH, &message);
                napi_create_error(env, nullptr, message, &error);
                return error;
            }

            // 获取根路径
            // 直接调用函数获取根路径字符串指针
            const char *path = bishare_service_get_root_path();

            // 验证返回的指针
            if (path == nullptr) {
                napi_value error, message;
                std::string errorMessage = "Failed to get root path: null pointer returned";
                napi_create_string_utf8(env, errorMessage.c_str(), NAPI_AUTO_LENGTH, &message);
                napi_create_error(env, nullptr, message, &error);
                return error;
            }

            // 返回路径字符串
            napi_value resultValue;
            napi_create_string_utf8(env, path, NAPI_AUTO_LENGTH, &resultValue);
            return resultValue;
        }

        // GetCurrentDirector 方法实现 - 直接返回方式
        napi_value BiShareNapi::GetCurrentDirector(napi_env env, napi_callback_info info) {
            // 获取回调信息
            size_t argc = 1;
            napi_value argv[1];
            napi_value thisArg;
            void *data;
            napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);

            // 检查是否已初始化
            if (!BiShareNapi::isInitialized_.load()) {
                napi_value error, message;
                napi_create_string_utf8(env, "BiShare service is not initialized", NAPI_AUTO_LENGTH, &message);
                napi_create_error(env, nullptr, message, &error);
                return error;
            }

            // An获取当前目录
            // 直接调用函数获取当前目录字符串指针
            const char *dir = bishare_service_get_current_director();

            // 验证返回的指针
            if (dir == nullptr) {
                napi_value error, message;
                std::string errorMessage = "Failed to get current directory: null pointer returned";
                napi_create_string_utf8(env, errorMessage.c_str(), NAPI_AUTO_LENGTH, &message);
                napi_create_error(env, nullptr, message, &error);
                return error;
            }

            // 返回目录字符串
            napi_value resultValue;
            napi_create_string_utf8(env, dir, NAPI_AUTO_LENGTH, &resultValue);
            return resultValue;
        }

        // SetSize 方法实现
        napi_value BiShareNapi::SetSize(napi_env env, napi_callback_info info) {
            return CreateAsyncWork(
                env, info, "SetSize",
                // 执行回调
                [](napi_env env, AsyncWorkData *workData) {
                    // 检查是否已初始化
                    if (!BiShareNapi::isInitialized_.load()) {
                        workData->result = BS_NOT_INIT;
                        workData->errorMessage = "BiShare service is not initialized";
                        return;
                    }

                    // 调用原生函数
                    workData->result = bishare_service_set_size(workData->data.intParam1, workData->data.intParam2,
                                                                workData->data.intParam3, workData->data.intParam4);

                    if (workData->result != BS_OK) {
                        workData->errorMessage =
                            std::string("Failed to set size: ") + std::string(err2str(workData->result));
                    }
                },
                // 完成回调
                StandardCompleteCallback,
                // 参数解析
                [](napi_env env, napi_callback_info info, AsyncWorkData **workData) {
                    // 创建工作数据
                    *workData = new AsyncWorkData(env);

                    // 获取回调信息
                    size_t argc = 5;
                    napi_value argv[5];
                    napi_value thisArg;
                    void *data;
                    napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);

                    // 检查所需的参数
                    if (argc < 4) {
                        napi_throw_error(env, "EINVAL", "Wrong number of arguments, expected at least 4");
                        return false;
                    }

                    // 解析 screenWidth（数字）
                    int32_t screenWidth;
                    napi_get_value_int32(env, argv[0], &screenWidth);
                    (*workData)->data.intParam1 = screenWidth;

                    // 解析 screenHeight（数字）
                    int32_t screenHeight;
                    napi_get_value_int32(env, argv[1], &screenHeight);
                    (*workData)->data.intParam2 = screenHeight;

                    // 解析 videoWidth（数字）
                    int32_t videoWidth;
                    napi_get_value_int32(env, argv[2], &videoWidth);
                    (*workData)->data.intParam3 = videoWidth;

                    // 解析 videoHeight（数字）
                    int32_t videoHeight;
                    napi_get_value_int32(env, argv[3], &videoHeight);
                    (*workData)->data.intParam4 = videoHeight;

                    // 解析回调（如果提供）
                    if (argc > 4) {
                        napi_valuetype valueType;
                        napi_typeof(env, argv[4], &valueType);

                        if (valueType == napi_function) {
                            napi_create_reference(env, argv[4], 1, &(*workData)->callback);
                        }
                    }

                    return true;
                });
        }

        // SetDefaultAudioOutputDevice 方法实现
        napi_value BiShareNapi::SetDefaultAudioOutputDevice(napi_env env, napi_callback_info info) {
            return CreateAsyncWork(
                env, info, "SetDefaultAudioOutputDevice",
                // 执行回调
                [](napi_env env, AsyncWorkData *workData) {
                    // 检查是否已初始化
                    if (!BiShareNapi::isInitialized_.load()) {
                        workData->result = BS_NOT_INIT;
                        workData->errorMessage = "BiShare service is not initialized";
                        return;
                    }

                    // 调用原生函数
                    workData->result = bishare_service_set_default_audio_output_device(workData->data.enableFlag);

                    if (workData->result != BS_OK) {
                        workData->errorMessage = std::string("Failed to set default audio output device: ") +
                                                 std::string(err2str(workData->result));
                    }
                },
                // 完成回调
                StandardCompleteCallback,
                // 参数解析
                [](napi_env env, napi_callback_info info, AsyncWorkData **workData) {
                    // 创建工作数据
                    *workData = new AsyncWorkData(env);

                    // 获取回调信息
                    size_t argc = 2;
                    napi_value argv[2];
                    napi_value thisArg;
                    void *data;
                    napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);

                    // 检查所需的参数
                    if (argc < 1) {
                        napi_throw_error(env, "EINVAL", "Wrong number of arguments, expected at least 1");
                        return false;
                    }

                    // 解析 enable（布尔值）
                    bool enable;
                    napi_get_value_bool(env, argv[0], &enable);
                    (*workData)->data.enableFlag = enable ? BOOL_TRUE : BOOL_FALSE;

                    // 解析回调（如果提供）
                    if (argc > 1) {
                        napi_valuetype valueType;
                        napi_typeof(env, argv[1], &valueType);

                        if (valueType == napi_function) {
                            napi_create_reference(env, argv[1], 1, &(*workData)->callback);
                        }
                    }

                    return true;
                });
        }

        // Screenshot 方法实现
        napi_value BiShareNapi::Screenshot(napi_env env, napi_callback_info info) {
            return CreateAsyncWork(
                env, info, "Screenshot",
                // 执行回调
                [](napi_env env, AsyncWorkData *workData) {
                    // 检查是否已初始化
                    if (!BiShareNapi::isInitialized_.load()) {
                        workData->result = BS_NOT_INIT;
                        workData->errorMessage = "BiShare service is not initialized";
                        return;
                    }

                    // 调用原生函数
                    workData->result = bishare_service_screenshot(workData->data.stringParam1.c_str(),
                                                                  workData->data.longParam1, workData->data.longParam2,
                                                                  workData->data.longParam3, workData->data.longParam4);

                    if (workData->result != BS_OK) {
                        workData->errorMessage =
                            std::string("Failed to take screenshot: ") + std::string(err2str(workData->result));
                    }
                },
                // 完成回调
                StandardCompleteCallback,
                // 参数解析
                [](napi_env env, napi_callback_info info, AsyncWorkData **workData) {
                    // 创建工作数据
                    *workData = new AsyncWorkData(env);

                    // 获取回调信息
                    size_t argc = 6;
                    napi_value argv[6];
                    napi_value thisArg;
                    void *data;
                    napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);

                    // 检查所需的参数
                    if (argc < 5) {
                        napi_throw_error(env, "EINVAL", "Wrong number of arguments, expected at least 5");
                        return false;
                    }

                    // 解析 filePath（字符串）
                    size_t filePathLength;
                    napi_get_value_string_utf8(env, argv[0], nullptr, 0, &filePathLength);
                    if (filePathLength > 0) {
                        std::vector<char> filePathBuffer(filePathLength + 1);
                        napi_get_value_string_utf8(env, argv[0], filePathBuffer.data(), filePathLength + 1,
                                                   &filePathLength);
                        (*workData)->data.stringParam1 = std::string(filePathBuffer.data(), filePathLength);
                    } else {
                        (*workData)->data.stringParam1 = "";
                    }

                    // 解析 top（数字）
                    int32_t top;
                    napi_get_value_int32(env, argv[1], &top);
                    (*workData)->data.longParam1 = top;

                    // 解析 bottom（数字）
                    int32_t bottom;
                    napi_get_value_int32(env, argv[2], &bottom);
                    (*workData)->data.longParam2 = bottom;

                    // 解析 left（数字）
                    int32_t left;
                    napi_get_value_int32(env, argv[3], &left);
                    (*workData)->data.longParam3 = left;

                    // 解析 right（数字）
                    int32_t right;
                    napi_get_value_int32(env, argv[4], &right);
                    (*workData)->data.longParam4 = right;

                    // 解析回调（如果提供）
                    if (argc > 5) {
                        napi_valuetype valueType;
                        napi_typeof(env, argv[5], &valueType);

                        if (valueType == napi_function) {
                            napi_create_reference(env, argv[5], 1, &(*workData)->callback);
                        }
                    }

                    return true;
                });
        }

        // FindRemoteDevice 方法实现
        napi_value BiShareNapi::FindRemoteDevice(napi_env env, napi_callback_info info) {
            return CreateAsyncWork(
                env, info, "FindRemoteDevice",
                // 执行回调
                [](napi_env env, AsyncWorkData *workData) {
                    // 检查是否已初始化
                    if (!BiShareNapi::isInitialized_.load()) {
                        workData->result = BS_NOT_INIT;
                        workData->errorMessage = "BiShare service is not initialized";
                        return;
                    }

                    // 调用原生函数
                    workData->result = bishare_service_find_remote_device(workData->data.stringParam1.c_str());

                    if (workData->result != BS_OK) {
                        workData->errorMessage =
                            std::string("Failed to find remote device: ") + std::string(err2str(workData->result));
                    }
                },
                // 完成回调
                StandardCompleteCallback,
                // 参数解析
                [](napi_env env, napi_callback_info info, AsyncWorkData **workData) {
                    // 创建工作数据
                    *workData = new AsyncWorkData(env);

                    // 获取回调信息
                    size_t argc = 2;
                    napi_value argv[2];
                    napi_value thisArg;
                    void *data;
                    napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);

                    // 检查所需的参数
                    if (argc < 1) {
                        napi_throw_error(env, "EINVAL", "Wrong number of arguments, expected at least 1");
                        return false;
                    }

                    // 解析 pincode（字符串）
                    size_t pincodeLength;
                    napi_get_value_string_utf8(env, argv[0], nullptr, 0, &pincodeLength);
                    if (pincodeLength > 0) {
                        std::vector<char> pincodeBuffer(pincodeLength + 1);
                        napi_get_value_string_utf8(env, argv[0], pincodeBuffer.data(), pincodeLength + 1,
                                                   &pincodeLength);
                        (*workData)->data.stringParam1 = std::string(pincodeBuffer.data(), pincodeLength);
                    } else {
                        (*workData)->data.stringParam1 = "";
                    }

                    // 解析回调（如果提供）
                    if (argc > 1) {
                        napi_valuetype valueType;
                        napi_typeof(env, argv[1], &valueType);

                        if (valueType == napi_function) {
                            napi_create_reference(env, argv[1], 1, &(*workData)->callback);
                        }
                    }

                    return true;
                });
        }

        // SetNetworkInfo 方法实现
        napi_value BiShareNapi::SetNetworkInfo(napi_env env, napi_callback_info info) {
            return CreateAsyncWork(
                env, info, "SetNetworkInfo",
                // 执行回调
                [](napi_env env, AsyncWorkData *workData) {
                    // 检查是否已初始化
                    if (!BiShareNapi::isInitialized_.load()) {
                        workData->result = BS_NOT_INIT;
                        workData->errorMessage = "BiShare service is not initialized";
                        return;
                    }

                    // 转换参数
                    network_type_t networkType = workData->data.networkType;
                    BiShareLogger::Info(NAPI_TAG, "Value of networkType (string): %s", networkType);

                    // 调用原生函数
                    workData->result = bishare_service_set_network_info(networkType, workData->data.stringParam1.c_str(), workData->data.stringParam2.c_str());

                    if (workData->result != BS_OK) {
                        workData->errorMessage =
                            std::string("Failed to set network info: ") + std::string(err2str(workData->result));
                    }
                },
                // 完成回调
                StandardCompleteCallback,
                // 参数解析
                [](napi_env env, napi_callback_info info, AsyncWorkData **workData) {
                    // 创建工作数据
                    *workData = new AsyncWorkData(env);

                    //  获取回调信息
                    size_t argc = 4;
                    napi_value argv[4]; // 包括回调
                    napi_value thisArg;
                    void *data;
                    napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);

                    // 检查所需的参数
                    if (argc < 3) {
                        napi_throw_error(env, "EINVAL", "Wrong number of arguments, expected at least 3");
                        return false;
                    }

                    // 解析 type（network_type_t）
                    napi_valuetype valueType;
                    napi_typeof(env, argv[0], &valueType);

                    if (valueType == napi_number) {
                        int32_t networkType;
                        napi_get_value_int32(env, argv[0], &networkType);
                        (*workData)->data.networkType = static_cast<network_type_t>(networkType);
                    } else {
                        (*workData)->data.networkType = NotNetwork; // 默认
                    }

                    // 解析 addr（字符串）
                    size_t addrLength;
                    napi_get_value_string_utf8(env, argv[1], nullptr, 0, &addrLength);
                    if (addrLength > 0) {
                        std::vector<char> filePathBuffer(addrLength + 1);
                        napi_get_value_string_utf8(env, argv[1], filePathBuffer.data(), addrLength + 1,
                                                   &addrLength);
                        (*workData)->data.stringParam1 = std::string(filePathBuffer.data(), addrLength);
                    } else {
                        (*workData)->data.stringParam1 = "";
                    }

                    // 解析 mac（字符串）
                    size_t macLength;
                    napi_get_value_string_utf8(env, argv[2], nullptr, 0, &macLength);
                    if (macLength > 0) {
                        std::vector<char> filePathBuffer(macLength + 1);
                        napi_get_value_string_utf8(env, argv[2], filePathBuffer.data(), macLength + 1,
                                                   &macLength);
                        (*workData)->data.stringParam2 = std::string(filePathBuffer.data(), macLength);
                    } else {
                        (*workData)->data.stringParam2 = "";
                    }
        
                    // 解析回调（如果提供）
                    if (argc > 3) {
                        napi_valuetype valueType;
                        napi_typeof(env, argv[3], &valueType);

                        if (valueType == napi_function) {
                            napi_create_reference(env, argv[3], 1, &(*workData)->callback);
                        }
                    }

                    return true;
                });
        }

        // 事件处理方法 - On（添加事件监听器）
        napi_value BiShareNapi::On(napi_env env, napi_callback_info info) {
            // 获取回调信息
            size_t argc = 2;
            napi_value argv[2];
            napi_value thisArg;
            void *data;
            napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);

            // 检查所需的参数
            if (argc < 2) {
                napi_throw_error(env, "EINVAL", "Wrong number of arguments, expected at least 2");
                return nullptr;
            }

            // 解析事件类型（数字）
            int32_t eventType;
            napi_get_value_int32(env, argv[0], &eventType);

            // 解析回调函数
            napi_valuetype valueType;
            napi_typeof(env, argv[1], &valueType);
            if (valueType != napi_function) {
                napi_throw_error(env, "EINVAL", "Second argument must be a function");
                return nullptr;
            }

            // 创建回调函数的引用
            napi_ref callbackRef;
            napi_create_reference(env, argv[1], 1, &callbackRef);

            // 将引用存储在事件回调映射中
            pthread_mutex_lock(&BiShareNapi::instance_->eventCallbacksMutex_);
            BiShareNapi::instance_->eventCallbacks_[eventType].push_back({env, callbackRef});
            pthread_mutex_unlock(&BiShareNapi::instance_->eventCallbacksMutex_);

            // 返回 undefined
            napi_value result;
            napi_get_undefined(env, &result);
            return result;
        }

        // 事件处理方法 - Off（移除事件监听器）
        napi_value BiShareNapi::Off(napi_env env, napi_callback_info info) {
            // 获取回调信息
            size_t argc = 2;
            napi_value argv[2];
            napi_value thisArg;
            void *data;
            napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);

            // 检查所需的参数
            if (argc < 2) {
                napi_throw_error(env, "EINVAL", "Wrong number of arguments, expected at least 2");
                return nullptr;
            }

            // 解析事件类型（数字）
            int32_t eventType;
            napi_get_value_int32(env, argv[0], &eventType);

            // 解析回调函数
            napi_valuetype valueType;
            napi_typeof(env, argv[1], &valueType);
            if (valueType != napi_function) {
                napi_throw_error(env, "EINVAL", "Second argument must be a function");
                return nullptr;
            }

            // 在事件回调映射中查找并移除回调
            pthread_mutex_lock(&BiShareNapi::instance_->eventCallbacksMutex_);
            auto &callbacks = BiShareNapi::instance_->eventCallbacks_[eventType];
            for (auto it = callbacks.begin(); it != callbacks.end(); ++it) {
                if (it->env == env) {
                    napi_value callback1, callback2;
                    napi_get_reference_value(env, it->callbackRef, &callback1);
                    bool isEqual;
                    napi_strict_equals(env, callback1, argv[1], &isEqual);
                    if (isEqual) {
                        napi_delete_reference(env, it->callbackRef);
                        callbacks.erase(it);
                        break;
                    }
                }
            }
            pthread_mutex_unlock(&BiShareNapi::instance_->eventCallbacksMutex_);

            // 返回 undefined
            napi_value result;
            napi_get_undefined(env, &result);
            return result;
        }

        // 事件处理方法 - Once（一次性事件监听器）
        napi_value BiShareNapi::Once(napi_env env, napi_callback_info info) {
            // 获取回调信息
            size_t argc = 2;
            napi_value argv[2];
            napi_value thisArg;
            void *data;
            napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);

            // 检查所需的参数
            if (argc < 2) {
                napi_throw_error(env, "EINVAL", "Wrong number of arguments, expected at least 2");
                return nullptr;
            }

            // 解析事件类型（数字）
            int32_t eventType;
            napi_get_value_int32(env, argv[0], &eventType);

            // 解析回调函数
            napi_valuetype valueType;
            napi_typeof(env, argv[1], &valueType);
            if (valueType != napi_function) {
                napi_throw_error(env, "EINVAL", "Second argument must be a function");
                return nullptr;
            }

            // 创建回调函数的引用（用于后续调用）
            napi_ref callbackRef;
            napi_create_reference(env, argv[1], 1, &callbackRef);

            // 创建一个结构体来存储事件类型和回调引用
            struct OnceData {
                int32_t eventType;
                napi_ref callbackRef;
                napi_env env;
            };

            // 创建数据的副本（在堆上分配，以确保生命周期）
            auto onceData = new OnceData{eventType, callbackRef, env};

            // 创建包装函数
            napi_value wrapperFunction;
            napi_create_function(
                env, "onceWrapper", NAPI_AUTO_LENGTH,
                // 包装函数的回调
                [](napi_env env, napi_callback_info info) -> napi_value {
                    // 获取用户数据（OnceData 结构）
                    void *callbackData;
                    napi_value thisObj;
                    napi_get_cb_info(env, info, nullptr, nullptr, &thisObj, &callbackData);
                    auto onceData = static_cast<OnceData *>(callbackData);

                    // 移除包装函数
                    pthread_mutex_lock(&BiShareNapi::instance_->eventCallbacksMutex_);
                    auto &callbacks = BiShareNapi::instance_->eventCallbacks_[onceData->eventType];
                    for (auto it = callbacks.begin(); it != callbacks.end();) {
                        if (it->env == onceData->env) {
                            napi_value funcRef;
                            napi_get_reference_value(env, it->callbackRef, &funcRef);
                            napi_value thisObj;
                            napi_get_cb_info(env, info, nullptr, nullptr, &thisObj, nullptr);

                            bool isWrapped = false;
                            napi_get_value_bool(env, thisObj, &isWrapped);

                            if (isWrapped) {
                                napi_delete_reference(env, it->callbackRef);
                                it = callbacks.erase(it);
                                continue;
                            }
                        }
                        ++it;
                    }
                    pthread_mutex_unlock(&BiShareNapi::instance_->eventCallbacksMutex_);

                    // 调用原始回调函数
                    size_t argc = 0;
                    napi_value *argv = nullptr;
                    napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);

                    // 创建新的参数数组
                    std::vector<napi_value> newArgv(argc);
                    if (argc > 0) {
                        napi_get_cb_info(env, info, &argc, newArgv.data(), nullptr, nullptr);
                    }

                    napi_value callback, result, global;
                    napi_get_reference_value(env, onceData->callbackRef, &callback);
                    napi_get_global(env, &global);
                    napi_call_function(env, global, callback, argc, argc > 0 ? newArgv.data() : nullptr, &result);

                    // 清理
                    napi_delete_reference(env, onceData->callbackRef);
                    delete onceData;

                    return result;
                },
                // 用户数据（事件类型和回调引用）
                onceData, &wrapperFunction);

            // 创建包装函数的引用
            napi_ref wrapperRef;
            napi_create_reference(env, wrapperFunction, 1, &wrapperRef);

            // 将引用存储在事件回调映射中
            pthread_mutex_lock(&BiShareNapi::instance_->eventCallbacksMutex_);
            BiShareNapi::instance_->eventCallbacks_[eventType].push_back({env, wrapperRef});
            pthread_mutex_unlock(&BiShareNapi::instance_->eventCallbacksMutex_);

            // 返回 undefined
            napi_value result;
            napi_get_undefined(env, &result);
            return result;
        }

        // 模块初始化
        EXTERN_C_START
        static napi_module g_biShareModule = {
            .nm_version = 1,
            .nm_flags = 0,
            .nm_filename = nullptr,
            .nm_register_func = BiShareNapi::Init,
            .nm_modname = BISHARE_MODULE_NAME,
            .nm_priv = nullptr,
            .reserved = {nullptr},
        };
        EXTERN_C_END

        // 注册模块
        extern "C" __attribute__((constructor)) void RegisterBiShareModule(void) {
            napi_module_register(&g_biShareModule);
        }

    } // namespace BiShare
} // namespace OHOS