#include "bishare_recording.h"
#include "bishare_logger.h"

namespace OHOS {
    namespace BiShare {

        // Define a tag for logging
        static constexpr const char* RECORDING_TAG = "BiShareRecording";

        BiShareRecordManager::BiShareRecordManager() {
            // Constructor
        }

        BiShareRecordManager::~BiShareRecordManager() {
            // Destructor
        }

        bstatus_t BiShareRecordManager::StartScreenRecord(int session, int displayId, int direction) {
            // Validate parameters
            if (session <= 0) {
                BiShareLogger::Error(RECORDING_TAG, "Invalid session ID: %d", session);
                return BS_PARAMS_ERROR;
            }

            if (direction != DIR_SEND && direction != DIR_RECV) {
                BiShareLogger::Error(RECORDING_TAG, "Invalid direction: %d", direction);
                return BS_PARAMS_ERROR;
            }

            // Start screen recording
            return bishare_service_start_screen_record(session, displayId, direction);
        }

        bstatus_t BiShareRecordManager::StopScreenRecord(int session, int displayId, int direction) {
            // Validate parameters
            if (session <= 0) {
                BiShareLogger::Error(RECORDING_TAG, "Invalid session ID: %d", session);
                return BS_PARAMS_ERROR;
            }

            if (direction != DIR_SEND && direction != DIR_RECV) {
                BiShareLogger::Error(RECORDING_TAG, "Invalid direction: %d", direction);
                return BS_PARAMS_ERROR;
            }

            // Stop screen recording
            return bishare_service_stop_screen_record(session, displayId, direction);
        }

        bstatus_t BiShareRecordManager::StartCapture() {
            // Start capture
            return bishare_service_start_capture();
        }

        bstatus_t BiShareRecordManager::SetSize(int screenWidth, int screenHeight, int videoWidth, int videoHeight) {
            // Validate parameters
            if (screenWidth <= 0 || screenHeight <= 0 || videoWidth <= 0 || videoHeight <= 0) {
                BiShareLogger::Error(RECORDING_TAG, "Invalid screen or video size parameters");
                return BS_PARAMS_ERROR;
            }

            // Set screen and video size
            return bishare_service_set_size(screenWidth, screenHeight, videoWidth, videoHeight);
        }

        bstatus_t BiShareRecordManager::Screenshot(const std::string &filePath, long top, long bottom, long left,
                                                   long right) {
            // Validate parameters
            if (filePath.empty()) {
                BiShareLogger::Error(RECORDING_TAG, "File path is empty");
                return BS_PARAMS_ERROR;
            }

            if (top >= bottom || left >= right) {
                BiShareLogger::Error(RECORDING_TAG, "Invalid coordinates for screenshot");
                return BS_PARAMS_ERROR;
            }

            // Take screenshot
            return bishare_service_screenshot(filePath.c_str(), top, bottom, left, right);
        }

        bstatus_t BiShareRecordManager::SetDefaultAudioOutputDevice(bool enable) {
            // Set default audio output device
            bool_type_t enableType = enable ? BOOL_TRUE : BOOL_FALSE;
            return bishare_service_set_default_audio_output_device(enableType);
        }

    } // namespace BiShare
} // namespace OHOS