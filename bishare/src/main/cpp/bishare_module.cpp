#include <napi/native_api.h>
#include "bishare_napi_interface.h"

using namespace OHOS::BiShare::Interfaces;

/**
 * BiShare模块入口点
 * 
 * 这是整个BiShare模块的主入口文件，负责：
 * 1. 注册NAPI模块
 * 2. 初始化模块接口
 * 3. 导出JavaScript可用的API
 */

/**
 * 模块初始化函数
 */
static napi_value Init(napi_env env, napi_callback_info info) {
    napi_value exports;
    size_t argc = 1;
    napi_value argv[1];
    napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);

    if (argc > 0) {
        exports = argv[0];
    } else {
        napi_create_object(env, &exports);
    }

    return BiShareNapiInterface::Init(env, exports);
}

/**
 * 模块描述符
 */
static napi_module bishareModule = {
    .nm_version = 1,
    .nm_flags = 0,
    .nm_filename = nullptr,
    .nm_register_func = Init,
    .nm_modname = "bishare",
    .nm_priv = nullptr,
    .reserved = {nullptr},
};

/**
 * 注册模块
 */
extern "C" __attribute__((constructor)) void RegisterBiShareModule(void) {
    napi_module_register(&bishareModule);
}
