#include "bishare_facade.h"
#include "../managers/service_manager.h"
#include "../managers/callback_manager.h"
#include "../operations/operation_factory.h"

namespace OHOS {
    namespace BiShare {
        namespace Core {

            BiShareFacade& BiShareFacade::GetInstance() {
                static BiShareFacade instance;
                return instance;
            }

            bool BiShareFacade::Initialize() {
                if (isInitialized_.load()) {
                    return true;
                }

                try {
                    // 创建服务管理器
                    serviceManager_ = std::make_shared<ServiceManager>();
                    if (!serviceManager_->Initialize()) {
                        return false;
                    }

                    // 创建回调管理器
                    callbackManager_ = std::make_shared<CallbackManager>();
                    if (!callbackManager_->Initialize()) {
                        return false;
                    }

                    // 创建操作工厂
                    operationFactory_ = std::make_shared<OperationFactory>(serviceManager_, callbackManager_);

                    isInitialized_.store(true);
                    return true;
                } catch (const std::exception& e) {
                    // 清理已创建的资源
                    Release();
                    return false;
                }
            }

            void BiShareFacade::Release() {
                if (!isInitialized_.load()) {
                    return;
                }

                // 按相反顺序释放资源
                operationFactory_.reset();
                
                if (callbackManager_) {
                    callbackManager_->Release();
                    callbackManager_.reset();
                }

                if (serviceManager_) {
                    serviceManager_->Release();
                    serviceManager_.reset();
                }

                isInitialized_.store(false);
            }

        } // namespace Core
    } // namespace BiShare
} // namespace OHOS
