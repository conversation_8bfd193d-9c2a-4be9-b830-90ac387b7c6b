#include "bishare_callbacks.h"
#include "../../infrastructure/logging/bishare_logger.h"
#include "../../interfaces/napi/bishare_napi.h"
#include <unistd.h>
#include <sstream>


namespace OHOS {
    namespace BiShare {

        static constexpr const char* CALLBACKS_TAG = "BiShareCallbacks";

        // Initialize static instance
        std::shared_ptr<BiShareCallbacks> BiShareCallbacks::instance_ = nullptr;

        BiShareCallbacks::BiShareCallbacks(std::weak_ptr<BiShareNapi> napiInstance)
            : napiInstance_(napiInstance), isRunning_(false) {
            // Set the static instance
            instance_ = std::shared_ptr<BiShareCallbacks>(this);
        }

        BiShareCallbacks::~BiShareCallbacks() {
            // Stop the callback threads
            isRunning_ = false;

            // Notify the threads to exit
            eventCallbackCondition_.notify_all();
            packetCallbackCondition_.notify_all();

            // Wait for the threads to exit
            if (eventCallbackThread_.joinable()) {
                eventCallbackThread_.join();
            }

            if (packetCallbackThread_.joinable()) {
                packetCallbackThread_.join();
            }

            // Clear the static instance
            instance_ = nullptr;
        }

        bool BiShareCallbacks::Initialize() {
            // Start the callback threads
            isRunning_ = true;

            // Start the event callback thread
            eventCallbackThread_ = std::thread(&BiShareCallbacks::EventCallbackThreadFunc, this);

            // Start the packet callback thread
            packetCallbackThread_ = std::thread(&BiShareCallbacks::PacketCallbackThreadFunc, this);

            return true;
        }

        bool BiShareCallbacks::RegisterEventCallback(napi_env env, napi_value callback, int eventType,
                                                     napi_ref *callbackRef) {
            // Create a reference to the callback
            napi_create_reference(env, callback, 1, callbackRef);

            return true;
        }

        bool BiShareCallbacks::UnregisterEventCallback(napi_env env, napi_ref callbackRef, int eventType) {
            // Delete the reference
            napi_delete_reference(env, callbackRef);

            return true;
        }

        void BiShareCallbacks::OnEventCallback(int type, const char *value, int len) {
            BiShareLogger::Info(CALLBACKS_TAG, "OnEventCallback - type: %d, len: %d", type, len);
            BiShareLogger::Info(CALLBACKS_TAG, "OnEventCallback - value: %{public}s", value);
            // Check if instance exists
            if (instance_ == nullptr) {
                BiShareLogger::Error(CALLBACKS_TAG, "BiShareCallbacks instance is null");
                return;
            }

            // Enqueue the event callback
            instance_->EnqueueEventCallback(type, value, len);
        }

        void BiShareCallbacks::OnPacketCallback(int session, int type, const char *buffer, int len, int width,
                                                int height, int time) {
            BiShareLogger::Info(CALLBACKS_TAG, "OnPacketCallback - session: %d, type: %d, len: %d, width: %d, height: %d, time: %d", session, type, len, width, height, time);
            BiShareLogger::Info(CALLBACKS_TAG, "OnEventCallback - buffer: %{public}s", buffer);
            
            // Check if instance exists
            if (instance_ == nullptr) {
                BiShareLogger::Error(CALLBACKS_TAG, "BiShareCallbacks instance is null");
                return;
            }

            // Enqueue the packet callback
            instance_->EnqueuePacketCallback(session, type, buffer, len, width, height, time);
        }

        void BiShareCallbacks::EnqueueEventCallback(int type, const char *value, int len) {
            // Create event callback data
            EventCallbackData eventData;
            eventData.eventType = type;
            eventData.eventData = std::string(value, len);
            eventData.dataLength = len;

            // Enqueue the event callback
            {
                std::lock_guard<std::mutex> lock(eventCallbackMutex_);
                eventCallbackQueue_.push(eventData);
            }

            // Notify the event callback thread
            eventCallbackCondition_.notify_one();
        }

        void BiShareCallbacks::EnqueuePacketCallback(int session, int type, const char *buffer, int len, int width,
                                                     int height, int time) {
            // Create packet callback data
            PacketCallbackData packetData;
            packetData.session = session;
            packetData.bufferType = type;
            packetData.buffer.assign(buffer, buffer + len);
            packetData.width = width;
            packetData.height = height;
            packetData.timestamp = time;

            // Enqueue the packet callback
            {
                std::lock_guard<std::mutex> lock(packetCallbackMutex_);
                packetCallbackQueue_.push(packetData);
            }

            // Notify the packet callback thread
            packetCallbackCondition_.notify_one();
        }

        void BiShareCallbacks::EventCallbackThreadFunc() {
            while (isRunning_) {
                EventCallbackData eventData;

                // Wait for an event callback
                {
                    std::unique_lock<std::mutex> lock(eventCallbackMutex_);
                    eventCallbackCondition_.wait(lock, [this] { return !isRunning_ || !eventCallbackQueue_.empty(); });

                    // Check if we need to exit
                    if (!isRunning_) {
                        break;
                    }

                    // Get the event callback
                    if (eventCallbackQueue_.empty()) {
                        continue;
                    }

                    eventData = eventCallbackQueue_.front();
                    eventCallbackQueue_.pop();
                }

                // Get the NAPI instance from the weak pointer
                auto napiInstance = napiInstance_.lock();
                if (!napiInstance) {
                    BiShareLogger::Error(CALLBACKS_TAG, "NAPI instance is null");
                    continue;
                }

                // Find all callbacks for this event type
                std::vector<BiShareNapi::EventInfo> callbacks;
                {
                    pthread_mutex_lock(&napiInstance->eventCallbacksMutex_);
                    callbacks = napiInstance->eventCallbacks_[eventData.eventType];
                    pthread_mutex_unlock(&napiInstance->eventCallbacksMutex_);
                }

                // Call each callback with the event data
                for (const auto &callbackInfo : callbacks) {
                    napi_env env = callbackInfo.env;
                    napi_ref callbackRef = callbackInfo.callbackRef;

                    // Create the event data object
                    napi_value eventObject;
                    napi_create_object(env, &eventObject);

                    // Set event type
                    napi_value eventType;
                    napi_create_int32(env, eventData.eventType, &eventType);
                    napi_set_named_property(env, eventObject, "type", eventType);

                    // Set event data
                    napi_value eventDataValue;
                    napi_create_string_utf8(env, eventData.eventData.c_str(), eventData.eventData.length(),
                                            &eventDataValue);
                    napi_set_named_property(env, eventObject, "data", eventDataValue);

                    // Get the callback function
                    napi_value callback;
                    napi_get_reference_value(env, callbackRef, &callback);

                    // Call the callback function
                    napi_value global, result;
                    napi_get_global(env, &global);

                    // Prepare arguments
                    napi_value args[1] = {eventObject};

                    // Call the function
                    napi_status status = napi_call_function(env, global, callback, 1, args, &result);
                    if (status != napi_ok) {
                        BiShareLogger::Error(CALLBACKS_TAG, "Failed to call event callback function");
                    }
                }
            }
        }

        void BiShareCallbacks::PacketCallbackThreadFunc() {
            while (isRunning_) {
                PacketCallbackData packetData;

                // Wait for a packet callback
                {
                    std::unique_lock<std::mutex> lock(packetCallbackMutex_);
                    packetCallbackCondition_.wait(lock,
                                                  [this] { return !isRunning_ || !packetCallbackQueue_.empty(); });

                    // Check if we need to exit
                    if (!isRunning_) {
                        break;
                    }

                    // Get the packet callback
                    if (packetCallbackQueue_.empty()) {
                        continue;
                    }

                    packetData = packetCallbackQueue_.front();
                    packetCallbackQueue_.pop();
                }

                // Get the NAPI instance from the weak pointer
                auto napiInstance = napiInstance_.lock();
                if (!napiInstance) {
                    BiShareLogger::Error(CALLBACKS_TAG, "NAPI instance is null");
                    continue;
                }

                // Find all callbacks for the packet event type (we'll use EVENT_SCREEN_RECORD for packets)
                std::vector<BiShareNapi::EventInfo> callbacks;
                {
                    pthread_mutex_lock(&napiInstance->eventCallbacksMutex_);
                    callbacks = napiInstance->eventCallbacks_[EVENT_SCREEN_RECORD];
                    pthread_mutex_unlock(&napiInstance->eventCallbacksMutex_);
                }

                // Call each callback with the packet data
                for (const auto &callbackInfo : callbacks) {
                    napi_env env = callbackInfo.env;
                    napi_ref callbackRef = callbackInfo.callbackRef;

                    // Create the packet data object
                    napi_value packetObject;
                    napi_create_object(env, &packetObject);

                    // Set session
                    napi_value session;
                    napi_create_int32(env, packetData.session, &session);
                    napi_set_named_property(env, packetObject, "session", session);

                    // Set buffer type
                    napi_value bufferType;
                    napi_create_int32(env, packetData.bufferType, &bufferType);
                    napi_set_named_property(env, packetObject, "bufferType", bufferType);

                    // Set width
                    napi_value width;
                    napi_create_int32(env, packetData.width, &width);
                    napi_set_named_property(env, packetObject, "width", width);

                    // Set height
                    napi_value height;
                    napi_create_int32(env, packetData.height, &height);
                    napi_set_named_property(env, packetObject, "height", height);

                    // Set timestamp
                    napi_value timestamp;
                    napi_create_int32(env, packetData.timestamp, &timestamp);
                    napi_set_named_property(env, packetObject, "timestamp", timestamp);

                    // Set buffer (ArrayBuffer)
                    napi_value buffer;
                    void *bufferData;
                    napi_create_arraybuffer(env, packetData.buffer.size(), &bufferData, &buffer);
                    memcpy(bufferData, packetData.buffer.data(), packetData.buffer.size());
                    napi_set_named_property(env, packetObject, "buffer", buffer);

                    // Get the callback function
                    napi_value callback;
                    napi_get_reference_value(env, callbackRef, &callback);

                    // Call the callback function
                    napi_value global, result;
                    napi_get_global(env, &global);

                    // Prepare arguments
                    napi_value args[1] = {packetObject};

                    // Call the function
                    napi_status status = napi_call_function(env, global, callback, 1, args, &result);
                    if (status != napi_ok) {
                        BiShareLogger::Error(CALLBACKS_TAG, "Failed to call packet callback function");
                    }
                }
            }
        }

    } // namespace BiShare
} // namespace OHOS