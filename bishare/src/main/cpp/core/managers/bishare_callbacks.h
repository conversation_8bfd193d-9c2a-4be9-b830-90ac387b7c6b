#ifndef BISHARE_CALLBACKS_H
#define BISHARE_CALLBACKS_H

#include <napi/native_api.h>
#include <string>
#include <vector>
#include <map>
#include <mutex>
#include <memory>
#include <functional>
#include <queue>
#include <thread>
#include <condition_variable>

#include "bishare-define.h"

namespace OHOS {
    namespace BiShare {

        class BiShareNapi;

        // Callback data structure for async events
        struct EventCallbackData {
            napi_env env;
            napi_ref callbackRef;
            int eventType;
            std::string eventData;
            size_t dataLength;
        };

        // Packet data structure for frame data
        struct PacketCallbackData {
            napi_env env;
            napi_ref callbackRef;
            int session;
            int bufferType;
            std::vector<uint8_t> buffer;
            int width;
            int height;
            int timestamp;
        };

        // Main callback management class
        class BiShareCallbacks {
        public:
            BiShareCallbacks(std::weak_ptr<BiShareNapi> napiInstance);
            ~BiShareCallbacks();

            // Initialize the callback system
            bool Initialize();

            // Register for events
            bool RegisterEventCallback(napi_env env, napi_value callback, int eventType, napi_ref *callbackRef);

            // Unregister events
            bool UnregisterEventCallback(napi_env env, napi_ref callbackRef, int eventType);

            // Native callback handlers
            static void OnEventCallback(int type, const char *value, int len);
            static void OnPacketCallback(int session, int type, const char *buffer, int len, int width, int height,
                                         int time);

        private:
            // Singleton pointer to the NAPI instance
            std::weak_ptr<BiShareNapi> napiInstance_;

            // Event callback thread management
            std::thread eventCallbackThread_;
            std::condition_variable eventCallbackCondition_;
            std::mutex eventCallbackMutex_;
            std::queue<EventCallbackData> eventCallbackQueue_;
            bool isRunning_;

            // Packet callback thread management
            std::thread packetCallbackThread_;
            std::condition_variable packetCallbackCondition_;
            std::mutex packetCallbackMutex_;
            std::queue<PacketCallbackData> packetCallbackQueue_;

            // Event callback processing
            void EventCallbackThreadFunc();
            void PacketCallbackThreadFunc();

            // Helper functions
            void EnqueueEventCallback(int type, const char *value, int len);
            void EnqueuePacketCallback(int session, int type, const char *buffer, int len, int width, int height,
                                       int time);

            // Static instance for callback context
            static std::shared_ptr<BiShareCallbacks> instance_;
        };

    } // namespace BiShare
} // namespace OHOS

#endif // BISHARE_CALLBACKS_H