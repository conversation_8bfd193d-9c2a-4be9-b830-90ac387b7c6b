#include "callback_manager.h"
#include "../../thirdparty/biservice/include/bishare-service.h"

namespace OHOS {
    namespace BiShare {
        namespace Core {

            // 静态实例
            std::shared_ptr<CallbackManager> CallbackManager::instance_;

            CallbackManager::CallbackManager() {
                // 构造函数
            }

            CallbackManager::~CallbackManager() {
                Release();
            }

            bool CallbackManager::Initialize() {
                if (isRunning_.load()) {
                    return true; // 已经初始化
                }

                try {
                    // 设置静态实例
                    instance_ = shared_from_this();

                    // 启动事件处理线程
                    isRunning_.store(true);
                    eventProcessingThread_ = std::thread(&CallbackManager::ProcessEventQueue, this);
                    packetProcessingThread_ = std::thread(&CallbackManager::ProcessPacketQueue, this);

                    // 注册原生回调
                    bishare_service_register_event_callback(OnEventCallback);
                    bishare_service_register_packet_callback(OnPacketCallback);

                    return true;
                } catch (const std::exception& e) {
                    Release();
                    return false;
                }
            }

            void CallbackManager::Release() {
                if (!isRunning_.load()) {
                    return;
                }

                // 停止线程
                isRunning_.store(false);
                
                // 通知线程退出
                eventQueueCondition_.notify_all();
                packetQueueCondition_.notify_all();

                // 等待线程结束
                if (eventProcessingThread_.joinable()) {
                    eventProcessingThread_.join();
                }
                if (packetProcessingThread_.joinable()) {
                    packetProcessingThread_.join();
                }

                // 清理事件监听器
                std::lock_guard<std::mutex> lock(eventListenersMutex_);
                for (auto& pair : eventListeners_) {
                    for (auto& eventInfo : pair.second) {
                        if (eventInfo.callbackRef) {
                            napi_delete_reference(eventInfo.env, eventInfo.callbackRef);
                        }
                    }
                }
                eventListeners_.clear();

                // 清理静态实例
                instance_.reset();
            }

            bool CallbackManager::RegisterEventListener(napi_env env, int eventType, napi_value callback, bool isOnce) {
                napi_ref callbackRef;
                napi_status status = napi_create_reference(env, callback, 1, &callbackRef);
                if (status != napi_ok) {
                    return false;
                }

                std::lock_guard<std::mutex> lock(eventListenersMutex_);
                eventListeners_[eventType].push_back({env, callbackRef, isOnce});
                return true;
            }

            bool CallbackManager::UnregisterEventListener(napi_env env, int eventType, napi_value callback) {
                std::lock_guard<std::mutex> lock(eventListenersMutex_);
                auto it = eventListeners_.find(eventType);
                if (it == eventListeners_.end()) {
                    return false;
                }

                auto& listeners = it->second;
                for (auto listenerIt = listeners.begin(); listenerIt != listeners.end(); ++listenerIt) {
                    if (listenerIt->env == env) {
                        napi_value existingCallback;
                        napi_get_reference_value(env, listenerIt->callbackRef, &existingCallback);
                        
                        bool isEqual;
                        napi_strict_equals(env, existingCallback, callback, &isEqual);
                        if (isEqual) {
                            napi_delete_reference(env, listenerIt->callbackRef);
                            listeners.erase(listenerIt);
                            return true;
                        }
                    }
                }
                return false;
            }

            void CallbackManager::UnregisterAllEventListeners(int eventType) {
                std::lock_guard<std::mutex> lock(eventListenersMutex_);
                auto it = eventListeners_.find(eventType);
                if (it != eventListeners_.end()) {
                    for (auto& eventInfo : it->second) {
                        if (eventInfo.callbackRef) {
                            napi_delete_reference(eventInfo.env, eventInfo.callbackRef);
                        }
                    }
                    eventListeners_.erase(it);
                }
            }

            void CallbackManager::EmitEvent(int eventType, const std::string& eventValue, size_t dataLength) {
                EventData eventData;
                eventData.eventType = eventType;
                eventData.eventValue = eventValue;
                eventData.dataLength = dataLength;

                std::lock_guard<std::mutex> lock(eventQueueMutex_);
                eventQueue_.push(eventData);
                eventQueueCondition_.notify_one();
            }

            void CallbackManager::HandlePacketCallback(int session, int bufferType, const char* buffer, 
                                                     int length, int width, int height, int timestamp) {
                PacketData packetData;
                packetData.session = session;
                packetData.bufferType = bufferType;
                packetData.buffer.assign(buffer, buffer + length);
                packetData.width = width;
                packetData.height = height;
                packetData.timestamp = timestamp;

                std::lock_guard<std::mutex> lock(packetQueueMutex_);
                packetQueue_.push(packetData);
                packetQueueCondition_.notify_one();
            }

            // 静态回调函数
            void CallbackManager::OnEventCallback(int type, const char* value, int len) {
                if (instance_) {
                    instance_->EmitEvent(type, std::string(value, len), len);
                }
            }

            void CallbackManager::OnPacketCallback(int session, int type, const char* buffer, 
                                                 int len, int width, int height, int time) {
                if (instance_) {
                    instance_->HandlePacketCallback(session, type, buffer, len, width, height, time);
                }
            }

            void CallbackManager::SetInstance(std::shared_ptr<CallbackManager> instance) {
                instance_ = instance;
            }

            void CallbackManager::ProcessEventQueue() {
                while (isRunning_.load()) {
                    std::unique_lock<std::mutex> lock(eventQueueMutex_);
                    eventQueueCondition_.wait(lock, [this] { return !eventQueue_.empty() || !isRunning_.load(); });

                    while (!eventQueue_.empty() && isRunning_.load()) {
                        EventData eventData = eventQueue_.front();
                        eventQueue_.pop();
                        lock.unlock();

                        // 处理事件
                        std::lock_guard<std::mutex> listenerLock(eventListenersMutex_);
                        auto it = eventListeners_.find(eventData.eventType);
                        if (it != eventListeners_.end()) {
                            auto& listeners = it->second;
                            for (auto listenerIt = listeners.begin(); listenerIt != listeners.end();) {
                                InvokeEventCallback(*listenerIt, eventData);
                                
                                // 如果是一次性监听器，删除它
                                if (listenerIt->isOnce) {
                                    napi_delete_reference(listenerIt->env, listenerIt->callbackRef);
                                    listenerIt = listeners.erase(listenerIt);
                                } else {
                                    ++listenerIt;
                                }
                            }
                        }

                        lock.lock();
                    }
                }
            }

            void CallbackManager::ProcessPacketQueue() {
                while (isRunning_.load()) {
                    std::unique_lock<std::mutex> lock(packetQueueMutex_);
                    packetQueueCondition_.wait(lock, [this] { return !packetQueue_.empty() || !isRunning_.load(); });

                    while (!packetQueue_.empty() && isRunning_.load()) {
                        PacketData packetData = packetQueue_.front();
                        packetQueue_.pop();
                        lock.unlock();

                        // 处理数据包（这里可以根据需要实现具体逻辑）
                        // 暂时不处理数据包回调，因为原代码中也没有具体实现

                        lock.lock();
                    }
                }
            }

            void CallbackManager::InvokeEventCallback(const EventInfo& eventInfo, const EventData& eventData) {
                napi_value callback, global, result;
                napi_get_reference_value(eventInfo.env, eventInfo.callbackRef, &callback);
                napi_get_global(eventInfo.env, &global);

                // 创建事件对象
                napi_value eventObject = CreateEventObject(eventInfo.env, eventData);
                
                // 调用回调
                napi_call_function(eventInfo.env, global, callback, 1, &eventObject, &result);
            }

            void CallbackManager::InvokePacketCallback(const EventInfo& eventInfo, const PacketData& packetData) {
                // 暂时不实现，因为原代码中也没有具体的数据包回调处理
            }

            napi_value CallbackManager::CreateEventObject(napi_env env, const EventData& eventData) {
                napi_value eventObject;
                napi_create_object(env, &eventObject);

                // 设置事件类型
                napi_value eventType;
                napi_create_int32(env, eventData.eventType, &eventType);
                napi_set_named_property(env, eventObject, "type", eventType);

                // 设置事件数据
                napi_value eventValue;
                napi_create_string_utf8(env, eventData.eventValue.c_str(), eventData.eventValue.length(), &eventValue);
                napi_set_named_property(env, eventObject, "data", eventValue);

                return eventObject;
            }

            napi_value CallbackManager::CreatePacketObject(napi_env env, const PacketData& packetData) {
                napi_value packetObject;
                napi_create_object(env, &packetObject);

                // 设置会话ID
                napi_value session;
                napi_create_int32(env, packetData.session, &session);
                napi_set_named_property(env, packetObject, "session", session);

                // 设置缓冲区类型
                napi_value bufferType;
                napi_create_int32(env, packetData.bufferType, &bufferType);
                napi_set_named_property(env, packetObject, "bufferType", bufferType);

                // 设置尺寸信息
                napi_value width, height;
                napi_create_int32(env, packetData.width, &width);
                napi_create_int32(env, packetData.height, &height);
                napi_set_named_property(env, packetObject, "width", width);
                napi_set_named_property(env, packetObject, "height", height);

                // 设置时间戳
                napi_value timestamp;
                napi_create_int32(env, packetData.timestamp, &timestamp);
                napi_set_named_property(env, packetObject, "timestamp", timestamp);

                return packetObject;
            }

        } // namespace Core
    } // namespace BiShare
} // namespace OHOS
