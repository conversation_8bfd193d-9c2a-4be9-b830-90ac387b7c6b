#include "service_manager.h"
#include "../../domain/device/device_service.h"
#include "../../domain/recording/recording_service.h"
#include "../../domain/network/network_service.h"
#include "../../thirdparty/biservice/include/bishare-service.h"

namespace OHOS {
    namespace BiShare {
        namespace Core {

            ServiceManager::ServiceManager() {
                // 构造函数
            }

            ServiceManager::~ServiceManager() {
                Release();
            }

            bool ServiceManager::Initialize() {
                try {
                    // 初始化设备服务
                    if (!InitializeDeviceService()) {
                        return false;
                    }

                    // 初始化录制服务
                    if (!InitializeRecordingService()) {
                        return false;
                    }

                    // 初始化网络服务
                    if (!InitializeNetworkService()) {
                        return false;
                    }

                    return true;
                } catch (const std::exception& e) {
                    Release();
                    return false;
                }
            }

            void ServiceManager::Release() {
                // 释放BiShare服务
                if (isServiceInitialized_.load()) {
                    ReleaseBiShareService();
                }

                // 释放领域服务
                networkService_.reset();
                recordingService_.reset();
                deviceService_.reset();
            }

            bstatus_t ServiceManager::InitializeBiShareService(bool isConsole, bool isFile, 
                                                              const std::string& filePath, 
                                                              log_priority_t priority) {
                if (isServiceInitialized_.load()) {
                    return BS_OK; // 已经初始化
                }

                bstatus_t result = bishare_service_init(
                    isConsole ? BOOL_TRUE : BOOL_FALSE,
                    isFile ? BOOL_TRUE : BOOL_FALSE,
                    filePath.c_str(),
                    priority
                );

                if (result == BS_OK) {
                    isServiceInitialized_.store(true);
                }

                return result;
            }

            bstatus_t ServiceManager::ReleaseBiShareService() {
                if (!isServiceInitialized_.load()) {
                    return BS_OK; // 已经释放
                }

                bstatus_t result = bishare_service_release();
                
                if (result == BS_OK) {
                    isServiceInitialized_.store(false);
                }

                return result;
            }

            bool ServiceManager::InitializeDeviceService() {
                deviceService_ = std::make_shared<Domain::DeviceService>();
                return deviceService_->Initialize();
            }

            bool ServiceManager::InitializeRecordingService() {
                recordingService_ = std::make_shared<Domain::RecordingService>();
                return recordingService_->Initialize();
            }

            bool ServiceManager::InitializeNetworkService() {
                networkService_ = std::make_shared<Domain::NetworkService>();
                return networkService_->Initialize();
            }

        } // namespace Core
    } // namespace BiShare
} // namespace OHOS
