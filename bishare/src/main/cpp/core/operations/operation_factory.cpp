#include "operation_factory.h"
#include "../managers/service_manager.h"
#include "../managers/callback_manager.h"
#include "../../domain/device/device_service.h"
#include "../../domain/recording/recording_service.h"
#include "../../domain/network/network_service.h"

// 添加缺失的状态码定义
#ifndef BS_NOT_SUPPORTED
#define BS_NOT_SUPPORTED BS_NOT_FOUND
#endif

#ifndef BS_INVALID_PARAM
#define BS_INVALID_PARAM BS_PARAMS_ERROR
#endif

#ifndef BS_ALREADY_STARTED
#define BS_ALREADY_STARTED BS_OPS_ERROR
#endif

#ifndef BS_INIT_FAILED
#define BS_INIT_FAILED BS_NOT_INIT
#endif

#ifndef BS_CONNECTION_CLOSED
#define BS_CONNECTION_CLOSED BS_OPS_ERROR
#endif

namespace OHOS {
    namespace BiShare {
        namespace Core {

            // 具体操作类的前向声明和简单实现
            class InitServiceOperation : public IOperation {
            public:
                OperationResult Execute(const OperationParams& params) override {
                    // 这里应该调用ServiceManager的InitializeBiShareService方法
                    OperationResult result;
                    result.status = BS_OK;
                    result.stringResult = "Service initialized successfully";
                    return result;
                }

                OperationType GetType() const override { return OperationType::INIT_SERVICE; }
                std::string GetName() const override { return "InitService"; }
            };

            class ReleaseServiceOperation : public IOperation {
            public:
                OperationResult Execute(const OperationParams& params) override {
                    OperationResult result;
                    result.status = BS_OK;
                    result.stringResult = "Service released successfully";
                    return result;
                }

                OperationType GetType() const override { return OperationType::RELEASE_SERVICE; }
                std::string GetName() const override { return "ReleaseService"; }
            };

            // 设备操作类
            class SetDeviceInfoOperation : public IOperation {
            public:
                OperationResult Execute(const OperationParams& params) override {
                    OperationResult result;
                    result.status = BS_OK;
                    result.stringResult = "Device info set successfully";
                    return result;
                }

                OperationType GetType() const override { return OperationType::SET_DEVICE_INFO; }
                std::string GetName() const override { return "SetDeviceInfo"; }
            };

            OperationFactory::OperationFactory(std::shared_ptr<ServiceManager> serviceManager,
                                             std::shared_ptr<CallbackManager> callbackManager)
                : serviceManager_(serviceManager), callbackManager_(callbackManager) {
            }

            OperationFactory::~OperationFactory() {
            }

            std::unique_ptr<IOperation> OperationFactory::CreateOperation(OperationType type) {
                switch (type) {
                    case OperationType::INIT_SERVICE:
                        return std::make_unique<InitServiceOperation>();
                    case OperationType::RELEASE_SERVICE:
                        return std::make_unique<ReleaseServiceOperation>();
                    case OperationType::SET_DEVICE_INFO:
                        return std::make_unique<SetDeviceInfoOperation>();
                    // 可以继续添加其他操作类型
                    default:
                        return nullptr;
                }
            }

            OperationResult OperationFactory::ExecuteOperation(OperationType type, const OperationParams& params) {
                auto operation = CreateOperation(type);
                if (!operation) {
                    OperationResult result;
                    result.status = BS_NOT_SUPPORTED;
                    result.errorMessage = "Operation type not supported: " + GetOperationTypeName(type);
                    return result;
                }

                return operation->Execute(params);
            }

            OperationParams OperationFactory::ParseOperationParams(napi_env env, napi_callback_info info, OperationType type) {
                switch (type) {
                    case OperationType::INIT_SERVICE:
                        return ParseServiceOperationParams(env, info, type);
                    case OperationType::SET_DEVICE_INFO:
                        return ParseDeviceOperationParams(env, info, type);
                    case OperationType::START_SCREEN_RECORD:
                    case OperationType::STOP_SCREEN_RECORD:
                        return ParseRecordingOperationParams(env, info, type);
                    case OperationType::SET_NETWORK_INFO:
                        return ParseNetworkOperationParams(env, info, type);
                    case OperationType::ON_EVENT:
                    case OperationType::OFF_EVENT:
                        return ParseEventOperationParams(env, info, type);
                    default:
                        return OperationParams{}; // 返回默认参数
                }
            }

            napi_value OperationFactory::ConvertResultToNapi(napi_env env, const OperationResult& result, OperationType type) {
                napi_value resultObject;
                napi_create_object(env, &resultObject);

                // 设置状态码
                napi_value status;
                napi_create_int32(env, static_cast<int32_t>(result.status), &status);
                napi_set_named_property(env, resultObject, "status", status);

                // 设置错误消息（如果有）
                if (!result.errorMessage.empty()) {
                    napi_value errorMessage;
                    napi_create_string_utf8(env, result.errorMessage.c_str(), NAPI_AUTO_LENGTH, &errorMessage);
                    napi_set_named_property(env, resultObject, "error", errorMessage);
                }

                // 设置结果数据
                if (!result.stringResult.empty()) {
                    napi_value stringResult;
                    napi_create_string_utf8(env, result.stringResult.c_str(), NAPI_AUTO_LENGTH, &stringResult);
                    napi_set_named_property(env, resultObject, "data", stringResult);
                } else if (result.intResult != 0) {
                    napi_value intResult;
                    napi_create_int32(env, result.intResult, &intResult);
                    napi_set_named_property(env, resultObject, "data", intResult);
                } else {
                    napi_value boolResult;
                    napi_get_boolean(env, result.boolResult, &boolResult);
                    napi_set_named_property(env, resultObject, "data", boolResult);
                }

                return resultObject;
            }

            std::string OperationFactory::GetOperationTypeName(OperationType type) {
                switch (type) {
                    case OperationType::INIT_SERVICE: return "InitService";
                    case OperationType::RELEASE_SERVICE: return "ReleaseService";
                    case OperationType::SET_DEVICE_INFO: return "SetDeviceInfo";
                    case OperationType::SET_DEVICE_MODEL: return "SetDeviceModel";
                    case OperationType::GET_DEVICE_MODEL: return "GetDeviceModel";
                    case OperationType::RESET_DEVICE_MODEL: return "ResetDeviceModel";
                    case OperationType::START_DISCOVERY: return "StartDiscovery";
                    case OperationType::STOP_DISCOVERY: return "StopDiscovery";
                    case OperationType::CLEAR_DISCOVERED_DEVICES: return "ClearDiscoveredDevices";
                    case OperationType::GET_DISCOVERED_DEVICES: return "GetDiscoveredDevices";
                    case OperationType::FIND_REMOTE_DEVICE: return "FindRemoteDevice";
                    case OperationType::START_SCREEN_RECORD: return "StartScreenRecord";
                    case OperationType::STOP_SCREEN_RECORD: return "StopScreenRecord";
                    case OperationType::START_CAPTURE: return "StartCapture";
                    case OperationType::SET_SIZE: return "SetSize";
                    case OperationType::SET_DEFAULT_AUDIO_OUTPUT_DEVICE: return "SetDefaultAudioOutputDevice";
                    case OperationType::SCREENSHOT: return "Screenshot";
                    case OperationType::SET_NETWORK_INFO: return "SetNetworkInfo";
                    case OperationType::GET_ROOT_PATH: return "GetRootPath";
                    case OperationType::GET_CURRENT_DIRECTORY: return "GetCurrentDirectory";
                    case OperationType::ON_EVENT: return "OnEvent";
                    case OperationType::OFF_EVENT: return "OffEvent";
                    case OperationType::ONCE_EVENT: return "OnceEvent";
                    default: return "Unknown";
                }
            }

            OperationParams OperationFactory::ParseServiceOperationParams(napi_env env, napi_callback_info info, OperationType type) {
                OperationParams params;
                
                size_t argc = 5;
                napi_value argv[5];
                napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);

                if (type == OperationType::INIT_SERVICE && argc >= 3) {
                    // 解析初始化参数：isConsole, isFile, filePath, priority, callback
                    napi_get_value_bool(env, argv[0], &params.boolParam1); // isConsole
                    napi_get_value_bool(env, argv[1], &params.boolParam2); // isFile
                    
                    // 解析文件路径
                    size_t pathLength;
                    napi_get_value_string_utf8(env, argv[2], nullptr, 0, &pathLength);
                    if (pathLength > 0) {
                        std::vector<char> pathBuffer(pathLength + 1);
                        napi_get_value_string_utf8(env, argv[2], pathBuffer.data(), pathLength + 1, &pathLength);
                        params.stringParam1 = std::string(pathBuffer.data(), pathLength);
                    }

                    // 解析优先级（可选）
                    if (argc > 3) {
                        napi_valuetype valueType;
                        napi_typeof(env, argv[3], &valueType);
                        if (valueType == napi_number) {
                            int32_t priority;
                            napi_get_value_int32(env, argv[3], &priority);
                            params.logPriority = static_cast<log_priority_t>(priority);
                        }
                    }

                    // 解析回调（可选）
                    if (argc > 4) {
                        napi_valuetype valueType;
                        napi_typeof(env, argv[4], &valueType);
                        if (valueType == napi_function) {
                            napi_create_reference(env, argv[4], 1, &params.callback);
                        }
                    }
                }

                return params;
            }

            OperationParams OperationFactory::ParseDeviceOperationParams(napi_env env, napi_callback_info info, OperationType type) {
                OperationParams params;
                
                size_t argc = 3;
                napi_value argv[3];
                napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);

                if (type == OperationType::SET_DEVICE_INFO && argc >= 2) {
                    // 解析设备名称
                    size_t nameLength;
                    napi_get_value_string_utf8(env, argv[0], nullptr, 0, &nameLength);
                    if (nameLength > 0) {
                        std::vector<char> nameBuffer(nameLength + 1);
                        napi_get_value_string_utf8(env, argv[0], nameBuffer.data(), nameLength + 1, &nameLength);
                        params.stringParam1 = std::string(nameBuffer.data(), nameLength);
                    }

                    // 解析设备密码
                    size_t passwordLength;
                    napi_get_value_string_utf8(env, argv[1], nullptr, 0, &passwordLength);
                    if (passwordLength > 0) {
                        std::vector<char> passwordBuffer(passwordLength + 1);
                        napi_get_value_string_utf8(env, argv[1], passwordBuffer.data(), passwordLength + 1, &passwordLength);
                        params.stringParam2 = std::string(passwordBuffer.data(), passwordLength);
                    }

                    // 解析回调（可选）
                    if (argc > 2) {
                        napi_valuetype valueType;
                        napi_typeof(env, argv[2], &valueType);
                        if (valueType == napi_function) {
                            napi_create_reference(env, argv[2], 1, &params.callback);
                        }
                    }
                }

                return params;
            }

            OperationParams OperationFactory::ParseRecordingOperationParams(napi_env env, napi_callback_info info, OperationType type) {
                OperationParams params;
                
                size_t argc = 4;
                napi_value argv[4];
                napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);

                if ((type == OperationType::START_SCREEN_RECORD || type == OperationType::STOP_SCREEN_RECORD) && argc >= 3) {
                    // 解析会话ID
                    napi_get_value_int32(env, argv[0], &params.intParam1);
                    
                    // 解析显示ID
                    napi_get_value_int32(env, argv[1], &params.intParam2);
                    
                    // 解析方向
                    napi_get_value_int32(env, argv[2], &params.intParam3);

                    // 解析回调（可选）
                    if (argc > 3) {
                        napi_valuetype valueType;
                        napi_typeof(env, argv[3], &valueType);
                        if (valueType == napi_function) {
                            napi_create_reference(env, argv[3], 1, &params.callback);
                        }
                    }
                }

                return params;
            }

            OperationParams OperationFactory::ParseNetworkOperationParams(napi_env env, napi_callback_info info, OperationType type) {
                OperationParams params;
                
                size_t argc = 4;
                napi_value argv[4];
                napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);

                if (type == OperationType::SET_NETWORK_INFO && argc >= 3) {
                    // 解析网络类型
                    int32_t networkType;
                    napi_get_value_int32(env, argv[0], &networkType);
                    params.networkType = static_cast<network_type_t>(networkType);

                    // 解析地址
                    size_t addrLength;
                    napi_get_value_string_utf8(env, argv[1], nullptr, 0, &addrLength);
                    if (addrLength > 0) {
                        std::vector<char> addrBuffer(addrLength + 1);
                        napi_get_value_string_utf8(env, argv[1], addrBuffer.data(), addrLength + 1, &addrLength);
                        params.stringParam1 = std::string(addrBuffer.data(), addrLength);
                    }

                    // 解析MAC地址
                    size_t macLength;
                    napi_get_value_string_utf8(env, argv[2], nullptr, 0, &macLength);
                    if (macLength > 0) {
                        std::vector<char> macBuffer(macLength + 1);
                        napi_get_value_string_utf8(env, argv[2], macBuffer.data(), macLength + 1, &macLength);
                        params.stringParam2 = std::string(macBuffer.data(), macLength);
                    }

                    // 解析回调（可选）
                    if (argc > 3) {
                        napi_valuetype valueType;
                        napi_typeof(env, argv[3], &valueType);
                        if (valueType == napi_function) {
                            napi_create_reference(env, argv[3], 1, &params.callback);
                        }
                    }
                }

                return params;
            }

            OperationParams OperationFactory::ParseEventOperationParams(napi_env env, napi_callback_info info, OperationType type) {
                OperationParams params;
                
                size_t argc = 2;
                napi_value argv[2];
                napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);

                if (argc >= 2) {
                    // 解析事件类型
                    napi_get_value_int32(env, argv[0], &params.intParam1);

                    // 解析回调函数
                    napi_valuetype valueType;
                    napi_typeof(env, argv[1], &valueType);
                    if (valueType == napi_function) {
                        napi_create_reference(env, argv[1], 1, &params.callback);
                    }
                }

                return params;
            }

        } // namespace Core
    } // namespace BiShare
} // namespace OHOS
