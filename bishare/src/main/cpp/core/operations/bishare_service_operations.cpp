#include "bishare_operation_impls.h"
#include "bishare_napi.h"
#include "bishare_logger.h"
#include "bishare_utils.h"
#include "bishare_callbacks.h"
#include "bishare-service.h"


namespace OHOS {
    namespace BiShare {

        static constexpr const char *SERVICE_OPS_TAG = "BiShareServiceOps";

        // InitializeOperation 实现

        bool InitializeOperation::ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) {
            size_t argc = 5;
            napi_value argv[5];
            napi_value thisArg;
            void *data;
            
            napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);
            
            if (argc < 4) {
                BiShareLogger::Error(SERVICE_OPS_TAG, "Initialize参数数量不足，需要4个参数");
                return false;
            }

            // 解析isConsole参数
            bool isConsole;
            napi_get_value_bool(env, argv[0], &isConsole);
            workData->data.boolParam1 = isConsole;

            // 解析isFile参数
            bool isFile;
            napi_get_value_bool(env, argv[1], &isFile);
            workData->data.boolParam2 = isFile;

            // 解析logPath参数
            size_t logPathLength;
            napi_get_value_string_utf8(env, argv[2], nullptr, 0, &logPathLength);
            char* logPathBuffer = new char[logPathLength + 1];
            napi_get_value_string_utf8(env, argv[2], logPathBuffer, logPathLength + 1, &logPathLength);
            workData->data.stringParam1 = std::string(logPathBuffer);
            delete[] logPathBuffer;

            // 解析priority参数
            int32_t priority;
            napi_get_value_int32(env, argv[3], &priority);
            workData->data.priority = static_cast<log_priority_t>(priority);

            // 如果有第5个参数，则为回调函数
            if (argc >= 5) {
                napi_valuetype valueType;
                napi_typeof(env, argv[4], &valueType);
                if (valueType == napi_function) {
                    napi_create_reference(env, argv[4], 1, &workData->callbackRef);
                }
            }

            return true;
        }

        void InitializeOperation::ExecuteOperation(napi_env env, AsyncWorkData* workData) {
            // 检查是否已初始化
            if (BiShareNapi::IsInitialized()) {
                BiShareLogger::Info(SERVICE_OPS_TAG, "BiShare服务已经初始化过了");
                workData->result = BS_OK;
                workData->successMessage = "服务已经初始化";
                return;
            }

            // 转换参数
            bool_type_t isConsole = workData->data.boolParam1 ? BOOL_TRUE : BOOL_FALSE;
            bool_type_t isFile = workData->data.boolParam2 ? BOOL_TRUE : BOOL_FALSE;

            BiShareLogger::Info(SERVICE_OPS_TAG, "开始初始化BiShare服务...");
            BiShareLogger::Info(SERVICE_OPS_TAG, "控制台日志: %s", isConsole == BOOL_TRUE ? "启用" : "禁用");
            BiShareLogger::Info(SERVICE_OPS_TAG, "文件日志: %s", isFile == BOOL_TRUE ? "启用" : "禁用");
            BiShareLogger::Info(SERVICE_OPS_TAG, "日志路径: %s", workData->data.stringParam1.c_str());
            BiShareLogger::Info(SERVICE_OPS_TAG, "日志优先级: %d", workData->data.priority);

            // 调用原生初始化函数
            workData->result = bishare_service_init(isConsole, isFile, 
                workData->data.stringParam1.c_str(), workData->data.priority);

            BiShareLogger::Info(SERVICE_OPS_TAG, "初始化结果: %d", static_cast<int>(workData->result));

            // 如果初始化成功，注册回调并设置状态
            if (workData->result == BS_OK) {
                BiShareNapi::SetInitialized(true);
                
                // 注册回调
                bishare_service_register_event_callback(BiShareCallbacks::OnEventCallback);
                bishare_service_register_packet_callback(BiShareCallbacks::OnPacketCallback);
                
                workData->successMessage = "BiShare服务初始化成功";
                BiShareLogger::Info(SERVICE_OPS_TAG, "BiShare服务初始化成功，回调已注册");
            } else {
                workData->errorMessage = std::string("BiShare服务初始化失败: ") + 
                    std::string(err2str(workData->result));
                BiShareLogger::Error(SERVICE_OPS_TAG, "BiShare服务初始化失败: %s", 
                    err2str(workData->result));
            }
        }

        napi_value InitializeOperation::CreateResult(napi_env env, AsyncWorkData* workData) {
            return CreateStandardResult(env, workData);
        }

        // ReleaseOperation 实现

        bool ReleaseOperation::ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) {
            size_t argc = 1;
            napi_value argv[1];
            napi_value thisArg;
            void *data;
            
            napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);
            
            // 如果有回调函数参数
            if (argc >= 1) {
                napi_valuetype valueType;
                napi_typeof(env, argv[0], &valueType);
                if (valueType == napi_function) {
                    napi_create_reference(env, argv[0], 1, &workData->callbackRef);
                }
            }

            return true;
        }

        void ReleaseOperation::ExecuteOperation(napi_env env, AsyncWorkData* workData) {
            // 检查是否已初始化
            if (!CheckServiceInitialized(workData)) {
                return;
            }

            BiShareLogger::Info(SERVICE_OPS_TAG, "开始释放BiShare服务...");

            // 调用原生释放函数
            workData->result = bishare_service_release();

            if (workData->result == BS_OK) {
                BiShareNapi::SetInitialized(false);
                workData->successMessage = "BiShare服务释放成功";
                BiShareLogger::Info(SERVICE_OPS_TAG, "BiShare服务释放成功");
            } else {
                workData->errorMessage = std::string("BiShare服务释放失败: ") + 
                    std::string(err2str(workData->result));
                BiShareLogger::Error(SERVICE_OPS_TAG, "BiShare服务释放失败: %s", 
                    err2str(workData->result));
            }
        }

        napi_value ReleaseOperation::CreateResult(napi_env env, AsyncWorkData* workData) {
            return CreateStandardResult(env, workData);
        }

        // OnEventOperation 实现

        napi_value OnEventOperation::Execute(napi_env env, napi_callback_info info) {
            // 这是一个同步操作，直接处理事件监听注册
            size_t argc = 2;
            napi_value argv[2];
            napi_value thisArg;
            void *data;
            
            napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);
            
            if (argc < 2) {
                napi_value error, message;
                napi_create_string_utf8(env, "OnEvent需要2个参数：事件类型和回调函数", NAPI_AUTO_LENGTH, &message);
                napi_create_error(env, nullptr, message, &error);
                return error;
            }

            // 获取事件类型
            int32_t eventType;
            napi_get_value_int32(env, argv[0], &eventType);

            // 检查回调函数
            napi_valuetype valueType;
            napi_typeof(env, argv[1], &valueType);
            if (valueType != napi_function) {
                napi_value error, message;
                napi_create_string_utf8(env, "第二个参数必须是回调函数", NAPI_AUTO_LENGTH, &message);
                napi_create_error(env, nullptr, message, &error);
                return error;
            }

            // 注册事件回调
            auto callbacks = BiShareNapi::GetInstance()->GetCallbacks();
            napi_ref callbackRef;
            bool success = callbacks->RegisterEventCallback(env, argv[1], eventType, &callbackRef);

            if (success) {
                BiShareLogger::Info(SERVICE_OPS_TAG, "事件监听注册成功，类型: %d", eventType);
                napi_value result;
                napi_get_undefined(env, &result);
                return result;
            } else {
                napi_value error, message;
                napi_create_string_utf8(env, "事件监听注册失败", NAPI_AUTO_LENGTH, &message);
                napi_create_error(env, nullptr, message, &error);
                return error;
            }
        }

        bool OnEventOperation::ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) {
            // 这个方法在同步执行中不会被调用
            return true;
        }

        void OnEventOperation::ExecuteOperation(napi_env env, AsyncWorkData* workData) {
            // 这个方法在同步执行中不会被调用
        }

        napi_value OnEventOperation::CreateResult(napi_env env, AsyncWorkData* workData) {
            // 这个方法在同步执行中不会被调用
            napi_value result;
            napi_get_undefined(env, &result);
            return result;
        }

        // OffEventOperation 实现

        napi_value OffEventOperation::Execute(napi_env env, napi_callback_info info) {
            // 这是一个同步操作，直接处理事件监听注销
            size_t argc = 2;
            napi_value argv[2];
            napi_value thisArg;
            void *data;

            napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);

            if (argc < 2) {
                napi_value error, message;
                napi_create_string_utf8(env, "OffEvent需要2个参数：事件类型和回调函数", NAPI_AUTO_LENGTH, &message);
                napi_create_error(env, nullptr, message, &error);
                return error;
            }

            // 解析事件类型
            int32_t eventType;
            napi_valuetype valueType;
            napi_typeof(env, argv[0], &valueType);
            if (valueType != napi_number) {
                napi_value error, message;
                napi_create_string_utf8(env, "第一个参数必须是数字类型的事件类型", NAPI_AUTO_LENGTH, &message);
                napi_create_error(env, nullptr, message, &error);
                return error;
            }
            napi_get_value_int32(env, argv[0], &eventType);

            // 验证回调函数
            napi_typeof(env, argv[1], &valueType);
            if (valueType != napi_function) {
                napi_value error, message;
                napi_create_string_utf8(env, "第二个参数必须是回调函数", NAPI_AUTO_LENGTH, &message);
                napi_create_error(env, nullptr, message, &error);
                return error;
            }

            // 注销事件回调
            auto callbacks = BiShareNapi::GetInstance()->GetCallbacks();
            napi_ref callbackRef;
            napi_create_reference(env, argv[1], 1, &callbackRef);
            bool success = callbacks->UnregisterEventCallback(env, callbackRef, eventType);

            if (success) {
                BiShareLogger::Info(SERVICE_OPS_TAG, "事件监听注销成功，类型: %d", eventType);
                napi_value result;
                napi_get_undefined(env, &result);
                return result;
            } else {
                napi_value error, message;
                napi_create_string_utf8(env, "事件监听注销失败", NAPI_AUTO_LENGTH, &message);
                napi_create_error(env, nullptr, message, &error);
                return error;
            }
        }

        bool OffEventOperation::ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) {
            // 这个方法在同步执行中不会被调用
            return true;
        }

        void OffEventOperation::ExecuteOperation(napi_env env, AsyncWorkData* workData) {
            // 这个方法在同步执行中不会被调用
        }

        napi_value OffEventOperation::CreateResult(napi_env env, AsyncWorkData* workData) {
            // 这个方法在同步执行中不会被调用
            napi_value result;
            napi_get_undefined(env, &result);
            return result;
        }

        // OnceEventOperation 实现

        napi_value OnceEventOperation::Execute(napi_env env, napi_callback_info info) {
            // 这是一个同步操作，直接处理一次性事件监听注册
            size_t argc = 2;
            napi_value argv[2];
            napi_value thisArg;
            void *data;

            napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);

            if (argc < 2) {
                napi_value error, message;
                napi_create_string_utf8(env, "OnceEvent需要2个参数：事件类型和回调函数", NAPI_AUTO_LENGTH, &message);
                napi_create_error(env, nullptr, message, &error);
                return error;
            }

            // 解析事件类型
            int32_t eventType;
            napi_valuetype valueType;
            napi_typeof(env, argv[0], &valueType);
            if (valueType != napi_number) {
                napi_value error, message;
                napi_create_string_utf8(env, "第一个参数必须是数字类型的事件类型", NAPI_AUTO_LENGTH, &message);
                napi_create_error(env, nullptr, message, &error);
                return error;
            }
            napi_get_value_int32(env, argv[0], &eventType);

            // 验证回调函数
            napi_typeof(env, argv[1], &valueType);
            if (valueType != napi_function) {
                napi_value error, message;
                napi_create_string_utf8(env, "第二个参数必须是回调函数", NAPI_AUTO_LENGTH, &message);
                napi_create_error(env, nullptr, message, &error);
                return error;
            }

            // 注册一次性事件回调（使用普通事件回调，但标记为一次性）
            auto callbacks = BiShareNapi::GetInstance()->GetCallbacks();
            napi_ref callbackRef;
            bool success = callbacks->RegisterEventCallback(env, argv[1], eventType, &callbackRef);

            if (success) {
                BiShareLogger::Info(SERVICE_OPS_TAG, "一次性事件监听注册成功，类型: %d", eventType);
                napi_value result;
                napi_get_undefined(env, &result);
                return result;
            } else {
                napi_value error, message;
                napi_create_string_utf8(env, "一次性事件监听注册失败", NAPI_AUTO_LENGTH, &message);
                napi_create_error(env, nullptr, message, &error);
                return error;
            }
        }

        bool OnceEventOperation::ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) {
            // 这个方法在同步执行中不会被调用
            return true;
        }

        void OnceEventOperation::ExecuteOperation(napi_env env, AsyncWorkData* workData) {
            // 这个方法在同步执行中不会被调用
        }

        napi_value OnceEventOperation::CreateResult(napi_env env, AsyncWorkData* workData) {
            // 这个方法在同步执行中不会被调用
            napi_value result;
            napi_get_undefined(env, &result);
            return result;
        }

    } // namespace BiShare
} // namespace OHOS
