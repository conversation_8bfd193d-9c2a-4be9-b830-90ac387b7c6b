#include "bishare_operations.h"
#include "../../interfaces/napi/bishare_napi.h"
#include "../../infrastructure/logging/bishare_logger.h"
#include "../../domain/device/bishare_device.h"
#include "../../domain/recording/bishare_recording.h"
#include "../../infrastructure/bishare_utils.h"
#include "../../types/bishare_status_codes.h"

#include <napi/native_api.h>

namespace OHOS {
    namespace BiShare {

        static constexpr const char *OPERATIONS_TAG = "BiShareOperations";

        // 工厂类静态成员初始化
        std::map<BiShareOperationFactory::OperationType, BiShareOperationFactory::OperationCreator> 
            BiShareOperationFactory::creators_;

        // 创建字符串辅助函数
        napi_value CreateStringUtf8(napi_env env, const char *str, size_t length = NAPI_AUTO_LENGTH) {
            napi_value result;
            napi_create_string_utf8(env, str, length, &result);
            return result;
        }

        // 创建整数辅助函数
        napi_value CreateInt32(napi_env env, int32_t value) {
            napi_value result;
            napi_create_int32(env, value, &result);
            return result;
        }

        // 创建布尔值辅助函数
        napi_value CreateBoolean(napi_env env, bool value) {
            napi_value result;
            napi_get_boolean(env, value, &result);
            return result;
        }

        // BiShareAsyncOperation 实现

        napi_value BiShareAsyncOperation::Execute(napi_env env, napi_callback_info info) {
            // 创建工作数据
            auto workData = std::make_unique<AsyncWorkData>();
            workData->env = env;
            workData->workName = operationName_;

            // 解析参数
            if (!ParseArguments(env, info, workData.get())) {
                napi_value error, message;
                napi_create_string_utf8(env, "参数解析失败", NAPI_AUTO_LENGTH, &message);
                napi_create_error(env, nullptr, message, &error);
                return error;
            }

            // 验证参数
            if (!ValidateArguments(workData.get())) {
                napi_value error, message;
                napi_create_string_utf8(env, "参数验证失败", NAPI_AUTO_LENGTH, &message);
                napi_create_error(env, nullptr, message, &error);
                return error;
            }

            // 创建异步工作
            return CreateAsyncWork(env, workData.release(), ExecuteCallback, CompleteCallback);
        }

        napi_value BiShareAsyncOperation::CreateAsyncWork(napi_env env, AsyncWorkData* workData,
                                                        napi_async_execute_callback execute,
                                                        napi_async_complete_callback complete) {
            napi_value promise;
            napi_value resourceName;
            
            // 创建Promise
            napi_create_promise(env, &workData->deferred, &promise);
            
            // 创建资源名称
            napi_create_string_utf8(env, workData->workName.c_str(), NAPI_AUTO_LENGTH, &resourceName);
            
            // 创建异步工作
            napi_create_async_work(env, nullptr, resourceName, execute, complete, workData, &workData->work);
            
            // 排队异步工作
            napi_queue_async_work(env, workData->work);
            
            return promise;
        }

        void BiShareAsyncOperation::ExecuteCallback(napi_env env, void* data) {
            AsyncWorkData* workData = static_cast<AsyncWorkData*>(data);

            try {
                BiShareLogger::Info(OPERATIONS_TAG, "开始执行操作: %s", workData->workName.c_str());
                // 这里应该调用具体操作的ExecuteOperation方法
                // 但由于静态回调的限制，我们在这里只做基本的日志记录
                workData->result = BS_OK;

            } catch (const std::exception& e) {
                workData->result = BS_ERROR;
                workData->errorMessage = std::string("操作执行异常: ") + e.what();
                BiShareLogger::Error(OPERATIONS_TAG, "操作执行异常: %s", e.what());
            }
        }

        void BiShareAsyncOperation::CompleteCallback(napi_env env, napi_status status, void* data) {
            AsyncWorkData* workData = static_cast<AsyncWorkData*>(data);
            
            napi_value result;
            if (workData->result == BS_OK) {
                // 成功情况
                napi_create_object(env, &result);
                napi_value successValue = CreateBoolean(env, true);
                napi_value messageValue = CreateStringUtf8(env, workData->successMessage.empty() ? 
                    "操作成功" : workData->successMessage.c_str());
                
                napi_set_named_property(env, result, "success", successValue);
                napi_set_named_property(env, result, "message", messageValue);
                
                napi_resolve_deferred(env, workData->deferred, result);
            } else {
                // 失败情况
                napi_value error, message;
                std::string errorMsg = workData->errorMessage.empty() ? 
                    std::string("操作失败，错误码: ") + std::to_string(workData->result) : 
                    workData->errorMessage;
                
                napi_create_string_utf8(env, errorMsg.c_str(), NAPI_AUTO_LENGTH, &message);
                napi_create_error(env, nullptr, message, &error);
                napi_reject_deferred(env, workData->deferred, error);
            }
            
            // 清理资源
            napi_delete_async_work(env, workData->work);
            // 使用智能指针管理，不需要手动delete
            // delete workData;
        }

        // BiShareServiceOperation 实现

        bool BiShareServiceOperation::CheckServiceInitialized(AsyncWorkData* workData) {
            if (!BiShareNapi::IsInitialized()) {
                workData->result = BS_NOT_INIT;
                workData->errorMessage = "BiShare服务未初始化";
                return false;
            }
            return true;
        }

        napi_value BiShareServiceOperation::CreateStandardResult(napi_env env, AsyncWorkData* workData) {
            napi_value result;
            napi_create_object(env, &result);
            
            napi_value successValue = CreateBoolean(env, workData->result == BS_OK);
            napi_value codeValue = CreateInt32(env, static_cast<int32_t>(workData->result));
            napi_value messageValue = CreateStringUtf8(env, 
                workData->result == BS_OK ? "操作成功" : workData->errorMessage.c_str());
            
            napi_set_named_property(env, result, "success", successValue);
            napi_set_named_property(env, result, "code", codeValue);
            napi_set_named_property(env, result, "message", messageValue);
            
            return result;
        }

        // BiShareDeviceOperation 实现

        std::shared_ptr<BiShareDeviceManager> BiShareDeviceOperation::GetDeviceManager() {
            return BiShareNapi::GetInstance()->GetDeviceManager();
        }

        // BiShareRecordOperation 实现

        std::shared_ptr<BiShareRecordManager> BiShareRecordOperation::GetRecordManager() {
            return BiShareNapi::GetInstance()->GetRecordManager();
        }

        // BiShareOperationFactory 实现

        std::unique_ptr<BiShareAsyncOperation> BiShareOperationFactory::CreateOperation(OperationType type) {
            auto it = creators_.find(type);
            if (it != creators_.end()) {
                return it->second();
            }
            
            BiShareLogger::Error(OPERATIONS_TAG, "未找到操作类型: %d", static_cast<int>(type));
            return nullptr;
        }

        void BiShareOperationFactory::RegisterOperationCreator(OperationType type, OperationCreator creator) {
            creators_[type] = creator;
            BiShareLogger::Info(OPERATIONS_TAG, "注册操作创建器，类型: %d", static_cast<int>(type));
        }

    } // namespace BiShare
} // namespace OHOS
