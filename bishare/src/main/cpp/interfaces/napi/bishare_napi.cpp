#include "bishare-define.h"
#include "bishare_callbacks.h"
#include "bishare_logger.h"
#include "bishare_device.h"
#include "bishare_napi.h"
#include "bishare_recording.h"
#include "bishare_utils.h"
#include "bishare_operations.h"
#include "bishare_operation_impls.h"

#include <napi/native_api.h>
#include <string>
#include <vector>
#include <map>
#include <memory>
#include <unistd.h>
#include <functional>

static constexpr const char *BISHARE_MODULE_NAME = "bishare_napi";

namespace OHOS {
    namespace BiShare {

        // 定义日志标签
        static constexpr const char *NAPI_TAG = "BiShareNapi";

        // 初始化静态成员
        std::unique_ptr<BiShareNapi> BiShareNapi::instance_ = nullptr;
        std::atomic<bool> BiShareNapi::isInitialized_(false);

        // 创建字符串辅助函数
        napi_value CreateStringUtf8(napi_env env, const char *str, size_t length = NAPI_AUTO_LENGTH) {
            napi_value result;
            napi_create_string_utf8(env, str, length, &result);
            return result;
        }

        // 创建整数辅助函数
        napi_value CreateInt32(napi_env env, int32_t value) {
            napi_value result;
            napi_create_int32(env, value, &result);
            return result;
        }

        // 使用工厂模式的NAPI方法包装器
        template<BiShareOperationFactory::OperationType OpType>
        napi_value ExecuteOperation(napi_env env, napi_callback_info info) {
            auto operation = BiShareOperationFactory::CreateOperation(OpType);
            if (!operation) {
                napi_value error, message;
                napi_create_string_utf8(env, "无法创建操作实例", NAPI_AUTO_LENGTH, &message);
                napi_create_error(env, nullptr, message, &error);
                return error;
            }
            return operation->Execute(env, info);
        }

        // 异步工作数据的基础结构
        struct AsyncWorkData {
            napi_env env;
            napi_deferred deferred;
            napi_async_work work;
            napi_ref callback;
            bstatus_t result;
            std::string errorMessage;
            void *customData; // 用于存储自定义数据

            // 所有可能的数据字段
            struct {
                // 通用字符串参数
                std::string stringParam1;
                std::string stringParam2;
                std::string stringParam3;

                // 通用整数参数
                int intParam1 = 0;
                int intParam2 = 0;
                int intParam3 = 0;
                int intParam4 = 0;

                // 通用布尔参数
                bool boolParam1 = false;
                bool boolParam2 = false;

                // 通用长整型参数
                long longParam1 = 0;
                long longParam2 = 0;
                long longParam3 = 0;
                long longParam4 = 0;

                // 特殊类型参数
                bool_type_t enableFlag = BOOL_FALSE;
                log_priority_t priority = LOG_INFO;
                network_type_t networkType = NotNetwork;
            } data;

            AsyncWorkData(napi_env e)
                : env(e), deferred(nullptr), work(nullptr), callback(nullptr), result(BS_NOT_INIT), errorMessage(""),
                  customData(nullptr) {}

            ~AsyncWorkData() {
                if (work != nullptr) {
                    napi_delete_async_work(env, work);
                }
                if (callback != nullptr) {
                    napi_delete_reference(env, callback);
                }
            }
        };

        // 用于存储异步回调的结构
        using ExecuteCallback = std::function<void(napi_env, AsyncWorkData *)>;
        using CompleteCallback = std::function<void(napi_env, napi_status, AsyncWorkData *)>;
        using ParseCallback = std::function<bool(napi_env, napi_callback_info, AsyncWorkData **)>;

        // 标准完成回调
        void StandardCompleteCallback(napi_env env, napi_status status, AsyncWorkData *workData) {
            if (workData->callback != nullptr) {
                napi_value callback, result, global;
                napi_get_reference_value(env, workData->callback, &callback);
                napi_get_global(env, &global);

                if (workData->result == BS_OK) {
                    napi_value args[2];
                    napi_get_null(env, &args[0]); // error = null
                    napi_create_int32(env, static_cast<int32_t>(workData->result), &args[1]); // result
                    napi_call_function(env, global, callback, 2, args, &result);
                } else {
                    napi_value args[2];
                    napi_create_string_utf8(env, workData->errorMessage.c_str(), NAPI_AUTO_LENGTH, &args[0]); // error
                    napi_get_null(env, &args[1]); // result = null
                    napi_call_function(env, global, callback, 2, args, &result);
                }
            }
            delete workData;
        }

        // 创建异步工作的通用函数
        napi_value CreateAsyncWork(napi_env env, napi_callback_info info, const char *resourceName,
                                   ExecuteCallback executeCallback, CompleteCallback completeCallback,
                                   ParseCallback parseCallback) {
            AsyncWorkData *workData = nullptr;

            // 解析参数
            if (!parseCallback(env, info, &workData)) {
                return nullptr;
            }

            // 创建异步工作
            napi_value resourceNameValue;
            napi_create_string_utf8(env, resourceName, NAPI_AUTO_LENGTH, &resourceNameValue);

            napi_create_async_work(
                env, nullptr, resourceNameValue,
                // 执行函数
                [](napi_env env, void *data) {
                    AsyncWorkData *workData = static_cast<AsyncWorkData *>(data);
                    // 这里会调用传入的执行回调
                    auto executeCallback = *static_cast<ExecuteCallback *>(workData->customData);
                    executeCallback(env, workData);
                },
                // 完成函数
                [](napi_env env, napi_status status, void *data) {
                    AsyncWorkData *workData = static_cast<AsyncWorkData *>(data);
                    auto completeCallback = *static_cast<CompleteCallback *>(workData->customData + sizeof(ExecuteCallback));
                    completeCallback(env, status, workData);
                },
                workData, &workData->work);

            // 存储回调函数
            workData->customData = new char[sizeof(ExecuteCallback) + sizeof(CompleteCallback)];
            new (workData->customData) ExecuteCallback(executeCallback);
            new (static_cast<char *>(workData->customData) + sizeof(ExecuteCallback)) CompleteCallback(completeCallback);

            // 队列异步工作
            napi_queue_async_work(env, workData->work);

            // 返回 undefined
            napi_value result;
            napi_get_undefined(env, &result);
            return result;
        }

        // 注册所有操作类型
        void RegisterAllOperations() {
            REGISTER_OPERATION(INITIALIZE, InitializeOperation);
            REGISTER_OPERATION(RELEASE, ReleaseOperation);
            REGISTER_OPERATION(DISCOVER_DEVICES, DiscoverDevicesOperation);
            REGISTER_OPERATION(GET_DISCOVERED_DEVICES, GetDiscoveredDevicesOperation);
            REGISTER_OPERATION(SET_DEVICE_MODEL, SetDeviceModelOperation);
            REGISTER_OPERATION(GET_DEVICE_MODEL, GetDeviceModelOperation);
            REGISTER_OPERATION(RESET_DEVICE_MODEL, ResetDeviceModelOperation);
            REGISTER_OPERATION(SET_DEVICE_INFO, SetDeviceInfoOperation);
            REGISTER_OPERATION(FIND_REMOTE_DEVICE, FindRemoteDeviceOperation);
            REGISTER_OPERATION(START_CAPTURE, StartCaptureOperation);
            REGISTER_OPERATION(SET_SIZE, SetSizeOperation);
            REGISTER_OPERATION(SCREENSHOT, ScreenshotOperation);
            REGISTER_OPERATION(START_SCREEN_RECORD, StartScreenRecordOperation);
            REGISTER_OPERATION(STOP_SCREEN_RECORD, StopScreenRecordOperation);
            REGISTER_OPERATION(SET_DEFAULT_AUDIO_OUTPUT_DEVICE, SetDefaultAudioOutputDeviceOperation);
            REGISTER_OPERATION(GET_ROOT_PATH, GetRootPathOperation);
            REGISTER_OPERATION(GET_CURRENT_DIRECTOR, GetCurrentDirectorOperation);
            REGISTER_OPERATION(ON_EVENT, OnEventOperation);
        }

        // NAPI 模块初始化
        napi_value BiShareNapi::Init(napi_env env, napi_value exports) {
            // 注册所有操作
            RegisterAllOperations();

            // 定义常量和方法的属性描述数组
            napi_property_descriptor properties[] = {
                // 版本常量
                {"VERSION", nullptr, nullptr, nullptr, nullptr, CreateStringUtf8(env, VERSION),
                 napi_default, nullptr},

                // 日志优先级常量
                {"LOG_PRIORITY_EMERG", nullptr, nullptr, nullptr, nullptr, CreateInt32(env, LOG_EMERG), napi_default,
                 nullptr},
                {"LOG_PRIORITY_FATAL", nullptr, nullptr, nullptr, nullptr, CreateInt32(env, LOG_FATAL), napi_default,
                 nullptr},
                {"LOG_PRIORITY_ALERT", nullptr, nullptr, nullptr, nullptr, CreateInt32(env, LOG_ALERT), napi_default,
                 nullptr},
                {"LOG_PRIORITY_CRIT", nullptr, nullptr, nullptr, nullptr, CreateInt32(env, LOG_CRIT), napi_default,
                 nullptr},
                {"LOG_PRIORITY_ERROR", nullptr, nullptr, nullptr, nullptr, CreateInt32(env, LOG_ERROR), napi_default,
                 nullptr},
                {"LOG_PRIORITY_WARN", nullptr, nullptr, nullptr, nullptr, CreateInt32(env, LOG_WARN), napi_default,
                 nullptr},
                {"LOG_PRIORITY_NOTICE", nullptr, nullptr, nullptr, nullptr, CreateInt32(env, LOG_NOTICE), napi_default,
                 nullptr},
                {"LOG_PRIORITY_INFO", nullptr, nullptr, nullptr, nullptr, CreateInt32(env, LOG_INFO), napi_default,
                 nullptr},
                {"LOG_PRIORITY_DEBUG", nullptr, nullptr, nullptr, nullptr, CreateInt32(env, LOG_DEBUG), napi_default,
                 nullptr},

                // 事件类型常量
                {"EVENT_DEVICE_INFO", nullptr, nullptr, nullptr, nullptr, CreateInt32(env, EVENT_DEVICE_INFO),
                 napi_default, nullptr},
                {"EVENT_DEVICE_STATUS", nullptr, nullptr, nullptr, nullptr, CreateInt32(env, EVENT_DEVICE_STATUS),
                 napi_default, nullptr},
                {"EVENT_CONNECT_STATUS", nullptr, nullptr, nullptr, nullptr, CreateInt32(env, EVENT_CONNECT_STATUS),
                 napi_default, nullptr},
                {"EVENT_KEY_VALUE", nullptr, nullptr, nullptr, nullptr, CreateInt32(env, EVENT_KEY_VALUE), napi_default,
                 nullptr},
                {"EVENT_ERROR_INFO", nullptr, nullptr, nullptr, nullptr, CreateInt32(env, EVENT_ERROR_INFO),
                 napi_default, nullptr},
                {"EVENT_MEDIA_CENTER", nullptr, nullptr, nullptr, nullptr, CreateInt32(env, EVENT_MEDIA_CENTER),
                 napi_default, nullptr},
                {"EVENT_SCREEN_RECORD", nullptr, nullptr, nullptr, nullptr, CreateInt32(env, EVENT_SCREEN_RECORD),
                 napi_default, nullptr},
                {"EVENT_FILE_ACTION", nullptr, nullptr, nullptr, nullptr, CreateInt32(env, EVENT_FILE_ACTION),
                 napi_default, nullptr},
                {"EVENT_WFD_EVENT", nullptr, nullptr, nullptr, nullptr, CreateInt32(env, EVENT_WFD_EVENT), napi_default,
                 nullptr},
                {"EVENT_ABILITY_ACTION", nullptr, nullptr, nullptr, nullptr, CreateInt32(env, EVENT_ABILITY_ACTION),
                 napi_default, nullptr},
                {"EVENT_DEVICE_INFO_LIST", nullptr, nullptr, nullptr, nullptr, CreateInt32(env, EVENT_DEVICE_INFO_LIST),
                 napi_default, nullptr},
                {"EVENT_WINDOWS_INFO_LIST", nullptr, nullptr, nullptr, nullptr,
                 CreateInt32(env, EVENT_WINDOWS_INFO_LIST), napi_default, nullptr},
                {"EVENT_MONITORS_INFO_LIST", nullptr, nullptr, nullptr, nullptr,
                 CreateInt32(env, EVENT_MONITORS_INFO_LIST), napi_default, nullptr},
                {"EVENT_LOG_INFO", nullptr, nullptr, nullptr, nullptr, CreateInt32(env, EVENT_LOG_INFO), napi_default,
                 nullptr},

                // 方向常量
                {"DIRECTION_NULL", nullptr, nullptr, nullptr, nullptr, CreateInt32(env, DIR_NULL), napi_default,
                 nullptr},
                {"DIRECTION_SEND", nullptr, nullptr, nullptr, nullptr, CreateInt32(env, DIR_SEND), napi_default,
                 nullptr},
                {"DIRECTION_RECV", nullptr, nullptr, nullptr, nullptr, CreateInt32(env, DIR_RECV), napi_default,
                 nullptr},

                // 缓冲区类型常量
                {"BUFFER_TYPE_VIDEO", nullptr, nullptr, nullptr, nullptr, CreateInt32(env, TYPE_VIDEO), napi_default,
                 nullptr},
                {"BUFFER_TYPE_AUDIO", nullptr, nullptr, nullptr, nullptr, CreateInt32(env, TYPE_AUDIO), napi_default,
                 nullptr},

                // API 方法定义 - 使用工厂模式
                {"initialize", nullptr, ExecuteOperation<BiShareOperationFactory::OperationType::INITIALIZE>, nullptr, nullptr, nullptr, napi_default, nullptr},
                {"release", nullptr, ExecuteOperation<BiShareOperationFactory::OperationType::RELEASE>, nullptr, nullptr, nullptr, napi_default, nullptr},
                {"discoverDevices", nullptr, ExecuteOperation<BiShareOperationFactory::OperationType::DISCOVER_DEVICES>, nullptr, nullptr, nullptr, napi_default, nullptr},
                {"getDiscoveredDevices", nullptr, ExecuteOperation<BiShareOperationFactory::OperationType::GET_DISCOVERED_DEVICES>, nullptr, nullptr, nullptr, napi_default, nullptr},
                {"setDeviceModel", nullptr, ExecuteOperation<BiShareOperationFactory::OperationType::SET_DEVICE_MODEL>, nullptr, nullptr, nullptr, napi_default, nullptr},
                {"getDeviceModel", nullptr, ExecuteOperation<BiShareOperationFactory::OperationType::GET_DEVICE_MODEL>, nullptr, nullptr, nullptr, napi_default, nullptr},
                {"resetDeviceModel", nullptr, ExecuteOperation<BiShareOperationFactory::OperationType::RESET_DEVICE_MODEL>, nullptr, nullptr, nullptr, napi_default, nullptr},
                {"setDeviceInfo", nullptr, ExecuteOperation<BiShareOperationFactory::OperationType::SET_DEVICE_INFO>, nullptr, nullptr, nullptr, napi_default, nullptr},
                {"findRemoteDevice", nullptr, ExecuteOperation<BiShareOperationFactory::OperationType::FIND_REMOTE_DEVICE>, nullptr, nullptr, nullptr, napi_default, nullptr},
                {"startCapture", nullptr, ExecuteOperation<BiShareOperationFactory::OperationType::START_CAPTURE>, nullptr, nullptr, nullptr, napi_default, nullptr},
                {"setSize", nullptr, ExecuteOperation<BiShareOperationFactory::OperationType::SET_SIZE>, nullptr, nullptr, nullptr, napi_default, nullptr},
                {"screenshot", nullptr, ExecuteOperation<BiShareOperationFactory::OperationType::SCREENSHOT>, nullptr, nullptr, nullptr, napi_default, nullptr},
                {"startScreenRecord", nullptr, ExecuteOperation<BiShareOperationFactory::OperationType::START_SCREEN_RECORD>, nullptr, nullptr, nullptr, napi_default, nullptr},
                {"stopScreenRecord", nullptr, ExecuteOperation<BiShareOperationFactory::OperationType::STOP_SCREEN_RECORD>, nullptr, nullptr, nullptr, napi_default, nullptr},
                {"setDefaultAudioOutputDevice", nullptr, ExecuteOperation<BiShareOperationFactory::OperationType::SET_DEFAULT_AUDIO_OUTPUT_DEVICE>, nullptr, nullptr, nullptr, napi_default, nullptr},
                {"getRootPath", nullptr, ExecuteOperation<BiShareOperationFactory::OperationType::GET_ROOT_PATH>, nullptr, nullptr, nullptr, napi_default, nullptr},
                {"getCurrentDirector", nullptr, ExecuteOperation<BiShareOperationFactory::OperationType::GET_CURRENT_DIRECTOR>, nullptr, nullptr, nullptr, napi_default, nullptr},

                // 事件处理方法
                {"on", nullptr, ExecuteOperation<BiShareOperationFactory::OperationType::ON_EVENT>, nullptr, nullptr, nullptr, napi_default, nullptr},
            };

            // 创建 NAPI 类
            napi_define_properties(env, exports, sizeof(properties) / sizeof(properties[0]), properties);

            // 如果未创建，则创建单例实例
            if (instance_ == nullptr) {
                instance_ = std::unique_ptr<BiShareNapi>(new BiShareNapi());
            }

            return exports;
        }

        // 构造函数
        BiShareNapi::BiShareNapi() {
            // 初始化管理器
            callbacks_ = std::make_shared<BiShareCallbacks>(std::weak_ptr<BiShareNapi>());
            deviceManager_ = std::make_shared<BiShareDeviceManager>();
            recordManager_ = std::make_shared<BiShareRecordManager>();
            operationFactory_ = std::make_shared<BiShareOperationFactory>();

            // 初始化互斥锁
            pthread_mutex_init(&eventCallbacksMutex_, nullptr);
        }

        // 获取单例实例
        BiShareNapi* BiShareNapi::GetInstance() {
            return instance_.get();
        }

        // 析构函数
        BiShareNapi::~BiShareNapi() {
            // 清理资源
            if (isInitialized_.load()) {
                bishare_service_release();
                isInitialized_.store(false);
            }

            // 销毁互斥锁
            pthread_mutex_destroy(&eventCallbacksMutex_);
        }

        // Init 方法实现
        napi_value BiShareNapi::Init(napi_env env, napi_callback_info info) {
            return CreateAsyncWork(
                env, info, "Init",
                // 执行回调
                [](napi_env env, AsyncWorkData *workData) {
                    // 检查是否已初始化
                    if (BiShareNapi::isInitialized_.load()) {
                        BiShareLogger::Info(NAPI_TAG, "已经初始化过了...");
                        workData->result = BS_OK;
                        return;
                    }

                    // 转换参数
                    bool_type_t isConsole = workData->data.boolParam1 ? BOOL_TRUE : BOOL_FALSE;
                    bool_type_t isFile = workData->data.boolParam2 ? BOOL_TRUE : BOOL_FALSE;

                    const char *isConsoleString = (isConsole == BOOL_TRUE) ? "TRUE" : "FALSE";
                    BiShareLogger::Info(NAPI_TAG, "Value of isConsole (string): %s", isConsoleString);
                    BiShareLogger::Info(NAPI_TAG, "Value of isFile (integer): %d", static_cast<int>(isFile));

                    BiShareLogger::Info(NAPI_TAG, "开始进行初始化...");
                    // 调用原生函数
                    workData->result = bishare_service_init(isConsole, isFile, workData->data.stringParam1.c_str(),
                                                            workData->data.priority);

                    BiShareLogger::Info(NAPI_TAG, "初始化是否成功 (integer): %d",
                                        static_cast<int>(workData->result));

                    // 如果初始化成功，注册回调
                    if (workData->result == BS_OK) {
                        // 设置初始化标志
                        BiShareNapi::isInitialized_.store(true);

                        // 注册回调
                        bishare_service_register_event_callback(BiShareCallbacks::OnEventCallback);
                        bishare_service_register_packet_callback(BiShareCallbacks::OnPacketCallback);
                    } else {
                        workData->errorMessage = std::string("Failed to initialize BiShare service: ") +
                                                 std::string(err2str(workData->result));
                    }
                },
                // 完成回调
                StandardCompleteCallback,
                // 参数解析
                [](napi_env env, napi_callback_info info, AsyncWorkData **workData) {
                    // 创建工作数据
                    *workData = new AsyncWorkData(env);

                    // 获取回调信息
                    size_t argc = 4;
                    napi_value argv[5]; // 包括回调
                    napi_value thisArg;
                    void *data;
                    napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);

                    // 检查所需的参数
                    if (argc < 3) {
                        napi_throw_error(env, "EINVAL", "Wrong number of arguments, expected at least 3");
                        return false;
                    }

                    // 解析 isConsole（布尔值）
                    bool isConsole;
                    napi_get_value_bool(env, argv[0], &isConsole);
                    (*workData)->data.boolParam1 = isConsole;

                    // 解析 isFile（布尔值）
                    bool isFile;
                    napi_get_value_bool(env, argv[1], &isFile);
                    (*workData)->data.boolParam2 = isFile;

                    // 解析 filePath（字符串）
                    size_t filePathLength;
                    napi_get_value_string_utf8(env, argv[2], nullptr, 0, &filePathLength);
                    if (filePathLength > 0) {
                        std::vector<char> filePathBuffer(filePathLength + 1);
                        napi_get_value_string_utf8(env, argv[2], filePathBuffer.data(), filePathLength + 1,
                                                   &filePathLength);
                        (*workData)->data.stringParam1 = std::string(filePathBuffer.data(), filePathLength);
                    } else {
                        (*workData)->data.stringParam1 = "";
                    }

                    // 解析 priority（数字），如果提供
                    if (argc > 3) {
                        napi_valuetype valueType;
                        napi_typeof(env, argv[3], &valueType);

                        if (valueType == napi_number) {
                            int32_t priority;
                            napi_get_value_int32(env, argv[3], &priority);
                            (*workData)->data.priority = static_cast<log_priority_t>(priority);
                        } else if (valueType == napi_function) {
                            // 这是回调而不是优先级
                            napi_create_reference(env, argv[3], 1, &(*workData)->callback);
                            (*workData)->data.priority = LOG_INFO; // 默认
                        } else {
                            (*workData)->data.priority = LOG_INFO; // 默认
                        }
                    } else {
                        (*workData)->data.priority = LOG_INFO; // 默认
                    }

                    // 解析回调（如果提供）
                    if (argc > 4) {
                        napi_valuetype valueType;
                        napi_typeof(env, argv[4], &valueType);

                        if (valueType == napi_function) {
                            napi_create_reference(env, argv[4], 1, &(*workData)->callback);
                        }
                    }

                    return true;
                });
        }

        // Release 方法实现
        napi_value BiShareNapi::Release(napi_env env, napi_callback_info info) {
            return CreateAsyncWork(
                env, info, "Release",
                // 执行回调
                [](napi_env env, AsyncWorkData *workData) {
                    // 检查是否已初始化
                    if (!BiShareNapi::isInitialized_.load()) {
                        workData->result = BS_NOT_INIT;
                        workData->errorMessage = "BiShare service is not initialized";
                        return;
                    }

                    // 调用原生函数
                    workData->result = bishare_service_release();

                    // 设置初始化标志
                    if (workData->result == BS_OK) {
                        BiShareNapi::isInitialized_.store(false);
                    } else {
                        workData->errorMessage =
                            std::string("Failed to release BiShare service: ") + std::string(err2str(workData->result));
                    }
                },
                // 完成回调
                StandardCompleteCallback,
                // 参数解析
                [](napi_env env, napi_callback_info info, AsyncWorkData **workData) {
                    // 创建工作数据
                    *workData = new AsyncWorkData(env);

                    // 获取回调信息
                    size_t argc = 1;
                    napi_value argv[1];
                    napi_value thisArg;
                    void *data;
                    napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);

                    // 解析回调（如果提供）
                    if (argc > 0) {
                        napi_valuetype valueType;
                        napi_typeof(env, argv[0], &valueType);

                        if (valueType == napi_function) {
                            napi_create_reference(env, argv[0], 1, &(*workData)->callback);
                        }
                    }

                    return true;
                });
        }


        // 模块初始化
        EXTERN_C_START
        static napi_module g_biShareModule = {
            .nm_version = 1,
            .nm_flags = 0,
            .nm_filename = nullptr,
            .nm_register_func = BiShareNapi::Init,
            .nm_modname = BISHARE_MODULE_NAME,
            .nm_priv = nullptr,
            .reserved = {nullptr},
        };
        EXTERN_C_END

        // 模块注册
        NAPI_MODULE(NODE_GYP_MODULE_NAME, g_biShareModule)
    } // namespace BiShare
} // namespace OHOS
