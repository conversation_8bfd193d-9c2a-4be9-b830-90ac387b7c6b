#include "bishare_napi.h"
#include "bishare_callbacks.h"
#include "bishare_device.h"
#include "bishare_recording.h"
#include "bishare-service.h"

namespace OHOS {
    namespace BiShare {

        // 初始化静态成员
        std::unique_ptr<BiShareNapi> BiShareNapi::instance_ = nullptr;
        std::atomic<bool> BiShareNapi::isInitialized_(false);

        // 获取单例实例
        BiShareNapi* BiShareNapi::GetInstance() {
            if (instance_ == nullptr) {
                instance_ = std::unique_ptr<BiShareNapi>(new BiShareNapi());
            }
            return instance_.get();
        }

        // 私有构造函数
        BiShareNapi::BiShareNapi() {
            // 初始化管理器
            callbacks_ = std::make_shared<BiShareCallbacks>(std::weak_ptr<BiShareNapi>());
            deviceManager_ = std::make_shared<BiShareDeviceManager>();
            recordManager_ = std::make_shared<BiShareRecordManager>();
        }

        // 析构函数
        BiShareNapi::~BiShareNapi() {
            // 清理资源
            if (isInitialized_.load()) {
                bishare_service_release();
                isInitialized_.store(false);
            }
        }

    } // namespace BiShare
} // namespace OHOS
