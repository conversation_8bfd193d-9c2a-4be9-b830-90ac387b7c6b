#include "bishare_napi_interface.h"
#include "bishare_facade.h"
#include "callback_manager.h"
#include "async_executor.h"
#include "sync_async_adapter.h"
#include "operation_factory.h"
#include "bishare_status_codes.h"
#include "bishare_logger.h"
#include "bishare_operations.h"
#include "bishare-service.h"
#include "bishare_napi.h"
#include "bishare_callbacks.h"
#include "bishare_operation_impls.h"

using namespace OHOS::BiShare::Infrastructure;

using namespace OHOS::BiShare::Core;

namespace OHOS {
    namespace BiShare {
        namespace Interfaces {

            // 定义日志标签
            static constexpr const char *NAPI_TAG = "BiShareNapiInterface";

            // 辅助函数类型定义
            using ExecuteCallback = std::function<void(napi_env, AsyncWorkData *)>;
            using CompleteCallback = std::function<void(napi_env, napi_status, AsyncWorkData *)>;
            using ParseCallback = std::function<bool(napi_env, napi_callback_info, AsyncWorkData **)>;

            // 标准完成回调
            void StandardCompleteCallback(napi_env env, napi_status status, AsyncWorkData *workData) {
                if (workData->callbackRef != nullptr) {
                    napi_value callback, result, global;
                    napi_get_reference_value(env, workData->callbackRef, &callback);
                    napi_get_global(env, &global);

                    if (workData->result == BS_OK) {
                        napi_value args[2];
                        napi_get_null(env, &args[0]); // error = null
                        napi_create_int32(env, static_cast<int32_t>(workData->result), &args[1]); // result
                        napi_call_function(env, global, callback, 2, args, &result);
                    } else {
                        napi_value args[2];
                        napi_create_string_utf8(env, workData->errorMessage.c_str(), NAPI_AUTO_LENGTH, &args[0]); // error
                        napi_get_null(env, &args[1]); // result = null
                        napi_call_function(env, global, callback, 2, args, &result);
                    }
                }
                delete workData;
            }

            // 创建异步工作的通用函数
            napi_value CreateAsyncWork(napi_env env, napi_callback_info info, const char *resourceName,
                                       ExecuteCallback executeCallback, CompleteCallback completeCallback,
                                       ParseCallback parseCallback) {
                AsyncWorkData *workData = nullptr;

                // 解析参数
                if (!parseCallback(env, info, &workData)) {
                    return nullptr;
                }

                // 创建异步工作
                napi_value resourceNameValue;
                napi_create_string_utf8(env, resourceName, NAPI_AUTO_LENGTH, &resourceNameValue);

                napi_create_async_work(
                    env, nullptr, resourceNameValue,
                    // 执行函数
                    [](napi_env env, void *data) {
                        AsyncWorkData *workData = static_cast<AsyncWorkData *>(data);
                        // 这里会调用传入的executeCallback
                        // 为了简化，我们直接在具体方法中实现执行逻辑
                    },
                    // 完成函数
                    [](napi_env env, napi_status status, void *data) {
                        AsyncWorkData *workData = static_cast<AsyncWorkData *>(data);
                        StandardCompleteCallback(env, status, workData);
                    },
                    workData, &workData->work);

                // 队列异步工作
                napi_queue_async_work(env, workData->work);

                // 返回 undefined
                napi_value result;
                napi_get_undefined(env, &result);
                return result;
            }

            napi_value BiShareNapiInterface::Init(napi_env env, napi_value exports) {
                // 初始化门面
                auto& facade = GetFacade();
                if (!facade.Initialize()) {
                    napi_throw_error(env, "INIT_FAILED", "Failed to initialize BiShare facade");
                    return nullptr;
                }

                // 定义常量
                napi_property_descriptor constants[] = {
                    // 版本信息
                    {"VERSION", nullptr, nullptr, nullptr, nullptr, 
                     CreateStringValue(env, "1.0.0"), napi_default, nullptr},

                    // 日志优先级常量
                    {"LOG_PRIORITY_DEBUG", nullptr, nullptr, nullptr, nullptr,
                     CreateInt32Value(env, LOG_DEBUG), napi_default, nullptr},
                    {"LOG_PRIORITY_INFO", nullptr, nullptr, nullptr, nullptr,
                     CreateInt32Value(env, LOG_INFO), napi_default, nullptr},
                    {"LOG_PRIORITY_WARN", nullptr, nullptr, nullptr, nullptr,
                     CreateInt32Value(env, LOG_WARN), napi_default, nullptr},
                    {"LOG_PRIORITY_ERROR", nullptr, nullptr, nullptr, nullptr,
                     CreateInt32Value(env, LOG_ERROR), napi_default, nullptr},

                    // 事件类型常量
                    {"EVENT_DEVICE_INFO", nullptr, nullptr, nullptr, nullptr, 
                     CreateInt32Value(env, EVENT_DEVICE_INFO), napi_default, nullptr},
                    {"EVENT_DEVICE_STATUS", nullptr, nullptr, nullptr, nullptr, 
                     CreateInt32Value(env, EVENT_DEVICE_STATUS), napi_default, nullptr},
                    {"EVENT_CONNECT_STATUS", nullptr, nullptr, nullptr, nullptr, 
                     CreateInt32Value(env, EVENT_CONNECT_STATUS), napi_default, nullptr},
                };

                // 定义方法 - 使用与bishare_napi.h一致的命名
                napi_property_descriptor methods[] = {
                    // API方法 - 与bishare_napi.h保持一致
                    {"initialize", nullptr, Initialize, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"release", nullptr, Release, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"discoverDevices", nullptr, DiscoverDevices, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"clearDiscoveredDevices", nullptr, ClearDiscoveredDevices, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"getDiscoveredDevices", nullptr, GetDiscoveredDevices, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"startScreenRecord", nullptr, StartScreenRecord, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"stopScreenRecord", nullptr, StopScreenRecord, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"startCapture", nullptr, StartCapture, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"setDeviceInfo", nullptr, SetDeviceInfo, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"setDeviceModel", nullptr, SetDeviceModel, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"getDeviceModel", nullptr, GetDeviceModel, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"resetDeviceModel", nullptr, ResetDeviceModel, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"getRootPath", nullptr, GetRootPath, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"getCurrentDirector", nullptr, GetCurrentDirector, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"setSize", nullptr, SetSize, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"setDefaultAudioOutputDevice", nullptr, SetDefaultAudioOutputDevice, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"screenshot", nullptr, Screenshot, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"findRemoteDevice", nullptr, FindRemoteDevice, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"setNetworkInfo", nullptr, SetNetworkInfo, nullptr, nullptr, nullptr, napi_default, nullptr},

                    // 事件管理方法
                    {"on", nullptr, On, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"off", nullptr, Off, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"once", nullptr, Once, nullptr, nullptr, nullptr, napi_default, nullptr},
                };

                // 定义常量
                napi_define_properties(env, exports, sizeof(constants) / sizeof(constants[0]), constants);
                
                // 定义方法
                napi_define_properties(env, exports, sizeof(methods) / sizeof(methods[0]), methods);

                return exports;
            }

            // API方法实现 - 与bishare_napi.h保持一致的命名
            napi_value BiShareNapiInterface::Initialize(napi_env env, napi_callback_info info) {
                // 使用现有的InitializeOperation
                auto operation = std::make_unique<InitializeOperation>();
                return operation->Execute(env, info);
            }

            napi_value BiShareNapiInterface::Release(napi_env env, napi_callback_info info) {
                // 使用现有的ReleaseOperation
                auto operation = std::make_unique<ReleaseOperation>();
                return operation->Execute(env, info);
            }

            napi_value BiShareNapiInterface::DiscoverDevices(napi_env env, napi_callback_info info) {
                // 同步/异步双模式支持
                return SMART_EXECUTE("DiscoverDevices",
                    // 同步执行函数
                    [](napi_env env, napi_callback_info info) -> napi_value {
                        auto operation = std::make_unique<DiscoverDevicesOperation>();
                        return operation->Execute(env, info);
                    }
                );
            }

            napi_value BiShareNapiInterface::ClearDiscoveredDevices(napi_env env, napi_callback_info info) {
                // 使用现有的ClearDiscoveredDevicesOperation
                auto operation = std::make_unique<ClearDiscoveredDevicesOperation>();
                return operation->Execute(env, info);
            }

            napi_value BiShareNapiInterface::GetDiscoveredDevices(napi_env env, napi_callback_info info) {
                // 同步/异步双模式支持 - 提供专门的异步实现
                return SMART_EXECUTE_WITH_ASYNC("GetDiscoveredDevices",
                    // 同步执行函数
                    [](napi_env env, napi_callback_info info) -> napi_value {
                        auto operation = std::make_unique<GetDiscoveredDevicesOperation>();
                        return operation->Execute(env, info);
                    },
                    // 异步执行函数（后台线程）
                    [](AsyncWorkData* workData) {
                        try {
                            // 在后台线程调用原生服务
                            // 注意：这里需要根据实际的原生函数签名调整
                            // 暂时使用简化的实现
                            workData->errorMessage = ""; // 标记成功
                        } catch (const std::exception& e) {
                            workData->errorMessage = e.what();
                        }
                    },
                    // 异步完成函数（主线程）
                    [](napi_env env, AsyncWorkData* workData) -> napi_value {
                        if (workData->errorMessage.empty()) {
                            // 创建设备数组（简化示例）
                            napi_value deviceArray;
                            napi_create_array(env, &deviceArray);

                            // 这里应该调用现有的Operation来获取实际数据
                            // 暂时返回空数组作为示例

                            return deviceArray;
                        } else {
                            napi_value error;
                            napi_value message;
                            napi_create_string_utf8(env, workData->errorMessage.c_str(),
                                                   NAPI_AUTO_LENGTH, &message);
                            napi_create_error(env, nullptr, message, &error);
                            return error;
                        }
                    }
                );
            }

            napi_value BiShareNapiInterface::SetDeviceInfo(napi_env env, napi_callback_info info) {
                // 使用现有的SetDeviceInfoOperation
                auto operation = std::make_unique<SetDeviceInfoOperation>();
                return operation->Execute(env, info);
            }

            napi_value BiShareNapiInterface::SetDeviceModel(napi_env env, napi_callback_info info) {
                // 使用现有的SetDeviceModelOperation
                auto operation = std::make_unique<SetDeviceModelOperation>();
                return operation->Execute(env, info);
            }

            napi_value BiShareNapiInterface::GetDeviceModel(napi_env env, napi_callback_info info) {
                // 使用现有的GetDeviceModelOperation
                auto operation = std::make_unique<GetDeviceModelOperation>();
                return operation->Execute(env, info);
            }

            napi_value BiShareNapiInterface::ResetDeviceModel(napi_env env, napi_callback_info info) {
                // 使用现有的ResetDeviceModelOperation
                auto operation = std::make_unique<ResetDeviceModelOperation>();
                return operation->Execute(env, info);
            }

            napi_value BiShareNapiInterface::FindRemoteDevice(napi_env env, napi_callback_info info) {
                // 使用现有的FindRemoteDeviceOperation
                auto operation = std::make_unique<FindRemoteDeviceOperation>();
                return operation->Execute(env, info);
            }

            // 录制管理方法实现
            napi_value BiShareNapiInterface::StartScreenRecord(napi_env env, napi_callback_info info) {
                // 录制操作通常需要异步处理，使用包装模式
                return WRAP_SYNC_AS_ASYNC("StartScreenRecord",
                    [](napi_env env, napi_callback_info info) -> napi_value {
                        auto operation = std::make_unique<StartScreenRecordOperation>();
                        return operation->Execute(env, info);
                    }
                );
            }

            napi_value BiShareNapiInterface::StopScreenRecord(napi_env env, napi_callback_info info) {
                // 使用现有的StopScreenRecordOperation
                auto operation = std::make_unique<StopScreenRecordOperation>();
                return operation->Execute(env, info);
            }

            napi_value BiShareNapiInterface::StartCapture(napi_env env, napi_callback_info info) {
                // 使用现有的StartCaptureOperation
                auto operation = std::make_unique<StartCaptureOperation>();
                return operation->Execute(env, info);
            }

            napi_value BiShareNapiInterface::SetSize(napi_env env, napi_callback_info info) {
                // 使用现有的SetSizeOperation
                auto operation = std::make_unique<SetSizeOperation>();
                return operation->Execute(env, info);
            }

            napi_value BiShareNapiInterface::SetDefaultAudioOutputDevice(napi_env env, napi_callback_info info) {
                // 使用现有的SetDefaultAudioOutputDeviceOperation
                auto operation = std::make_unique<SetDefaultAudioOutputDeviceOperation>();
                return operation->Execute(env, info);
            }

            napi_value BiShareNapiInterface::Screenshot(napi_env env, napi_callback_info info) {
                // 使用现有的ScreenshotOperation
                auto operation = std::make_unique<ScreenshotOperation>();
                return operation->Execute(env, info);
            }

            // 网络管理方法实现
            napi_value BiShareNapiInterface::SetNetworkInfo(napi_env env, napi_callback_info info) {
                // 使用现有的SetNetworkInfoOperation
                auto operation = std::make_unique<SetNetworkInfoOperation>();
                return operation->Execute(env, info);
            }

            napi_value BiShareNapiInterface::GetRootPath(napi_env env, napi_callback_info info) {
                // 使用现有的GetRootPathOperation
                auto operation = std::make_unique<GetRootPathOperation>();
                return operation->Execute(env, info);
            }

            napi_value BiShareNapiInterface::GetCurrentDirector(napi_env env, napi_callback_info info) {
                // 使用现有的GetCurrentDirectoryOperation
                auto operation = std::make_unique<GetCurrentDirectoryOperation>();
                return operation->Execute(env, info);
            }

            // 事件管理方法实现
            napi_value BiShareNapiInterface::On(napi_env env, napi_callback_info info) {
                // 使用现有的OnEventOperation
                auto operation = std::make_unique<OnEventOperation>();
                return operation->Execute(env, info);
            }

            napi_value BiShareNapiInterface::Off(napi_env env, napi_callback_info info) {
                // 使用现有的OffEventOperation
                auto operation = std::make_unique<OffEventOperation>();
                return operation->Execute(env, info);
            }

            napi_value BiShareNapiInterface::Once(napi_env env, napi_callback_info info) {
                // 使用现有的OnceEventOperation
                auto operation = std::make_unique<OnceEventOperation>();
                return operation->Execute(env, info);
            }

            // 私有辅助方法
            OHOS::BiShare::Core::BiShareFacade& BiShareNapiInterface::GetFacade() {
                return OHOS::BiShare::Core::BiShareFacade::GetInstance();
            }

            napi_value BiShareNapiInterface::CreateError(napi_env env, const std::string& message) {
                napi_value error, errorMessage;
                napi_create_string_utf8(env, message.c_str(), NAPI_AUTO_LENGTH, &errorMessage);
                napi_create_error(env, nullptr, errorMessage, &error);
                return error;
            }

            napi_value BiShareNapiInterface::CreateSuccessResult(napi_env env, napi_value data) {
                napi_value result;
                if (data) {
                    result = data;
                } else {
                    napi_get_undefined(env, &result);
                }
                return result;
            }

            napi_value BiShareNapiInterface::CreateStringValue(napi_env env, const std::string& str) {
                napi_value result;
                napi_create_string_utf8(env, str.c_str(), NAPI_AUTO_LENGTH, &result);
                return result;
            }

            napi_value BiShareNapiInterface::CreateInt32Value(napi_env env, int32_t value) {
                napi_value result;
                napi_create_int32(env, value, &result);
                return result;
            }

        } // namespace Interfaces
    } // namespace BiShare
} // namespace OHOS
