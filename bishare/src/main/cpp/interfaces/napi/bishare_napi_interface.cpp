#include "bishare_napi_interface.h"
#include "bishare_facade.h"
#include "callback_manager.h"
#include "async_executor.h"
#include "sync_async_adapter.h"
#include "operation_factory.h"
#include "bishare_status_codes.h"
#include "bishare_logger.h"
#include "bishare_operations.h"
#include "bishare-service.h"
#include "bishare_napi.h"
#include "bishare_callbacks.h"
#include "bishare_operation_impls.h"

using namespace OHOS::BiShare::Infrastructure;

using namespace OHOS::BiShare::Core;

namespace OHOS {
    namespace BiShare {
        namespace Interfaces {

            // 定义日志标签
            static constexpr const char *NAPI_TAG = "BiShareNapiInterface";

            // 辅助函数类型定义
            using ExecuteCallback = std::function<void(napi_env, AsyncWorkData *)>;
            using CompleteCallback = std::function<void(napi_env, napi_status, AsyncWorkData *)>;
            using ParseCallback = std::function<bool(napi_env, napi_callback_info, AsyncWorkData **)>;

            // 标准完成回调
            void StandardCompleteCallback(napi_env env, napi_status status, AsyncWorkData *workData) {
                if (workData->callbackRef != nullptr) {
                    napi_value callback, result, global;
                    napi_get_reference_value(env, workData->callbackRef, &callback);
                    napi_get_global(env, &global);

                    if (workData->result == BS_OK) {
                        napi_value args[2];
                        napi_get_null(env, &args[0]); // error = null
                        napi_create_int32(env, static_cast<int32_t>(workData->result), &args[1]); // result
                        napi_call_function(env, global, callback, 2, args, &result);
                    } else {
                        napi_value args[2];
                        napi_create_string_utf8(env, workData->errorMessage.c_str(), NAPI_AUTO_LENGTH, &args[0]); // error
                        napi_get_null(env, &args[1]); // result = null
                        napi_call_function(env, global, callback, 2, args, &result);
                    }
                }
                delete workData;
            }

            // 创建异步工作的通用函数
            napi_value CreateAsyncWork(napi_env env, napi_callback_info info, const char *resourceName,
                                       ExecuteCallback executeCallback, CompleteCallback completeCallback,
                                       ParseCallback parseCallback) {
                AsyncWorkData *workData = nullptr;

                // 解析参数
                if (!parseCallback(env, info, &workData)) {
                    return nullptr;
                }

                // 创建异步工作
                napi_value resourceNameValue;
                napi_create_string_utf8(env, resourceName, NAPI_AUTO_LENGTH, &resourceNameValue);

                napi_create_async_work(
                    env, nullptr, resourceNameValue,
                    // 执行函数
                    [](napi_env env, void *data) {
                        AsyncWorkData *workData = static_cast<AsyncWorkData *>(data);
                        // 这里会调用传入的executeCallback
                        // 为了简化，我们直接在具体方法中实现执行逻辑
                    },
                    // 完成函数
                    [](napi_env env, napi_status status, void *data) {
                        AsyncWorkData *workData = static_cast<AsyncWorkData *>(data);
                        StandardCompleteCallback(env, status, workData);
                    },
                    workData, &workData->work);

                // 队列异步工作
                napi_queue_async_work(env, workData->work);

                // 返回 undefined
                napi_value result;
                napi_get_undefined(env, &result);
                return result;
            }

            napi_value BiShareNapiInterface::Init(napi_env env, napi_value exports) {
                // 初始化门面
                auto& facade = GetFacade();
                if (!facade.Initialize()) {
                    napi_throw_error(env, "INIT_FAILED", "Failed to initialize BiShare facade");
                    return nullptr;
                }

                // 定义常量
                napi_property_descriptor constants[] = {
                    // 版本信息
                    {"VERSION", nullptr, nullptr, nullptr, nullptr, 
                     CreateStringValue(env, "1.0.0"), napi_default, nullptr},

                    // 日志优先级常量
                    {"LOG_PRIORITY_DEBUG", nullptr, nullptr, nullptr, nullptr,
                     CreateInt32Value(env, LOG_DEBUG), napi_default, nullptr},
                    {"LOG_PRIORITY_INFO", nullptr, nullptr, nullptr, nullptr,
                     CreateInt32Value(env, LOG_INFO), napi_default, nullptr},
                    {"LOG_PRIORITY_WARN", nullptr, nullptr, nullptr, nullptr,
                     CreateInt32Value(env, LOG_WARN), napi_default, nullptr},
                    {"LOG_PRIORITY_ERROR", nullptr, nullptr, nullptr, nullptr,
                     CreateInt32Value(env, LOG_ERROR), napi_default, nullptr},

                    // 事件类型常量
                    {"EVENT_DEVICE_INFO", nullptr, nullptr, nullptr, nullptr, 
                     CreateInt32Value(env, EVENT_DEVICE_INFO), napi_default, nullptr},
                    {"EVENT_DEVICE_STATUS", nullptr, nullptr, nullptr, nullptr, 
                     CreateInt32Value(env, EVENT_DEVICE_STATUS), napi_default, nullptr},
                    {"EVENT_CONNECT_STATUS", nullptr, nullptr, nullptr, nullptr, 
                     CreateInt32Value(env, EVENT_CONNECT_STATUS), napi_default, nullptr},
                };

                // 定义方法 - 使用与bishare_napi.h一致的命名
                napi_property_descriptor methods[] = {
                    // API方法 - 与bishare_napi.h保持一致
                    {"initialize", nullptr, Initialize, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"release", nullptr, Release, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"discoverDevices", nullptr, DiscoverDevices, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"clearDiscoveredDevices", nullptr, ClearDiscoveredDevices, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"getDiscoveredDevices", nullptr, GetDiscoveredDevices, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"startScreenRecord", nullptr, StartScreenRecord, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"stopScreenRecord", nullptr, StopScreenRecord, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"startCapture", nullptr, StartCapture, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"setDeviceInfo", nullptr, SetDeviceInfo, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"setDeviceModel", nullptr, SetDeviceModel, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"getDeviceModel", nullptr, GetDeviceModel, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"resetDeviceModel", nullptr, ResetDeviceModel, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"getRootPath", nullptr, GetRootPath, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"getCurrentDirector", nullptr, GetCurrentDirector, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"setSize", nullptr, SetSize, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"setDefaultAudioOutputDevice", nullptr, SetDefaultAudioOutputDevice, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"screenshot", nullptr, Screenshot, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"findRemoteDevice", nullptr, FindRemoteDevice, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"setNetworkInfo", nullptr, SetNetworkInfo, nullptr, nullptr, nullptr, napi_default, nullptr},

                    // 事件管理方法
                    {"on", nullptr, On, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"off", nullptr, Off, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"once", nullptr, Once, nullptr, nullptr, nullptr, napi_default, nullptr},
                };

                // 定义常量
                napi_define_properties(env, exports, sizeof(constants) / sizeof(constants[0]), constants);
                
                // 定义方法
                napi_define_properties(env, exports, sizeof(methods) / sizeof(methods[0]), methods);

                return exports;
            }

            // API方法实现 - 与bishare_napi.h保持一致的命名
            napi_value BiShareNapiInterface::Initialize(napi_env env, napi_callback_info info) {
                // 使用现有的InitializeOperation
                auto operation = std::make_unique<InitializeOperation>();
                return operation->Execute(env, info);
            }

            napi_value BiShareNapiInterface::Release(napi_env env, napi_callback_info info) {
                // 使用现有的ReleaseOperation
                auto operation = std::make_unique<ReleaseOperation>();
                return operation->Execute(env, info);
            }

            napi_value BiShareNapiInterface::DiscoverDevices(napi_env env, napi_callback_info info) {
                // 真正的同步/异步双模式支持
                return SyncAsyncAdapter::SmartExecute(env, info, "DiscoverDevices",
                    // 同步执行函数
                    [](napi_env env, napi_callback_info info) -> napi_value {
                        BiShareLogger::Info("DiscoverDevices", "执行同步设备发现");
                        auto operation = std::make_unique<DiscoverDevicesOperation>();
                        return operation->Execute(env, info);
                    },
                    // 异步执行函数（后台线程）
                    [](AsyncWorkData* workData) {
                        try {
                            BiShareLogger::Info("DiscoverDevices", "异步执行设备发现");

                            // 在后台线程调用原生设备发现服务
                            bstatus_t result = bishare_service_discover_devices();

                            if (result == BS_OK) {
                                workData->errorMessage = ""; // 标记成功
                                BiShareLogger::Info("DiscoverDevices", "异步设备发现成功");
                            } else {
                                workData->errorMessage = std::string("设备发现失败: ") +
                                    std::string(err2str(result));
                                BiShareLogger::Error("DiscoverDevices", "异步设备发现失败: %d", static_cast<int>(result));
                            }
                        } catch (const std::exception& e) {
                            workData->errorMessage = std::string("异步设备发现异常: ") + e.what();
                            BiShareLogger::Error("DiscoverDevices", "异步设备发现异常: %s", e.what());
                        }
                    },
                    // 异步完成函数（主线程）
                    [](napi_env env, AsyncWorkData* workData) -> napi_value {
                        if (workData->errorMessage.empty()) {
                            // 成功情况：返回成功结果
                            napi_value result;
                            napi_get_boolean(env, true, &result);
                            BiShareLogger::Info("DiscoverDevices", "异步设备发现完成，返回成功");
                            return result;
                        } else {
                            // 失败情况：创建错误对象
                            napi_value error;
                            napi_value message;
                            napi_create_string_utf8(env, workData->errorMessage.c_str(),
                                                   NAPI_AUTO_LENGTH, &message);
                            napi_create_error(env, nullptr, message, &error);
                            BiShareLogger::Error("DiscoverDevices", "异步设备发现完成，返回错误: %s",
                                                workData->errorMessage.c_str());
                            return error;
                        }
                    }
                );
            }

            napi_value BiShareNapiInterface::ClearDiscoveredDevices(napi_env env, napi_callback_info info) {
                // 同步/异步双模式支持
                return SMART_EXECUTE("ClearDiscoveredDevices",
                    [](napi_env env, napi_callback_info info) -> napi_value {
                        auto operation = std::make_unique<ClearDiscoveredDevicesOperation>();
                        return operation->Execute(env, info);
                    }
                );
            }

            napi_value BiShareNapiInterface::GetDiscoveredDevices(napi_env env, napi_callback_info info) {
                // 真正的同步/异步双模式支持 - 获取设备列表
                return SyncAsyncAdapter::SmartExecute(env, info, "GetDiscoveredDevices",
                    // 同步执行函数
                    [](napi_env env, napi_callback_info info) -> napi_value {
                        BiShareLogger::Info("GetDiscoveredDevices", "执行同步获取设备列表");
                        auto operation = std::make_unique<GetDiscoveredDevicesOperation>();
                        return operation->Execute(env, info);
                    },
                    // 异步执行函数（后台线程）
                    [](AsyncWorkData* workData) {
                        try {
                            BiShareLogger::Info("GetDiscoveredDevices", "异步获取设备列表");

                            // 在后台线程调用原生服务获取设备列表
                            bstatus_t result = bishare_service_get_discovery_device();

                            if (result == BS_OK) {
                                workData->errorMessage = ""; // 标记成功
                                BiShareLogger::Info("GetDiscoveredDevices", "异步获取设备列表成功");
                            } else {
                                workData->errorMessage = std::string("获取设备列表失败: ") +
                                    std::string(err2str(result));
                                BiShareLogger::Error("GetDiscoveredDevices", "异步获取设备列表失败: %d", static_cast<int>(result));
                            }
                        } catch (const std::exception& e) {
                            workData->errorMessage = std::string("异步获取设备列表异常: ") + e.what();
                            BiShareLogger::Error("GetDiscoveredDevices", "异步获取设备列表异常: %s", e.what());
                        }
                    },
                    // 异步完成函数（主线程）
                    [](napi_env env, AsyncWorkData* workData) -> napi_value {
                        if (workData->errorMessage.empty()) {
                            // 成功情况：创建设备数组
                            // 注意：这里应该从全局状态或回调中获取实际的设备数据
                            // 目前返回一个表示成功的布尔值，实际项目中应该返回设备数组
                            napi_value result;
                            napi_get_boolean(env, true, &result);
                            BiShareLogger::Info("GetDiscoveredDevices", "异步获取设备列表完成，返回成功");
                            return result;
                        } else {
                            // 失败情况：创建错误对象
                            napi_value error;
                            napi_value message;
                            napi_create_string_utf8(env, workData->errorMessage.c_str(),
                                                   NAPI_AUTO_LENGTH, &message);
                            napi_create_error(env, nullptr, message, &error);
                            BiShareLogger::Error("GetDiscoveredDevices", "异步获取设备列表完成，返回错误: %s",
                                                workData->errorMessage.c_str());
                            return error;
                        }
                    }
                );
            }

            napi_value BiShareNapiInterface::SetDeviceInfo(napi_env env, napi_callback_info info) {
                // 同步/异步双模式支持 - 配置操作通常较快，适合智能检测
                return SMART_EXECUTE("SetDeviceInfo",
                    [](napi_env env, napi_callback_info info) -> napi_value {
                        auto operation = std::make_unique<SetDeviceInfoOperation>();
                        return operation->Execute(env, info);
                    }
                );
            }

            napi_value BiShareNapiInterface::SetDeviceModel(napi_env env, napi_callback_info info) {
                // 同步/异步双模式支持
                return SMART_EXECUTE("SetDeviceModel",
                    [](napi_env env, napi_callback_info info) -> napi_value {
                        auto operation = std::make_unique<SetDeviceModelOperation>();
                        return operation->Execute(env, info);
                    }
                );
            }

            napi_value BiShareNapiInterface::GetDeviceModel(napi_env env, napi_callback_info info) {
                // 同步/异步双模式支持 - 查询操作通常较快
                return SMART_EXECUTE("GetDeviceModel",
                    [](napi_env env, napi_callback_info info) -> napi_value {
                        auto operation = std::make_unique<GetDeviceModelOperation>();
                        return operation->Execute(env, info);
                    }
                );
            }

            napi_value BiShareNapiInterface::ResetDeviceModel(napi_env env, napi_callback_info info) {
                // 同步/异步双模式支持
                return SMART_EXECUTE("ResetDeviceModel",
                    [](napi_env env, napi_callback_info info) -> napi_value {
                        auto operation = std::make_unique<ResetDeviceModelOperation>();
                        return operation->Execute(env, info);
                    }
                );
            }

            napi_value BiShareNapiInterface::FindRemoteDevice(napi_env env, napi_callback_info info) {
                // 同步/异步双模式支持 - 网络操作可能耗时，建议异步
                return SMART_EXECUTE("FindRemoteDevice",
                    [](napi_env env, napi_callback_info info) -> napi_value {
                        auto operation = std::make_unique<FindRemoteDeviceOperation>();
                        return operation->Execute(env, info);
                    }
                );
            }

            // 录制管理方法实现
            napi_value BiShareNapiInterface::StartScreenRecord(napi_env env, napi_callback_info info) {
                // 录制操作通常需要异步处理，使用包装模式
                return WRAP_SYNC_AS_ASYNC("StartScreenRecord",
                    [](napi_env env, napi_callback_info info) -> napi_value {
                        auto operation = std::make_unique<StartScreenRecordOperation>();
                        return operation->Execute(env, info);
                    }
                );
            }

            napi_value BiShareNapiInterface::StopScreenRecord(napi_env env, napi_callback_info info) {
                // 录制停止操作也可能耗时，使用包装异步模式
                return WRAP_SYNC_AS_ASYNC("StopScreenRecord",
                    [](napi_env env, napi_callback_info info) -> napi_value {
                        auto operation = std::make_unique<StopScreenRecordOperation>();
                        return operation->Execute(env, info);
                    }
                );
            }

            napi_value BiShareNapiInterface::StartCapture(napi_env env, napi_callback_info info) {
                // 捕获操作可能耗时，使用包装异步模式
                return WRAP_SYNC_AS_ASYNC("StartCapture",
                    [](napi_env env, napi_callback_info info) -> napi_value {
                        auto operation = std::make_unique<StartCaptureOperation>();
                        return operation->Execute(env, info);
                    }
                );
            }

            napi_value BiShareNapiInterface::SetSize(napi_env env, napi_callback_info info) {
                // 设置尺寸是配置操作，通常较快，使用智能检测
                return SMART_EXECUTE("SetSize",
                    [](napi_env env, napi_callback_info info) -> napi_value {
                        auto operation = std::make_unique<SetSizeOperation>();
                        return operation->Execute(env, info);
                    }
                );
            }

            napi_value BiShareNapiInterface::SetDefaultAudioOutputDevice(napi_env env, napi_callback_info info) {
                // 音频设备设置是配置操作，使用智能检测
                return SMART_EXECUTE("SetDefaultAudioOutputDevice",
                    [](napi_env env, napi_callback_info info) -> napi_value {
                        auto operation = std::make_unique<SetDefaultAudioOutputDeviceOperation>();
                        return operation->Execute(env, info);
                    }
                );
            }

            napi_value BiShareNapiInterface::Screenshot(napi_env env, napi_callback_info info) {
                // 截图操作涉及I/O，可能耗时，使用包装异步模式
                return WRAP_SYNC_AS_ASYNC("Screenshot",
                    [](napi_env env, napi_callback_info info) -> napi_value {
                        auto operation = std::make_unique<ScreenshotOperation>();
                        return operation->Execute(env, info);
                    }
                );
            }

            // 网络管理方法实现
            napi_value BiShareNapiInterface::SetNetworkInfo(napi_env env, napi_callback_info info) {
                // 网络配置操作，使用智能检测模式
                return SMART_EXECUTE("SetNetworkInfo",
                    [](napi_env env, napi_callback_info info) -> napi_value {
                        auto operation = std::make_unique<SetNetworkInfoOperation>();
                        return operation->Execute(env, info);
                    }
                );
            }

            napi_value BiShareNapiInterface::GetRootPath(napi_env env, napi_callback_info info) {
                // 路径查询操作，通常较快，使用智能检测
                return SMART_EXECUTE("GetRootPath",
                    [](napi_env env, napi_callback_info info) -> napi_value {
                        auto operation = std::make_unique<GetRootPathOperation>();
                        return operation->Execute(env, info);
                    }
                );
            }

            napi_value BiShareNapiInterface::GetCurrentDirector(napi_env env, napi_callback_info info) {
                // 目录查询操作，通常较快，使用智能检测
                return SMART_EXECUTE("GetCurrentDirector",
                    [](napi_env env, napi_callback_info info) -> napi_value {
                        auto operation = std::make_unique<GetCurrentDirectoryOperation>();
                        return operation->Execute(env, info);
                    }
                );
            }

            // 事件管理方法实现
            napi_value BiShareNapiInterface::On(napi_env env, napi_callback_info info) {
                // 事件注册操作，通常较快，使用智能检测
                return SMART_EXECUTE("On",
                    [](napi_env env, napi_callback_info info) -> napi_value {
                        auto operation = std::make_unique<OnEventOperation>();
                        return operation->Execute(env, info);
                    }
                );
            }

            napi_value BiShareNapiInterface::Off(napi_env env, napi_callback_info info) {
                // 事件注销操作，通常较快，使用智能检测
                return SMART_EXECUTE("Off",
                    [](napi_env env, napi_callback_info info) -> napi_value {
                        auto operation = std::make_unique<OffEventOperation>();
                        return operation->Execute(env, info);
                    }
                );
            }

            napi_value BiShareNapiInterface::Once(napi_env env, napi_callback_info info) {
                // 一次性事件注册，通常较快，使用智能检测
                return SMART_EXECUTE("Once",
                    [](napi_env env, napi_callback_info info) -> napi_value {
                        auto operation = std::make_unique<OnceEventOperation>();
                        return operation->Execute(env, info);
                    }
                );
            }

            // 私有辅助方法
            OHOS::BiShare::Core::BiShareFacade& BiShareNapiInterface::GetFacade() {
                return OHOS::BiShare::Core::BiShareFacade::GetInstance();
            }

            napi_value BiShareNapiInterface::CreateError(napi_env env, const std::string& message) {
                napi_value error, errorMessage;
                napi_create_string_utf8(env, message.c_str(), NAPI_AUTO_LENGTH, &errorMessage);
                napi_create_error(env, nullptr, errorMessage, &error);
                return error;
            }

            napi_value BiShareNapiInterface::CreateSuccessResult(napi_env env, napi_value data) {
                napi_value result;
                if (data) {
                    result = data;
                } else {
                    napi_get_undefined(env, &result);
                }
                return result;
            }

            napi_value BiShareNapiInterface::CreateStringValue(napi_env env, const std::string& str) {
                napi_value result;
                napi_create_string_utf8(env, str.c_str(), NAPI_AUTO_LENGTH, &result);
                return result;
            }

            napi_value BiShareNapiInterface::CreateInt32Value(napi_env env, int32_t value) {
                napi_value result;
                napi_create_int32(env, value, &result);
                return result;
            }

        } // namespace Interfaces
    } // namespace BiShare
} // namespace OHOS
