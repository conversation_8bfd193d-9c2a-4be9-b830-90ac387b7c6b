#include "bishare_napi_interface.h"
#include "bishare_facade.h"
#include "async_executor.h"
#include "operation_factory.h"

using namespace OHOS::BiShare::Core;
using namespace OHOS::BiShare::Infrastructure;

namespace OHOS {
    namespace BiShare {
        namespace Interfaces {

            napi_value BiShareNapiInterface::Init(napi_env env, napi_value exports) {
                // 初始化门面
                auto& facade = GetFacade();
                if (!facade.Initialize()) {
                    napi_throw_error(env, "INIT_FAILED", "Failed to initialize BiShare facade");
                    return nullptr;
                }

                // 定义常量
                napi_property_descriptor constants[] = {
                    // 版本信息
                    {"VERSION", nullptr, nullptr, nullptr, nullptr, 
                     CreateStringValue(env, "1.0.0"), napi_default, nullptr},

                    // 日志优先级常量
                    {"LOG_PRIORITY_DEBUG", nullptr, nullptr, nullptr, nullptr,
                     CreateInt32Value(env, LOG_DEBUG), napi_default, nullptr},
                    {"LOG_PRIORITY_INFO", nullptr, nullptr, nullptr, nullptr,
                     CreateInt32Value(env, LOG_INFO), napi_default, nullptr},
                    {"LOG_PRIORITY_WARN", nullptr, nullptr, nullptr, nullptr,
                     CreateInt32Value(env, LOG_WARN), napi_default, nullptr},
                    {"LOG_PRIORITY_ERROR", nullptr, nullptr, nullptr, nullptr,
                     CreateInt32Value(env, LOG_ERROR), napi_default, nullptr},

                    // 事件类型常量
                    {"EVENT_DEVICE_INFO", nullptr, nullptr, nullptr, nullptr, 
                     CreateInt32Value(env, EVENT_DEVICE_INFO), napi_default, nullptr},
                    {"EVENT_DEVICE_STATUS", nullptr, nullptr, nullptr, nullptr, 
                     CreateInt32Value(env, EVENT_DEVICE_STATUS), napi_default, nullptr},
                    {"EVENT_CONNECT_STATUS", nullptr, nullptr, nullptr, nullptr, 
                     CreateInt32Value(env, EVENT_CONNECT_STATUS), napi_default, nullptr},
                };

                // 定义方法
                napi_property_descriptor methods[] = {
                    // 服务管理方法
                    {"initService", nullptr, InitService, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"releaseService", nullptr, ReleaseService, nullptr, nullptr, nullptr, napi_default, nullptr},

                    // 设备管理方法
                    {"setDeviceInfo", nullptr, SetDeviceInfo, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"setDeviceModel", nullptr, SetDeviceModel, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"getDeviceModel", nullptr, GetDeviceModel, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"resetDeviceModel", nullptr, ResetDeviceModel, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"startDiscovery", nullptr, StartDiscovery, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"stopDiscovery", nullptr, StopDiscovery, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"clearDiscoveredDevices", nullptr, ClearDiscoveredDevices, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"getDiscoveredDevices", nullptr, GetDiscoveredDevices, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"findRemoteDevice", nullptr, FindRemoteDevice, nullptr, nullptr, nullptr, napi_default, nullptr},

                    // 录制管理方法
                    {"startScreenRecord", nullptr, StartScreenRecord, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"stopScreenRecord", nullptr, StopScreenRecord, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"startCapture", nullptr, StartCapture, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"setSize", nullptr, SetSize, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"setDefaultAudioOutputDevice", nullptr, SetDefaultAudioOutputDevice, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"screenshot", nullptr, Screenshot, nullptr, nullptr, nullptr, napi_default, nullptr},

                    // 网络管理方法
                    {"setNetworkInfo", nullptr, SetNetworkInfo, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"getRootPath", nullptr, GetRootPath, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"getCurrentDirectory", nullptr, GetCurrentDirectory, nullptr, nullptr, nullptr, napi_default, nullptr},

                    // 事件管理方法
                    {"on", nullptr, On, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"off", nullptr, Off, nullptr, nullptr, nullptr, napi_default, nullptr},
                    {"once", nullptr, Once, nullptr, nullptr, nullptr, napi_default, nullptr},
                };

                // 定义常量
                napi_define_properties(env, exports, sizeof(constants) / sizeof(constants[0]), constants);
                
                // 定义方法
                napi_define_properties(env, exports, sizeof(methods) / sizeof(methods[0]), methods);

                return exports;
            }

            // 服务管理方法实现
            napi_value BiShareNapiInterface::InitService(napi_env env, napi_callback_info info) {
                // 简化实现，先返回成功结果
                auto& facade = GetFacade();
                if (!facade.Initialize()) {
                    return CreateError(env, "Failed to initialize BiShare facade");
                }
                return CreateSuccessResult(env);
            }

            napi_value BiShareNapiInterface::ReleaseService(napi_env env, napi_callback_info info) {
                // 简化实现，先返回成功结果
                auto& facade = GetFacade();
                facade.Release();
                return CreateSuccessResult(env);
            }

            // 设备管理方法实现
            napi_value BiShareNapiInterface::SetDeviceInfo(napi_env env, napi_callback_info info) {
                // 简化实现，先返回成功结果
                return CreateSuccessResult(env);
            }

            napi_value BiShareNapiInterface::SetDeviceModel(napi_env env, napi_callback_info info) {
                // 类似的实现模式
                napi_value result;
                napi_get_undefined(env, &result);
                return result;
            }

            napi_value BiShareNapiInterface::GetDeviceModel(napi_env env, napi_callback_info info) {
                // 类似的实现模式
                napi_value result;
                napi_get_undefined(env, &result);
                return result;
            }

            napi_value BiShareNapiInterface::ResetDeviceModel(napi_env env, napi_callback_info info) {
                // 类似的实现模式
                napi_value result;
                napi_get_undefined(env, &result);
                return result;
            }

            napi_value BiShareNapiInterface::StartDiscovery(napi_env env, napi_callback_info info) {
                // 类似的实现模式
                napi_value result;
                napi_get_undefined(env, &result);
                return result;
            }

            napi_value BiShareNapiInterface::StopDiscovery(napi_env env, napi_callback_info info) {
                // 类似的实现模式
                napi_value result;
                napi_get_undefined(env, &result);
                return result;
            }

            napi_value BiShareNapiInterface::ClearDiscoveredDevices(napi_env env, napi_callback_info info) {
                // 类似的实现模式
                napi_value result;
                napi_get_undefined(env, &result);
                return result;
            }

            napi_value BiShareNapiInterface::GetDiscoveredDevices(napi_env env, napi_callback_info info) {
                // 类似的实现模式
                napi_value result;
                napi_get_undefined(env, &result);
                return result;
            }

            napi_value BiShareNapiInterface::FindRemoteDevice(napi_env env, napi_callback_info info) {
                // 类似的实现模式
                napi_value result;
                napi_get_undefined(env, &result);
                return result;
            }

            // 录制管理方法实现
            napi_value BiShareNapiInterface::StartScreenRecord(napi_env env, napi_callback_info info) {
                // 类似的实现模式
                napi_value result;
                napi_get_undefined(env, &result);
                return result;
            }

            napi_value BiShareNapiInterface::StopScreenRecord(napi_env env, napi_callback_info info) {
                // 类似的实现模式
                napi_value result;
                napi_get_undefined(env, &result);
                return result;
            }

            napi_value BiShareNapiInterface::StartCapture(napi_env env, napi_callback_info info) {
                // 类似的实现模式
                napi_value result;
                napi_get_undefined(env, &result);
                return result;
            }

            napi_value BiShareNapiInterface::SetSize(napi_env env, napi_callback_info info) {
                // 类似的实现模式
                napi_value result;
                napi_get_undefined(env, &result);
                return result;
            }

            napi_value BiShareNapiInterface::SetDefaultAudioOutputDevice(napi_env env, napi_callback_info info) {
                // 类似的实现模式
                napi_value result;
                napi_get_undefined(env, &result);
                return result;
            }

            napi_value BiShareNapiInterface::Screenshot(napi_env env, napi_callback_info info) {
                // 类似的实现模式
                napi_value result;
                napi_get_undefined(env, &result);
                return result;
            }

            // 网络管理方法实现
            napi_value BiShareNapiInterface::SetNetworkInfo(napi_env env, napi_callback_info info) {
                // 类似的实现模式
                napi_value result;
                napi_get_undefined(env, &result);
                return result;
            }

            napi_value BiShareNapiInterface::GetRootPath(napi_env env, napi_callback_info info) {
                // 类似的实现模式
                napi_value result;
                napi_get_undefined(env, &result);
                return result;
            }

            napi_value BiShareNapiInterface::GetCurrentDirectory(napi_env env, napi_callback_info info) {
                // 类似的实现模式
                napi_value result;
                napi_get_undefined(env, &result);
                return result;
            }

            // 事件管理方法实现
            napi_value BiShareNapiInterface::On(napi_env env, napi_callback_info info) {
                size_t argc = 2;
                napi_value argv[2];
                napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);

                if (argc < 2) {
                    napi_throw_error(env, "EINVAL", "Wrong number of arguments, expected 2");
                    return nullptr;
                }

                // 解析事件类型
                int32_t eventType;
                napi_get_value_int32(env, argv[0], &eventType);

                // 验证回调函数
                napi_valuetype valueType;
                napi_typeof(env, argv[1], &valueType);
                if (valueType != napi_function) {
                    napi_throw_error(env, "EINVAL", "Second argument must be a function");
                    return nullptr;
                }

                // 注册事件监听器
                auto& facade = GetFacade();
                auto callbackManager = facade.GetCallbackManager();
                bool success = callbackManager->RegisterEventListener(env, eventType, argv[1], false);

                napi_value result;
                napi_get_boolean(env, success, &result);
                return result;
            }

            napi_value BiShareNapiInterface::Off(napi_env env, napi_callback_info info) {
                size_t argc = 2;
                napi_value argv[2];
                napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);

                if (argc < 2) {
                    napi_throw_error(env, "EINVAL", "Wrong number of arguments, expected 2");
                    return nullptr;
                }

                // 解析事件类型
                int32_t eventType;
                napi_get_value_int32(env, argv[0], &eventType);

                // 验证回调函数
                napi_valuetype valueType;
                napi_typeof(env, argv[1], &valueType);
                if (valueType != napi_function) {
                    napi_throw_error(env, "EINVAL", "Second argument must be a function");
                    return nullptr;
                }

                // 注销事件监听器
                auto& facade = GetFacade();
                auto callbackManager = facade.GetCallbackManager();
                bool success = callbackManager->UnregisterEventListener(env, eventType, argv[1]);

                napi_value result;
                napi_get_boolean(env, success, &result);
                return result;
            }

            napi_value BiShareNapiInterface::Once(napi_env env, napi_callback_info info) {
                size_t argc = 2;
                napi_value argv[2];
                napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);

                if (argc < 2) {
                    napi_throw_error(env, "EINVAL", "Wrong number of arguments, expected 2");
                    return nullptr;
                }

                // 解析事件类型
                int32_t eventType;
                napi_get_value_int32(env, argv[0], &eventType);

                // 验证回调函数
                napi_valuetype valueType;
                napi_typeof(env, argv[1], &valueType);
                if (valueType != napi_function) {
                    napi_throw_error(env, "EINVAL", "Second argument must be a function");
                    return nullptr;
                }

                // 注册一次性事件监听器
                auto& facade = GetFacade();
                auto callbackManager = facade.GetCallbackManager();
                bool success = callbackManager->RegisterEventListener(env, eventType, argv[1], true);

                napi_value result;
                napi_get_boolean(env, success, &result);
                return result;
            }

            // 私有辅助方法
            OHOS::BiShare::Core::BiShareFacade& BiShareNapiInterface::GetFacade() {
                return OHOS::BiShare::Core::BiShareFacade::GetInstance();
            }

            napi_value BiShareNapiInterface::CreateError(napi_env env, const std::string& message) {
                napi_value error, errorMessage;
                napi_create_string_utf8(env, message.c_str(), NAPI_AUTO_LENGTH, &errorMessage);
                napi_create_error(env, nullptr, errorMessage, &error);
                return error;
            }

            napi_value BiShareNapiInterface::CreateSuccessResult(napi_env env, napi_value data) {
                napi_value result;
                if (data) {
                    result = data;
                } else {
                    napi_get_undefined(env, &result);
                }
                return result;
            }

            napi_value BiShareNapiInterface::CreateStringValue(napi_env env, const std::string& str) {
                napi_value result;
                napi_create_string_utf8(env, str.c_str(), NAPI_AUTO_LENGTH, &result);
                return result;
            }

            napi_value BiShareNapiInterface::CreateInt32Value(napi_env env, int32_t value) {
                napi_value result;
                napi_create_int32(env, value, &result);
                return result;
            }

        } // namespace Interfaces
    } // namespace BiShare
} // namespace OHOS
