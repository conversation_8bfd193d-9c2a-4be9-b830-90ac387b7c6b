#include "bishare_utils.h"
#include "bishare_logger.h"

namespace OHOS {
    namespace BiShare {

        static constexpr const char* UTILS_TAG = "BiShareUtils";

        bool BiShareUtils::BoolTypeToBoolean(bool_type_t value) { return value == BOOL_TRUE; }

        bool_type_t BiShareUtils::BooleanToBoolType(bool value) { return value ? BOOL_TRUE : BOOL_FALSE; }

        std::string BiShareUtils::StatusToString(bstatus_t status) { return std::string(err2str(status)); }

        napi_value BiShareUtils::CreateErrorFromStatus(napi_env env, bstatus_t status) {
            napi_value error, message;
            std::string errorMessage = std::string("BiShare error: ") + std::string(err2str(status));

            napi_create_string_utf8(env, errorMessage.c_str(), NAPI_AUTO_LENGTH, &message);
            napi_create_error(env, nullptr, message, &error);

            return error;
        }

        std::string BiShareUtils::GetStringFromNapiValue(napi_env env, napi_value value) {
            size_t length = 0;
            napi_get_value_string_utf8(env, value, nullptr, 0, &length);

            if (length == 0) {
                return "";
            }

            std::vector<char> buffer(length + 1);
            napi_get_value_string_utf8(env, value, buffer.data(), length + 1, &length);

            return std::string(buffer.data(), length);
        }

        int BiShareUtils::GetIntFromNapiValue(napi_env env, napi_value value) {
            int result = 0;
            napi_get_value_int32(env, value, &result);
            return result;
        }

        bool BiShareUtils::GetBoolFromNapiValue(napi_env env, napi_value value) {
            bool result = false;
            napi_get_value_bool(env, value, &result);
            return result;
        }

        bool BiShareUtils::IsValueOfType(napi_env env, napi_value value, napi_valuetype type) {
            napi_valuetype valueType;
            napi_typeof(env, value, &valueType);
            return valueType == type;
        }

        napi_value BiShareUtils::CreateNapiString(napi_env env, const std::string &value) {
            napi_value result;
            napi_create_string_utf8(env, value.c_str(), NAPI_AUTO_LENGTH, &result);
            return result;
        }

        napi_value BiShareUtils::CreateNapiStringArray(napi_env env, const std::vector<std::string> &values) {
            napi_value result;
            napi_create_array_with_length(env, values.size(), &result);

            for (size_t i = 0; i < values.size(); i++) {
                napi_value item;
                napi_create_string_utf8(env, values[i].c_str(), NAPI_AUTO_LENGTH, &item);
                napi_set_element(env, result, i, item);
            }

            return result;
        }

    } // namespace BiShare
} // namespace OHOS