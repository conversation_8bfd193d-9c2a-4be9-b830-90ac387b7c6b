#include "bishare_logger.h"
#include <cstdarg>     // C++ 标准库，用于处理可变参数 (va_list, va_start, va_end)
#include <cstdio>      // C++ 标准库，用于 vsnprintf
#include <hilog/log.h> // OpenHarmony HiLog NDK 头文件

namespace OHOS {
    namespace BiShare {

        // 如果 HLogger 的方法签名是 static void Debug(const char *tag, ...)，则使用这个
        static constexpr unsigned int BISHARE_LOGGER_DEFAULT_DOMAIN = 0x00201; // 例如，BiShareLogger 的默认域

/*        // --- 版本 A: HLogger 方法接受 domain 作为参数 ---
        void BiShareLogger::Debug(unsigned int domain, const char *tag, const char *format, ...) {
            va_list args;
            va_start(args, format);
            char buffer[1024] = {0}; // 建议 buffer 大小根据实际需要调整
            vsnprintf(buffer, sizeof(buffer) - 1, format, args);
            va_end(args);
            buffer[sizeof(buffer) - 1] = '\0'; // 确保空终止

            // 直接调用 OH_LOG_Print
            // 参数: type, level, domain, tag, format_for_buffer, buffer_content
            ((void)OH_LOG_Print(LOG_APP, LOG_DEBUG, domain, tag, "%{public}s", buffer));
        }

        void BiShareLogger::Info(unsigned int domain, const char *tag, const char *format, ...) {
            va_list args;
            va_start(args, format);
            char buffer[1024] = {0};
            vsnprintf(buffer, sizeof(buffer) - 1, format, args);
            va_end(args);
            buffer[sizeof(buffer) - 1] = '\0';

            ((void)OH_LOG_Print(LOG_APP, LOG_INFO, domain, tag, "%{public}s", buffer));
        }

        void BiShareLogger::Warn(unsigned int domain, const char *tag, const char *format, ...) {
            va_list args;
            va_start(args, format);
            char buffer[1024] = {0};
            vsnprintf(buffer, sizeof(buffer) - 1, format, args);
            va_end(args);
            buffer[sizeof(buffer) - 1] = '\0';

            ((void)OH_LOG_Print(LOG_APP, LOG_WARN, domain, tag, "%{public}s", buffer));
        }

        void BiShareLogger::Error(unsigned int domain, const char *tag, const char *format, ...) {
            va_list args;
            va_start(args, format);
            char buffer[1024] = {0};
            vsnprintf(buffer, sizeof(buffer) - 1, format, args);
            va_end(args);
            buffer[sizeof(buffer) - 1] = '\0';

            ((void)OH_LOG_Print(LOG_APP, LOG_ERROR, domain, tag, "%{public}s", buffer));
        }

        void BiShareLogger::Fatal(unsigned int domain, const char *tag, const char *format, ...) {
            va_list args;
            va_start(args, format);
            char buffer[1024] = {0};
            vsnprintf(buffer, sizeof(buffer) - 1, format, args);
            va_end(args);
            buffer[sizeof(buffer) - 1] = '\0';

            ((void)OH_LOG_Print(LOG_APP, LOG_FATAL, domain, tag, "%{public}s", buffer));
        }*/

        // --- 版本 B: HLogger 方法使用内部固定的 HLOGGER_DEFAULT_DOMAIN，只接受动态 tag ---
        // 如果 h_logger.h 中的声明是 static void Debug(const char *tag, ...)
        void BiShareLogger::Debug(const char *tag, const char *format, ...) {
            va_list args;
            va_start(args, format);
            char buffer[1024] = {0};
            vsnprintf(buffer, sizeof(buffer) - 1, format, args);
            va_end(args);
            buffer[sizeof(buffer) - 1] = '\0';

            ((void)OH_LOG_Print(LOG_APP, LOG_DEBUG, BISHARE_LOGGER_DEFAULT_DOMAIN, tag, "%{public}s", buffer));
        }

        void BiShareLogger::Info(const char *tag, const char *format, ...) {
            va_list args;
            va_start(args, format);
            char buffer[1024] = {0};
            vsnprintf(buffer, sizeof(buffer) - 1, format, args);
            va_end(args);
            buffer[sizeof(buffer) - 1] = '\0';

            ((void)OH_LOG_Print(LOG_APP, LOG_INFO, BISHARE_LOGGER_DEFAULT_DOMAIN, tag, "%{public}s", buffer));
        }

        void BiShareLogger::Warn(const char *tag, const char *format, ...) {
            va_list args;
            va_start(args, format);
            char buffer[1024] = {0};
            vsnprintf(buffer, sizeof(buffer) - 1, format, args);
            va_end(args);
            buffer[sizeof(buffer) - 1] = '\0';

            ((void)OH_LOG_Print(LOG_APP, LOG_WARN, BISHARE_LOGGER_DEFAULT_DOMAIN, tag, "%{public}s", buffer));
        }

        void BiShareLogger::Error(const char *tag, const char *format, ...) {
            va_list args;
            va_start(args, format);
            char buffer[1024] = {0};
            vsnprintf(buffer, sizeof(buffer) - 1, format, args);
            va_end(args);
            buffer[sizeof(buffer) - 1] = '\0';

            ((void)OH_LOG_Print(LOG_APP, LOG_ERROR, BISHARE_LOGGER_DEFAULT_DOMAIN, tag, "%{public}s", buffer));
        }

        void BiShareLogger::Fatal(const char *tag, const char *format, ...) {
            va_list args;
            va_start(args, format);
            char buffer[1024] = {0};
            vsnprintf(buffer, sizeof(buffer) - 1, format, args);
            va_end(args);
            buffer[sizeof(buffer) - 1] = '\0';

            ((void)OH_LOG_Print(LOG_APP, LOG_FATAL, BISHARE_LOGGER_DEFAULT_DOMAIN, tag, "%{public}s", buffer));
        }


    } // namespace BiShare
} // namespace OHOS