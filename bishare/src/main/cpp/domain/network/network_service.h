#ifndef NETWORK_SERVICE_H
#define NETWORK_SERVICE_H

#include <string>
#include <memory>
#include <vector>

#include "bishare-define.h"

namespace OHOS {
    namespace BiShare {
        namespace Domain {

            /**
             * 网络配置结构
             */
            struct NetworkConfig {
                network_type_t networkType;
                std::string ipAddress;
                std::string macAddress;
                int port;
                bool isEnabled;
            };

            /**
             * 网络连接信息
             */
            struct NetworkConnection {
                std::string connectionId;
                std::string remoteAddress;
                int remotePort;
                bool isActive;
                int64_t lastActivity;
            };

            /**
             * 网络服务 - 处理网络相关的业务逻辑
             * 
             * 职责：
             * 1. 管理网络配置和连接
             * 2. 处理网络通信
             * 3. 管理网络状态
             * 4. 提供文件路径和目录管理
             */
            class NetworkService {
            public:
                NetworkService();
                ~NetworkService();

                /**
                 * 初始化网络服务
                 */
                bool Initialize();

                /**
                 * 释放网络服务
                 */
                void Release();

                /**
                 * 设置网络信息
                 */
                bstatus_t SetNetworkInfo(network_type_t networkType, const std::string& address, const std::string& mac);

                /**
                 * 获取网络配置
                 */
                NetworkConfig GetNetworkConfig() const { return config_; }

                /**
                 * 获取根路径
                 */
                std::string GetRootPath() const;

                /**
                 * 获取当前目录
                 */
                std::string GetCurrentDirectory() const;

                /**
                 * 设置根路径
                 */
                bstatus_t SetRootPath(const std::string& rootPath);

                /**
                 * 设置当前目录
                 */
                bstatus_t SetCurrentDirectory(const std::string& directory);

                /**
                 * 启动网络服务
                 */
                bstatus_t StartNetworkService();

                /**
                 * 停止网络服务
                 */
                bstatus_t StopNetworkService();

                /**
                 * 获取活动连接列表
                 */
                std::vector<NetworkConnection> GetActiveConnections() const;

                /**
                 * 检查网络是否可用
                 */
                bool IsNetworkAvailable() const;

                /**
                 * 检查服务是否运行
                 */
                bool IsServiceRunning() const { return isServiceRunning_; }

                /**
                 * 发送数据到指定连接
                 */
                bstatus_t SendData(const std::string& connectionId, const void* data, size_t length);

                /**
                 * 广播数据到所有连接
                 */
                bstatus_t BroadcastData(const void* data, size_t length);

            private:
                // 网络配置
                NetworkConfig config_;

                // 服务运行状态
                bool isServiceRunning_;

                // 根路径和当前目录
                std::string rootPath_;
                std::string currentDirectory_;

                // 活动连接列表
                std::vector<NetworkConnection> activeConnections_;

                // 内部辅助方法
                bool ValidateNetworkConfig(network_type_t networkType, const std::string& address, const std::string& mac) const;
                bool ValidatePath(const std::string& path) const;
                void UpdateConnectionStatus(const std::string& connectionId, bool isActive);
                void CleanupInactiveConnections();
                bool InitializeNetworkStack();
                void CleanupNetworkResources();
            };

        } // namespace Domain
    } // namespace BiShare
} // namespace OHOS

#endif // NETWORK_SERVICE_H
