#include "bishare_operation_impls.h"
#include "bishare_napi.h"
#include "bishare_logger.h"
#include "bishare_utils.h"
#include "bishare_network.h"

namespace OHOS {
    namespace BiShare {

        static constexpr const char *NETWORK_OPS_TAG = "BiShareNetworkOps";

        // SetNetworkInfoOperation 实现

        bool SetNetworkInfoOperation::ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) {
            size_t argc = 3;
            napi_value argv[3];
            napi_value thisArg;
            void *data;
            
            napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);
            
            if (argc < 2) {
                BiShareLogger::Error(NETWORK_OPS_TAG, "SetNetworkInfo需要网络信息参数");
                return false;
            }

            // 解析网络SSID参数
            size_t ssidLength;
            napi_get_value_string_utf8(env, argv[0], nullptr, 0, &ssidLength);
            char* ssidBuffer = new char[ssidLength + 1];
            napi_get_value_string_utf8(env, argv[0], ssidBuffer, ssidLength + 1, &ssidLength);
            workData->data.stringParam1 = std::string(ssidBuffer);
            delete[] ssidBuffer;

            // 解析网络密码参数
            size_t passwordLength;
            napi_get_value_string_utf8(env, argv[1], nullptr, 0, &passwordLength);
            char* passwordBuffer = new char[passwordLength + 1];
            napi_get_value_string_utf8(env, argv[1], passwordBuffer, passwordLength + 1, &passwordLength);
            workData->data.stringParam2 = std::string(passwordBuffer);
            delete[] passwordBuffer;

            // 如果有回调函数参数
            if (argc >= 3) {
                napi_valuetype valueType;
                napi_typeof(env, argv[2], &valueType);
                if (valueType == napi_function) {
                    napi_create_reference(env, argv[2], 1, &workData->callbackRef);
                }
            }

            return true;
        }

        void SetNetworkInfoOperation::ExecuteOperation(napi_env env, AsyncWorkData* workData) {
            // 检查服务是否已初始化
            if (!CheckServiceInitialized(workData)) {
                return;
            }

            BiShareLogger::Info(NETWORK_OPS_TAG, "设置网络信息: SSID=%s", 
                workData->data.stringParam1.c_str());

            // 通过网络管理器设置网络信息
            auto networkManager = GetNetworkManager();
            workData->result = networkManager->SetNetworkInfo(
                workData->data.stringParam1,  // SSID
                workData->data.stringParam2   // Password
            );

            if (workData->result == BS_OK) {
                workData->successMessage = "网络信息设置成功";
                BiShareLogger::Info(NETWORK_OPS_TAG, "网络信息设置成功");
            } else {
                workData->errorMessage = std::string("网络信息设置失败: ") + 
                    std::string(err2str(workData->result));
                BiShareLogger::Error(NETWORK_OPS_TAG, "网络信息设置失败: %s", 
                    err2str(workData->result));
            }
        }

        napi_value SetNetworkInfoOperation::CreateResult(napi_env env, AsyncWorkData* workData) {
            return CreateStandardResult(env, workData);
        }

    } // namespace BiShare
} // namespace OHOS
