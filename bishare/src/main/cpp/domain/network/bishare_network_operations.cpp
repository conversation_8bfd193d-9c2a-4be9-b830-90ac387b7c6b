#include "bishare_operation_impls.h"
#include "bishare_napi.h"
#include "bishare_logger.h"
#include "bishare_utils.h"
#include "bishare-service.h"

namespace OHOS {
    namespace BiShare {

        static constexpr const char *NETWORK_OPS_TAG = "BiShareNetworkOps";

        // SetNetworkInfoOperation 实现

        bool SetNetworkInfoOperation::ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) {
            size_t argc = 4;
            napi_value argv[4];
            napi_value thisArg;
            void *data;

            napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);

            if (argc < 3) {
                BiShareLogger::Error(NETWORK_OPS_TAG, "SetNetworkInfo需要网络类型、IP地址和MAC地址参数");
                return false;
            }

            // 解析网络类型参数
            int32_t networkType;
            napi_get_value_int32(env, argv[0], &networkType);
            workData->data.intParam1 = networkType;

            // 解析IP地址参数
            size_t addrLength;
            napi_get_value_string_utf8(env, argv[1], nullptr, 0, &addrLength);
            char* addrBuffer = new char[addrLength + 1];
            napi_get_value_string_utf8(env, argv[1], addrBuffer, addrLength + 1, &addrLength);
            workData->data.stringParam1 = std::string(addrBuffer);
            delete[] addrBuffer;

            // 解析MAC地址参数
            size_t macLength;
            napi_get_value_string_utf8(env, argv[2], nullptr, 0, &macLength);
            char* macBuffer = new char[macLength + 1];
            napi_get_value_string_utf8(env, argv[2], macBuffer, macLength + 1, &macLength);
            workData->data.stringParam2 = std::string(macBuffer);
            delete[] macBuffer;

            // 如果有回调函数参数
            if (argc >= 4) {
                napi_valuetype valueType;
                napi_typeof(env, argv[3], &valueType);
                if (valueType == napi_function) {
                    napi_create_reference(env, argv[3], 1, &workData->callbackRef);
                }
            }

            return true;
        }

        void SetNetworkInfoOperation::ExecuteOperation(napi_env env, AsyncWorkData* workData) {
            // 检查服务是否已初始化
            if (!CheckServiceInitialized(workData)) {
                return;
            }

            BiShareLogger::Info(NETWORK_OPS_TAG, "设置网络信息: 类型=%d, IP=%s, MAC=%s",
                workData->data.intParam1,
                workData->data.stringParam1.c_str(),
                workData->data.stringParam2.c_str());

            // 调用原生网络设置函数
            workData->result = bishare_service_set_network_info(
                static_cast<network_type_t>(workData->data.intParam1),  // 网络类型
                workData->data.stringParam1.c_str(),  // IP地址
                workData->data.stringParam2.c_str()   // MAC地址
            );

            if (workData->result == BS_OK) {
                workData->successMessage = "网络信息设置成功";
                BiShareLogger::Info(NETWORK_OPS_TAG, "网络信息设置成功");
            } else {
                workData->errorMessage = std::string("网络信息设置失败: ") + 
                    std::string(err2str(workData->result));
                BiShareLogger::Error(NETWORK_OPS_TAG, "网络信息设置失败: %s", 
                    err2str(workData->result));
            }
        }

        napi_value SetNetworkInfoOperation::CreateResult(napi_env env, AsyncWorkData* workData) {
            return CreateStandardResult(env, workData);
        }

    } // namespace BiShare
} // namespace OHOS
