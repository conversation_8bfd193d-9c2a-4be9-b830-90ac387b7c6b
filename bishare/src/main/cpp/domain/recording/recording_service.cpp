#include "recording_service.h"
#include "../../thirdparty/biservice/include/bishare-service.h"

namespace OHOS {
    namespace BiShare {
        namespace Domain {

            RecordingService::RecordingService() : currentSession_(0) {
                // 初始化录制配置
                config_.screenWidth = 0;
                config_.screenHeight = 0;
                config_.videoWidth = 0;
                config_.videoHeight = 0;
                config_.displayId = 0;
                config_.direction = 0;
                config_.enableAudio = false;
                config_.outputPath = "";
            }

            RecordingService::~RecordingService() {
                Release();
            }

            bool RecordingService::Initialize() {
                // 初始化录制服务
                currentState_.store(RecordingState::IDLE);
                isCapturing_.store(false);
                currentSession_ = 0;
                
                return true;
            }

            void RecordingService::Release() {
                // 停止录制
                if (IsRecording()) {
                    StopScreenRecord(currentSession_, config_.displayId, config_.direction);
                }
                
                // 停止捕获
                if (IsCapturing()) {
                    StopCapture();
                }
                
                // 清理资源
                CleanupRecordingResources();
            }

            bstatus_t RecordingService::StartScreenRecord(int session, int displayId, int direction) {
                if (!ValidateRecordingParameters(session, displayId, direction)) {
                    return BS_INVALID_PARAM;
                }

                if (IsRecording()) {
                    return BS_ALREADY_STARTED; // 已经在录制中
                }

                // 准备录制环境
                if (!PrepareRecordingEnvironment()) {
                    return BS_INIT_FAILED;
                }

                // 调用原生服务
                bstatus_t result = bishare_service_start_screen_record(session, displayId, direction);
                
                if (result == BS_OK) {
                    currentSession_ = session;
                    config_.displayId = displayId;
                    config_.direction = direction;
                    UpdateRecordingState(RecordingState::RECORDING);
                }
                
                return result;
            }

            bstatus_t RecordingService::StopScreenRecord(int session, int displayId, int direction) {
                if (!IsRecording()) {
                    return BS_OK; // 已经停止
                }

                if (!ValidateRecordingParameters(session, displayId, direction)) {
                    return BS_INVALID_PARAM;
                }

                UpdateRecordingState(RecordingState::STOPPING);

                // 调用原生服务
                bstatus_t result = bishare_service_stop_screen_record(session, displayId, direction);
                
                if (result == BS_OK) {
                    UpdateRecordingState(RecordingState::IDLE);
                    CleanupRecordingResources();
                } else {
                    UpdateRecordingState(RecordingState::ERROR);
                }
                
                return result;
            }

            bstatus_t RecordingService::StartCapture() {
                if (IsCapturing()) {
                    return BS_OK; // 已经在捕获中
                }

                // 调用原生服务
                bstatus_t result = bishare_service_start_capture();
                
                if (result == BS_OK) {
                    isCapturing_.store(true);
                }
                
                return result;
            }

            bstatus_t RecordingService::StopCapture() {
                if (!IsCapturing()) {
                    return BS_OK; // 已经停止
                }

                // 这里可以添加停止捕获的逻辑
                // 目前原生服务中没有直接的停止捕获API
                isCapturing_.store(false);
                return BS_OK;
            }

            bstatus_t RecordingService::SetRecordingSize(int screenWidth, int screenHeight, int videoWidth, int videoHeight) {
                if (screenWidth <= 0 || screenHeight <= 0 || videoWidth <= 0 || videoHeight <= 0) {
                    return BS_INVALID_PARAM;
                }

                // 调用原生服务
                bstatus_t result = bishare_service_set_size(screenWidth, screenHeight, videoWidth, videoHeight);
                
                if (result == BS_OK) {
                    config_.screenWidth = screenWidth;
                    config_.screenHeight = screenHeight;
                    config_.videoWidth = videoWidth;
                    config_.videoHeight = videoHeight;
                }
                
                return result;
            }

            bstatus_t RecordingService::SetDefaultAudioOutputDevice(bool enable) {
                // 调用原生服务
                bstatus_t result = bishare_service_set_default_audio_output_device(enable ? BOOL_TRUE : BOOL_FALSE);
                
                if (result == BS_OK) {
                    config_.enableAudio = enable;
                }
                
                return result;
            }

            bstatus_t RecordingService::TakeScreenshot(const std::string& filePath, int top, int bottom, int left, int right) {
                if (!ValidateScreenshotParameters(filePath, top, bottom, left, right)) {
                    return BS_INVALID_PARAM;
                }

                // 调用原生服务
                return bishare_service_screenshot(filePath.c_str(), top, bottom, left, right);
            }

            bool RecordingService::ValidateRecordingParameters(int session, int displayId, int direction) const {
                // 验证会话ID
                if (session < 0) {
                    return false;
                }

                // 验证显示ID
                if (displayId < 0) {
                    return false;
                }

                // 验证方向参数
                if (direction < 0) {
                    return false;
                }

                return true;
            }

            bool RecordingService::ValidateScreenshotParameters(const std::string& filePath, int top, int bottom, int left, int right) const {
                // 验证文件路径
                if (filePath.empty()) {
                    return false;
                }

                // 验证坐标参数
                if (top < 0 || bottom < 0 || left < 0 || right < 0) {
                    return false;
                }

                if (top >= bottom || left >= right) {
                    return false;
                }

                return true;
            }

            void RecordingService::UpdateRecordingState(RecordingState newState) {
                currentState_.store(newState);
            }

            bool RecordingService::PrepareRecordingEnvironment() {
                // 这里可以添加录制前的准备工作
                // 例如检查存储空间、权限等
                return true;
            }

            void RecordingService::CleanupRecordingResources() {
                // 这里可以添加录制后的清理工作
                // 例如释放临时文件、重置状态等
                currentSession_ = 0;
            }

        } // namespace Domain
    } // namespace BiShare
} // namespace OHOS
