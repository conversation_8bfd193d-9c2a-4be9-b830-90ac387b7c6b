#ifndef RECORDING_SERVICE_H
#define RECORDING_SERVICE_H

#include <string>
#include <memory>
#include <atomic>

#include "bishare-define.h"

namespace OHOS {
    namespace BiShare {
        namespace Domain {

            /**
             * 录制配置结构
             */
            struct RecordingConfig {
                int screenWidth;
                int screenHeight;
                int videoWidth;
                int videoHeight;
                int displayId;
                int direction;
                bool enableAudio;
                std::string outputPath;
            };

            /**
             * 录制状态枚举
             */
            enum class RecordingState {
                IDLE,           // 空闲状态
                RECORDING,      // 录制中
                PAUSED,         // 暂停
                STOPPING,       // 停止中
                ERROR           // 错误状态
            };

            /**
             * 录制服务 - 处理屏幕录制相关的业务逻辑
             * 
             * 职责：
             * 1. 管理屏幕录制的生命周期
             * 2. 处理录制配置和参数
             * 3. 管理录制状态
             * 4. 提供截图功能
             * 5. 处理音频输出设备设置
             */
            class RecordingService {
            public:
                RecordingService();
                ~RecordingService();

                /**
                 * 初始化录制服务
                 */
                bool Initialize();

                /**
                 * 释放录制服务
                 */
                void Release();

                /**
                 * 开始屏幕录制
                 */
                bstatus_t StartScreenRecord(int session, int displayId, int direction);

                /**
                 * 停止屏幕录制
                 */
                bstatus_t StopScreenRecord(int session, int displayId, int direction);

                /**
                 * 开始捕获
                 */
                bstatus_t StartCapture();

                /**
                 * 停止捕获
                 */
                bstatus_t StopCapture();

                /**
                 * 设置录制尺寸
                 */
                bstatus_t SetRecordingSize(int screenWidth, int screenHeight, int videoWidth, int videoHeight);

                /**
                 * 设置默认音频输出设备
                 */
                bstatus_t SetDefaultAudioOutputDevice(bool enable);

                /**
                 * 截图
                 */
                bstatus_t TakeScreenshot(const std::string& filePath, int top, int bottom, int left, int right);

                /**
                 * 获取当前录制状态
                 */
                RecordingState GetRecordingState() const { return currentState_.load(); }

                /**
                 * 获取录制配置
                 */
                RecordingConfig GetRecordingConfig() const { return config_; }

                /**
                 * 设置录制配置
                 */
                void SetRecordingConfig(const RecordingConfig& config) { config_ = config; }

                /**
                 * 检查是否正在录制
                 */
                bool IsRecording() const { return currentState_.load() == RecordingState::RECORDING; }

                /**
                 * 检查是否正在捕获
                 */
                bool IsCapturing() const { return isCapturing_.load(); }

            private:
                // 录制配置
                RecordingConfig config_;

                // 录制状态
                std::atomic<RecordingState> currentState_{RecordingState::IDLE};

                // 捕获状态
                std::atomic<bool> isCapturing_{false};

                // 当前会话ID
                int currentSession_;

                // 内部辅助方法
                bool ValidateRecordingParameters(int session, int displayId, int direction) const;
                bool ValidateScreenshotParameters(const std::string& filePath, int top, int bottom, int left, int right) const;
                void UpdateRecordingState(RecordingState newState);
                bool PrepareRecordingEnvironment();
                void CleanupRecordingResources();
            };

        } // namespace Domain
    } // namespace BiShare
} // namespace OHOS

#endif // RECORDING_SERVICE_H
