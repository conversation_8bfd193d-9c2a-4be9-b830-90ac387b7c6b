#ifndef DEVICE_SERVICE_H
#define DEVICE_SERVICE_H

#include <string>
#include <vector>
#include <memory>

#include "bishare-define.h"

namespace OHOS {
    namespace BiShare {
        namespace Domain {

            /**
             * 设备信息结构
             */
            struct DeviceInfo {
                std::string deviceId;
                std::string deviceName;
                std::string deviceModel;
                std::string devicePassword;
                bool isConnected;
            };

            /**
             * 发现的设备信息
             */
            struct DiscoveredDevice {
                std::string deviceId;
                std::string deviceName;
                std::string ipAddress;
                int port;
                bool isAvailable;
            };

            /**
             * 设备服务 - 处理设备相关的业务逻辑
             * 
             * 职责：
             * 1. 管理设备信息（名称、型号、密码等）
             * 2. 处理设备发现和连接
             * 3. 管理设备状态
             * 4. 提供设备操作的业务接口
             */
            class DeviceService {
            public:
                DeviceService();
                ~DeviceService();

                /**
                 * 初始化设备服务
                 */
                bool Initialize();

                /**
                 * 释放设备服务
                 */
                void Release();

                /**
                 * 设置设备信息
                 */
                bstatus_t SetDeviceInfo(const std::string& deviceName, const std::string& devicePassword);

                /**
                 * 设置设备型号
                 */
                bstatus_t SetDeviceModel(const std::string& deviceModel);

                /**
                 * 获取设备型号
                 */
                std::string GetDeviceModel() const;

                /**
                 * 重置设备型号
                 */
                bstatus_t ResetDeviceModel();

                /**
                 * 开始设备发现
                 */
                bstatus_t StartDeviceDiscovery();

                /**
                 * 停止设备发现
                 */
                bstatus_t StopDeviceDiscovery();

                /**
                 * 清除已发现的设备
                 */
                bstatus_t ClearDiscoveredDevices();

                /**
                 * 获取已发现的设备列表
                 */
                bstatus_t GetDiscoveredDevices();

                /**
                 * 查找远程设备
                 */
                bstatus_t FindRemoteDevice(const std::string& pincode);

                /**
                 * 连接到设备
                 */
                bstatus_t ConnectToDevice(const std::string& deviceId, const std::string& password);

                /**
                 * 断开设备连接
                 */
                bstatus_t DisconnectFromDevice(const std::string& deviceId);

                /**
                 * 获取当前设备信息
                 */
                DeviceInfo GetCurrentDeviceInfo() const;

                /**
                 * 获取已发现设备列表
                 */
                std::vector<DiscoveredDevice> GetDiscoveredDeviceList() const;

                /**
                 * 检查设备是否已连接
                 */
                bool IsDeviceConnected(const std::string& deviceId) const;

            private:
                // 当前设备信息
                DeviceInfo currentDevice_;

                // 已发现的设备列表
                std::vector<DiscoveredDevice> discoveredDevices_;

                // 设备发现状态
                bool isDiscovering_;

                // 内部辅助方法
                bool ValidateDeviceInfo(const std::string& deviceName, const std::string& devicePassword) const;
                bool ValidateDeviceModel(const std::string& deviceModel) const;
                void UpdateDeviceStatus(const std::string& deviceId, bool isConnected);
            };

        } // namespace Domain
    } // namespace BiShare
} // namespace OHOS

#endif // DEVICE_SERVICE_H
