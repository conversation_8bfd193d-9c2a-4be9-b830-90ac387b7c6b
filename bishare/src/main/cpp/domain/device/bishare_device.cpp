#include "bishare_device.h"
#include "../../infrastructure/logging/bishare_logger.h"
#include "cJSON.h" // Include cJSON instead of JsonCpp

namespace OHOS {
    namespace BiShare {

        // Define a tag for logging
        static constexpr const char *DEVICE_TAG = "BiShareDevice";

        BiShareDeviceManager::BiShareDeviceManager() {
            // Constructor
        }

        BiShareDeviceManager::~BiShareDeviceManager() {
            // Destructor
        }

        bstatus_t BiShareDeviceManager::SetDeviceModel(const std::string &model) {
            return bishare_service_set_device_model(model.c_str());
        }

        std::string BiShareDeviceManager::GetDeviceModel() {
            const char *model = bishare_service_get_device_model();
            return model != nullptr ? std::string(model) : "";
        }

        bstatus_t BiShareDeviceManager::ResetDeviceModel() { return bishare_service_reset_device_model(); }

        bstatus_t BiShareDeviceManager::SetDeviceInfo(const std::string &name, const std::string &password) {
            return bishare_service_set_device_info(name.c_str(), password.c_str());
        }

        bstatus_t BiShareDeviceManager::FindRemoteDevice(const std::string &pincode) {
            return bishare_service_find_remote_device(pincode.c_str());
        }

        std::string BiShareDeviceManager::GetRootPath() {
            const char *path = bishare_service_get_root_path();
            return path != nullptr ? std::string(path) : "";
        }

        std::string BiShareDeviceManager::GetCurrentDirector() {
            const char *path = bishare_service_get_current_director();
            return path != nullptr ? std::string(path) : "";
        }

        DeviceInfo BiShareDeviceManager::ParseDeviceInfo(const std::string &jsonData) {
            DeviceInfo deviceInfo;

            // Parse JSON using cJSON
            cJSON *root = cJSON_Parse(jsonData.c_str());
            if (root == nullptr) {
                const char *error_ptr = cJSON_GetErrorPtr();
                if (error_ptr != nullptr) {
                    BiShareLogger::Error(DEVICE_TAG, "Failed to parse device info JSON: Error before %s", error_ptr);
                } else {
                    BiShareLogger::Error(DEVICE_TAG, "Failed to parse device info JSON");
                }
                return deviceInfo;
            }

            // Extract device information
            cJSON *idItem = cJSON_GetObjectItem(root, "id");
            if (cJSON_IsString(idItem) && (idItem->valuestring != nullptr)) {
                deviceInfo.id = idItem->valuestring;
            }

            cJSON *nameItem = cJSON_GetObjectItem(root, "name");
            if (cJSON_IsString(nameItem) && (nameItem->valuestring != nullptr)) {
                deviceInfo.name = nameItem->valuestring;
            }

            cJSON *addressItem = cJSON_GetObjectItem(root, "address");
            if (cJSON_IsString(addressItem) && (addressItem->valuestring != nullptr)) {
                deviceInfo.address = addressItem->valuestring;
            }

            cJSON *modelItem = cJSON_GetObjectItem(root, "model");
            if (cJSON_IsString(modelItem) && (modelItem->valuestring != nullptr)) {
                deviceInfo.model = modelItem->valuestring;
            }

            cJSON *pincodeItem = cJSON_GetObjectItem(root, "pincode");
            if (cJSON_IsString(pincodeItem) && (pincodeItem->valuestring != nullptr)) {
                deviceInfo.pincode = pincodeItem->valuestring;
            }

            cJSON *statusItem = cJSON_GetObjectItem(root, "status");
            if (cJSON_IsNumber(statusItem)) {
                deviceInfo.status = statusItem->valueint;
            }

            cJSON *connectedItem = cJSON_GetObjectItem(root, "connected");
            if (cJSON_IsBool(connectedItem)) {
                deviceInfo.connected = cJSON_IsTrue(connectedItem);
            }

            // Free the JSON object
            cJSON_Delete(root);
            return deviceInfo;
        }

        std::vector<DeviceInfo> BiShareDeviceManager::ParseDeviceInfoList(const std::string &jsonData) {
            std::vector<DeviceInfo> deviceList;

            // Parse JSON using cJSON
            cJSON *root = cJSON_Parse(jsonData.c_str());
            if (root == nullptr) {
                const char *error_ptr = cJSON_GetErrorPtr();
                if (error_ptr != nullptr) {
                    BiShareLogger::Error(DEVICE_TAG, "Failed to parse device info list JSON: Error before %s",
                                         error_ptr);
                } else {
                    BiShareLogger::Error(DEVICE_TAG, "Failed to parse device info list JSON");
                }
                return deviceList;
            }

            // Check if the root is an array
            if (!cJSON_IsArray(root)) {
                BiShareLogger::Error(DEVICE_TAG, "Device info list JSON is not an array");
                cJSON_Delete(root);
                return deviceList;
            }

            // Extract device information for each device
            int size = cJSON_GetArraySize(root);
            for (int i = 0; i < size; i++) {
                cJSON *deviceJson = cJSON_GetArrayItem(root, i);
                DeviceInfo deviceInfo;

                cJSON *idItem = cJSON_GetObjectItem(deviceJson, "id");
                if (cJSON_IsString(idItem) && (idItem->valuestring != nullptr)) {
                    deviceInfo.id = idItem->valuestring;
                }

                cJSON *nameItem = cJSON_GetObjectItem(deviceJson, "name");
                if (cJSON_IsString(nameItem) && (nameItem->valuestring != nullptr)) {
                    deviceInfo.name = nameItem->valuestring;
                }

                cJSON *addressItem = cJSON_GetObjectItem(deviceJson, "address");
                if (cJSON_IsString(addressItem) && (addressItem->valuestring != nullptr)) {
                    deviceInfo.address = addressItem->valuestring;
                }

                cJSON *modelItem = cJSON_GetObjectItem(deviceJson, "model");
                if (cJSON_IsString(modelItem) && (modelItem->valuestring != nullptr)) {
                    deviceInfo.model = modelItem->valuestring;
                }

                cJSON *pincodeItem = cJSON_GetObjectItem(deviceJson, "pincode");
                if (cJSON_IsString(pincodeItem) && (pincodeItem->valuestring != nullptr)) {
                    deviceInfo.pincode = pincodeItem->valuestring;
                }

                cJSON *statusItem = cJSON_GetObjectItem(deviceJson, "status");
                if (cJSON_IsNumber(statusItem)) {
                    deviceInfo.status = statusItem->valueint;
                }

                cJSON *connectedItem = cJSON_GetObjectItem(deviceJson, "connected");
                if (cJSON_IsBool(connectedItem)) {
                    deviceInfo.connected = cJSON_IsTrue(connectedItem);
                }

                deviceList.push_back(deviceInfo);
            }

            // Free the JSON object
            cJSON_Delete(root);
            return deviceList;
        }

    } // namespace BiShare
} // namespace OHOS