#include "../../core/operations/bishare_operation_impls.h"
#include "bishare_napi.h"
#include "bishare_logger.h"
#include "bishare_utils.h"
#include "bishare_device.h"

namespace OHOS {
    namespace BiShare {

        static constexpr const char *DEVICE_OPS_TAG = "BiShareDeviceOps";

        // DiscoverDevicesOperation 实现

        bool DiscoverDevicesOperation::ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) {
            size_t argc = 1;
            napi_value argv[1];
            napi_value thisArg;
            void *data;
            
            napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);
            
            // 如果有回调函数参数
            if (argc >= 1) {
                napi_valuetype valueType;
                napi_typeof(env, argv[0], &valueType);
                if (valueType == napi_function) {
                    napi_create_reference(env, argv[0], 1, &workData->callbackRef);
                }
            }

            return true;
        }

        void DiscoverDevicesOperation::ExecuteOperation(napi_env env, AsyncWorkData* workData) {
            // 检查服务是否已初始化
            if (!CheckServiceInitialized(workData)) {
                return;
            }

            BiShareLogger::Info(DEVICE_OPS_TAG, "开始发现设备...");

            // 调用原生设备发现函数
            workData->result = bishare_service_discovery_device();

            BiShareLogger::Info(DEVICE_OPS_TAG, "设备发现结果: %d", static_cast<int>(workData->result));

            if (workData->result == BS_OK) {
                workData->successMessage = "设备发现启动成功";
            } else {
                workData->errorMessage = std::string("设备发现失败: ") + 
                    std::string(err2str(workData->result));
            }
        }

        napi_value DiscoverDevicesOperation::CreateResult(napi_env env, AsyncWorkData* workData) {
            return CreateStandardResult(env, workData);
        }

        // GetDiscoveredDevicesOperation 实现

        bool GetDiscoveredDevicesOperation::ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) {
            size_t argc = 1;
            napi_value argv[1];
            napi_value thisArg;
            void *data;
            
            napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);
            
            // 如果有回调函数参数
            if (argc >= 1) {
                napi_valuetype valueType;
                napi_typeof(env, argv[0], &valueType);
                if (valueType == napi_function) {
                    napi_create_reference(env, argv[0], 1, &workData->callbackRef);
                }
            }

            return true;
        }

        void GetDiscoveredDevicesOperation::ExecuteOperation(napi_env env, AsyncWorkData* workData) {
            // 检查服务是否已初始化
            if (!CheckServiceInitialized(workData)) {
                return;
            }

            BiShareLogger::Info(DEVICE_OPS_TAG, "获取已发现的设备...");

            // 调用原生函数获取设备列表
            workData->result = bishare_service_get_discovery_device();

            BiShareLogger::Info(DEVICE_OPS_TAG, "获取设备列表结果: %d", static_cast<int>(workData->result));

            if (workData->result == BS_OK) {
                workData->successMessage = "获取设备列表成功";
            } else {
                workData->errorMessage = std::string("获取设备列表失败: ") + 
                    std::string(err2str(workData->result));
            }
        }

        napi_value GetDiscoveredDevicesOperation::CreateResult(napi_env env, AsyncWorkData* workData) {
            return CreateStandardResult(env, workData);
        }

        // SetDeviceModelOperation 实现

        bool SetDeviceModelOperation::ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) {
            size_t argc = 2;
            napi_value argv[2];
            napi_value thisArg;
            void *data;
            
            napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);
            
            if (argc < 1) {
                BiShareLogger::Error(DEVICE_OPS_TAG, "SetDeviceModel需要设备模型参数");
                return false;
            }

            // 解析设备模型参数
            size_t modelLength;
            napi_get_value_string_utf8(env, argv[0], nullptr, 0, &modelLength);
            char* modelBuffer = new char[modelLength + 1];
            napi_get_value_string_utf8(env, argv[0], modelBuffer, modelLength + 1, &modelLength);
            workData->data.stringParam1 = std::string(modelBuffer);
            delete[] modelBuffer;

            // 如果有回调函数参数
            if (argc >= 2) {
                napi_valuetype valueType;
                napi_typeof(env, argv[1], &valueType);
                if (valueType == napi_function) {
                    napi_create_reference(env, argv[1], 1, &workData->callbackRef);
                }
            }

            return true;
        }

        void SetDeviceModelOperation::ExecuteOperation(napi_env env, AsyncWorkData* workData) {
            // 检查服务是否已初始化
            if (!CheckServiceInitialized(workData)) {
                return;
            }

            BiShareLogger::Info(DEVICE_OPS_TAG, "设置设备模型: %s", workData->data.stringParam1.c_str());

            // 通过设备管理器设置设备模型
            auto deviceManager = GetDeviceManager();
            workData->result = deviceManager->SetDeviceModel(workData->data.stringParam1);

            if (workData->result == BS_OK) {
                workData->successMessage = "设备模型设置成功";
                BiShareLogger::Info(DEVICE_OPS_TAG, "设备模型设置成功");
            } else {
                workData->errorMessage = std::string("设备模型设置失败: ") + 
                    std::string(err2str(workData->result));
                BiShareLogger::Error(DEVICE_OPS_TAG, "设备模型设置失败: %s", 
                    err2str(workData->result));
            }
        }

        napi_value SetDeviceModelOperation::CreateResult(napi_env env, AsyncWorkData* workData) {
            return CreateStandardResult(env, workData);
        }

        // GetDeviceModelOperation 实现

        bool GetDeviceModelOperation::ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) {
            size_t argc = 1;
            napi_value argv[1];
            napi_value thisArg;
            void *data;
            
            napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);
            
            // 如果有回调函数参数
            if (argc >= 1) {
                napi_valuetype valueType;
                napi_typeof(env, argv[0], &valueType);
                if (valueType == napi_function) {
                    napi_create_reference(env, argv[0], 1, &workData->callbackRef);
                }
            }

            return true;
        }

        void GetDeviceModelOperation::ExecuteOperation(napi_env env, AsyncWorkData* workData) {
            // 检查服务是否已初始化
            if (!CheckServiceInitialized(workData)) {
                return;
            }

            BiShareLogger::Info(DEVICE_OPS_TAG, "获取设备模型...");

            // 通过设备管理器获取设备模型
            auto deviceManager = GetDeviceManager();
            std::string model = deviceManager->GetDeviceModel();
            
            workData->result = BS_OK;
            workData->data.stringParam1 = model;
            workData->successMessage = "获取设备模型成功";
            
            BiShareLogger::Info(DEVICE_OPS_TAG, "获取设备模型成功: %s", model.c_str());
        }

        napi_value GetDeviceModelOperation::CreateResult(napi_env env, AsyncWorkData* workData) {
            napi_value result;
            napi_create_object(env, &result);
            
            napi_value successValue = CreateBoolean(env, workData->result == BS_OK);
            napi_value modelValue = CreateStringUtf8(env, workData->data.stringParam1.c_str());
            napi_value messageValue = CreateStringUtf8(env, workData->successMessage.c_str());
            
            napi_set_named_property(env, result, "success", successValue);
            napi_set_named_property(env, result, "model", modelValue);
            napi_set_named_property(env, result, "message", messageValue);
            
            return result;
        }

        // ResetDeviceModelOperation 实现

        bool ResetDeviceModelOperation::ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) {
            size_t argc = 1;
            napi_value argv[1];
            napi_value thisArg;
            void *data;
            
            napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);
            
            // 如果有回调函数参数
            if (argc >= 1) {
                napi_valuetype valueType;
                napi_typeof(env, argv[0], &valueType);
                if (valueType == napi_function) {
                    napi_create_reference(env, argv[0], 1, &workData->callbackRef);
                }
            }

            return true;
        }

        void ResetDeviceModelOperation::ExecuteOperation(napi_env env, AsyncWorkData* workData) {
            // 检查服务是否已初始化
            if (!CheckServiceInitialized(workData)) {
                return;
            }

            BiShareLogger::Info(DEVICE_OPS_TAG, "重置设备模型...");

            // 通过设备管理器重置设备模型
            auto deviceManager = GetDeviceManager();
            workData->result = deviceManager->ResetDeviceModel();

            if (workData->result == BS_OK) {
                workData->successMessage = "设备模型重置成功";
                BiShareLogger::Info(DEVICE_OPS_TAG, "设备模型重置成功");
            } else {
                workData->errorMessage = std::string("设备模型重置失败: ") + 
                    std::string(err2str(workData->result));
                BiShareLogger::Error(DEVICE_OPS_TAG, "设备模型重置失败: %s", 
                    err2str(workData->result));
            }
        }

        napi_value ResetDeviceModelOperation::CreateResult(napi_env env, AsyncWorkData* workData) {
            return CreateStandardResult(env, workData);
        }

        // SetDeviceInfoOperation 实现

        bool SetDeviceInfoOperation::ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) {
            size_t argc = 3;
            napi_value argv[3];
            napi_value thisArg;
            void *data;

            napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);

            if (argc < 2) {
                BiShareLogger::Error(DEVICE_OPS_TAG, "SetDeviceInfo需要设备名称和密码参数");
                return false;
            }

            // 解析设备名称参数
            size_t nameLength;
            napi_get_value_string_utf8(env, argv[0], nullptr, 0, &nameLength);
            char* nameBuffer = new char[nameLength + 1];
            napi_get_value_string_utf8(env, argv[0], nameBuffer, nameLength + 1, &nameLength);
            workData->data.stringParam1 = std::string(nameBuffer);
            delete[] nameBuffer;

            // 解析密码参数
            size_t passwordLength;
            napi_get_value_string_utf8(env, argv[1], nullptr, 0, &passwordLength);
            char* passwordBuffer = new char[passwordLength + 1];
            napi_get_value_string_utf8(env, argv[1], passwordBuffer, passwordLength + 1, &passwordLength);
            workData->data.stringParam2 = std::string(passwordBuffer);
            delete[] passwordBuffer;

            // 如果有回调函数参数
            if (argc >= 3) {
                napi_valuetype valueType;
                napi_typeof(env, argv[2], &valueType);
                if (valueType == napi_function) {
                    napi_create_reference(env, argv[2], 1, &workData->callbackRef);
                }
            }

            return true;
        }

        void SetDeviceInfoOperation::ExecuteOperation(napi_env env, AsyncWorkData* workData) {
            // 检查服务是否已初始化
            if (!CheckServiceInitialized(workData)) {
                return;
            }

            BiShareLogger::Info(DEVICE_OPS_TAG, "设置设备信息，名称: %s", workData->data.stringParam1.c_str());

            // 通过设备管理器设置设备信息
            auto deviceManager = GetDeviceManager();
            workData->result = deviceManager->SetDeviceInfo(workData->data.stringParam1, workData->data.stringParam2);

            if (workData->result == BS_OK) {
                workData->successMessage = "设备信息设置成功";
                BiShareLogger::Info(DEVICE_OPS_TAG, "设备信息设置成功");
            } else {
                workData->errorMessage = std::string("设备信息设置失败: ") +
                    std::string(err2str(workData->result));
                BiShareLogger::Error(DEVICE_OPS_TAG, "设备信息设置失败: %s",
                    err2str(workData->result));
            }
        }

        napi_value SetDeviceInfoOperation::CreateResult(napi_env env, AsyncWorkData* workData) {
            return CreateStandardResult(env, workData);
        }

        // FindRemoteDeviceOperation 实现

        bool FindRemoteDeviceOperation::ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) {
            size_t argc = 2;
            napi_value argv[2];
            napi_value thisArg;
            void *data;

            napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);

            if (argc < 1) {
                BiShareLogger::Error(DEVICE_OPS_TAG, "FindRemoteDevice需要PIN码参数");
                return false;
            }

            // 解析PIN码参数
            size_t pincodeLength;
            napi_get_value_string_utf8(env, argv[0], nullptr, 0, &pincodeLength);
            char* pincodeBuffer = new char[pincodeLength + 1];
            napi_get_value_string_utf8(env, argv[0], pincodeBuffer, pincodeLength + 1, &pincodeLength);
            workData->data.stringParam1 = std::string(pincodeBuffer);
            delete[] pincodeBuffer;

            // 如果有回调函数参数
            if (argc >= 2) {
                napi_valuetype valueType;
                napi_typeof(env, argv[1], &valueType);
                if (valueType == napi_function) {
                    napi_create_reference(env, argv[1], 1, &workData->callbackRef);
                }
            }

            return true;
        }

        void FindRemoteDeviceOperation::ExecuteOperation(napi_env env, AsyncWorkData* workData) {
            // 检查服务是否已初始化
            if (!CheckServiceInitialized(workData)) {
                return;
            }

            BiShareLogger::Info(DEVICE_OPS_TAG, "查找远程设备，PIN码: %s", workData->data.stringParam1.c_str());

            // 通过设备管理器查找远程设备
            auto deviceManager = GetDeviceManager();
            workData->result = deviceManager->FindRemoteDevice(workData->data.stringParam1);

            if (workData->result == BS_OK) {
                workData->successMessage = "远程设备查找启动成功";
                BiShareLogger::Info(DEVICE_OPS_TAG, "远程设备查找启动成功");
            } else {
                workData->errorMessage = std::string("远程设备查找失败: ") +
                    std::string(err2str(workData->result));
                BiShareLogger::Error(DEVICE_OPS_TAG, "远程设备查找失败: %s",
                    err2str(workData->result));
            }
        }

        napi_value FindRemoteDeviceOperation::CreateResult(napi_env env, AsyncWorkData* workData) {
            return CreateStandardResult(env, workData);
        }

        // GetRootPathOperation 实现

        napi_value GetRootPathOperation::Execute(napi_env env, napi_callback_info info) {
            // 这是一个同步操作
            size_t argc = 1;
            napi_value argv[1];
            napi_value thisArg;
            void *data;

            napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);

            // 检查服务是否已初始化
            if (!BiShareNapi::IsInitialized()) {
                napi_value error, message;
                napi_create_string_utf8(env, "BiShare服务未初始化", NAPI_AUTO_LENGTH, &message);
                napi_create_error(env, nullptr, message, &error);
                return error;
            }

            BiShareLogger::Info(DEVICE_OPS_TAG, "获取根路径...");

            // 通过设备管理器获取根路径
            auto deviceManager = BiShareNapi::GetInstance()->GetDeviceManager();
            std::string rootPath = deviceManager->GetRootPath();

            if (!rootPath.empty()) {
                BiShareLogger::Info(DEVICE_OPS_TAG, "获取根路径成功: %s", rootPath.c_str());
                napi_value result;
                napi_create_string_utf8(env, rootPath.c_str(), NAPI_AUTO_LENGTH, &result);
                return result;
            } else {
                napi_value error, message;
                napi_create_string_utf8(env, "获取根路径失败", NAPI_AUTO_LENGTH, &message);
                napi_create_error(env, nullptr, message, &error);
                return error;
            }
        }

        bool GetRootPathOperation::ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) {
            return true;
        }

        void GetRootPathOperation::ExecuteOperation(napi_env env, AsyncWorkData* workData) {
            // 同步操作中不使用
        }

        napi_value GetRootPathOperation::CreateResult(napi_env env, AsyncWorkData* workData) {
            // 同步操作中不使用
            napi_value result;
            napi_get_undefined(env, &result);
            return result;
        }

        // GetCurrentDirectorOperation 实现

        napi_value GetCurrentDirectorOperation::Execute(napi_env env, napi_callback_info info) {
            // 这是一个同步操作
            size_t argc = 1;
            napi_value argv[1];
            napi_value thisArg;
            void *data;

            napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);

            // 检查服务是否已初始化
            if (!BiShareNapi::IsInitialized()) {
                napi_value error, message;
                napi_create_string_utf8(env, "BiShare服务未初始化", NAPI_AUTO_LENGTH, &message);
                napi_create_error(env, nullptr, message, &error);
                return error;
            }

            BiShareLogger::Info(DEVICE_OPS_TAG, "获取当前目录...");

            // 通过设备管理器获取当前目录
            auto deviceManager = BiShareNapi::GetInstance()->GetDeviceManager();
            std::string currentDir = deviceManager->GetCurrentDirector();

            if (!currentDir.empty()) {
                BiShareLogger::Info(DEVICE_OPS_TAG, "获取当前目录成功: %s", currentDir.c_str());
                napi_value result;
                napi_create_string_utf8(env, currentDir.c_str(), NAPI_AUTO_LENGTH, &result);
                return result;
            } else {
                napi_value error, message;
                napi_create_string_utf8(env, "获取当前目录失败", NAPI_AUTO_LENGTH, &message);
                napi_create_error(env, nullptr, message, &error);
                return error;
            }
        }

        bool GetCurrentDirectorOperation::ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) {
            return true;
        }

        void GetCurrentDirectorOperation::ExecuteOperation(napi_env env, AsyncWorkData* workData) {
            // 同步操作中不使用
        }

        napi_value GetCurrentDirectorOperation::CreateResult(napi_env env, AsyncWorkData* workData) {
            // 同步操作中不使用
            napi_value result;
            napi_get_undefined(env, &result);
            return result;
        }

    } // namespace BiShare
} // namespace OHOS
