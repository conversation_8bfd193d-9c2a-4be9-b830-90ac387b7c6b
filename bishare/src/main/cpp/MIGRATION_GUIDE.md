# BiShare 架构迁移指南

## 📋 迁移概述

本指南说明如何从当前的单体架构迁移到新的分层架构。

## 🔄 迁移步骤

### 第一阶段：创建新架构基础

#### 1. 移动现有文件到新位置

```bash
# 移动工具类
mv utils/bishare_logger.cpp infrastructure/logging/
mv utils/bishare_utils.cpp infrastructure/

# 移动头文件到对应位置
mv include/bishare_device.h domain/device/
mv include/bishare_recording.h domain/recording/
mv include/bishare_callbacks.h core/managers/
```

#### 2. 重构现有类

**BiShareNapi → BiShareNapiInterface**
- 移除业务逻辑，只保留NAPI绑定
- 将业务逻辑委托给BiShareFacade

**BiShareDeviceManager → DeviceService**
- 移动到domain/device/目录
- 重构为纯业务逻辑类

**BiShareRecordManager → RecordingService**
- 移动到domain/recording/目录
- 重构为纯业务逻辑类

### 第二阶段：实现新组件

#### 1. 实现核心组件

```cpp
// 1. 实现BiShareFacade
// bishare/src/main/cpp/core/facade/bishare_facade.cpp

// 2. 实现ServiceManager
// bishare/src/main/cpp/core/managers/service_manager.cpp

// 3. 实现CallbackManager
// bishare/src/main/cpp/core/managers/callback_manager.cpp

// 4. 实现OperationFactory
// bishare/src/main/cpp/core/operations/operation_factory.cpp
```

#### 2. 实现领域服务

```cpp
// 1. 实现DeviceService
// bishare/src/main/cpp/domain/device/device_service.cpp

// 2. 实现RecordingService
// bishare/src/main/cpp/domain/recording/recording_service.cpp

// 3. 实现NetworkService
// bishare/src/main/cpp/domain/network/network_service.cpp
```

#### 3. 实现基础设施

```cpp
// 1. 实现AsyncExecutor
// bishare/src/main/cpp/infrastructure/async/async_executor.cpp

// 2. 重构日志系统
// bishare/src/main/cpp/infrastructure/logging/logger.cpp
```

### 第三阶段：重构NAPI接口

#### 1. 简化BiShareNapi类

```cpp
// 旧代码
class BiShareNapi {
    // 大量业务逻辑方法
    static napi_value SetDeviceInfo(napi_env env, napi_callback_info info);
    // ... 其他方法
};

// 新代码
class BiShareNapiInterface {
    // 只保留NAPI绑定逻辑
    static napi_value SetDeviceInfo(napi_env env, napi_callback_info info) {
        auto& facade = BiShareFacade::GetInstance();
        auto factory = facade.GetOperationFactory();
        // 委托给操作工厂处理
    }
};
```

#### 2. 使用操作工厂模式

```cpp
// 旧代码：直接调用原生函数
napi_value BiShareNapi::SetDeviceInfo(napi_env env, napi_callback_info info) {
    // 解析参数
    // 直接调用 bishare_service_set_device_info
    // 处理结果
}

// 新代码：使用操作工厂
napi_value BiShareNapiInterface::SetDeviceInfo(napi_env env, napi_callback_info info) {
    auto& facade = BiShareFacade::GetInstance();
    auto factory = facade.GetOperationFactory();
    
    // 解析参数
    auto params = factory->ParseOperationParams(env, info, OperationType::SET_DEVICE_INFO);
    
    // 执行操作
    auto result = factory->ExecuteOperation(OperationType::SET_DEVICE_INFO, params);
    
    // 转换结果
    return factory->ConvertResultToNapi(env, result, OperationType::SET_DEVICE_INFO);
}
```

### 第四阶段：更新构建配置

#### 1. 更新CMakeLists.txt

```cmake
# 添加新的源文件目录
file(GLOB_RECURSE CORE_SOURCES "core/*.cpp")
file(GLOB_RECURSE DOMAIN_SOURCES "domain/*.cpp")
file(GLOB_RECURSE INFRASTRUCTURE_SOURCES "infrastructure/*.cpp")
file(GLOB_RECURSE INTERFACES_SOURCES "interfaces/*.cpp")

# 添加包含目录
target_include_directories(bishare_napi PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/core
    ${CMAKE_CURRENT_SOURCE_DIR}/domain
    ${CMAKE_CURRENT_SOURCE_DIR}/infrastructure
    ${CMAKE_CURRENT_SOURCE_DIR}/interfaces
)
```

#### 2. 更新模块入口

```cpp
// 旧的入口：bishare_napi.cpp
NAPI_MODULE(NODE_GYP_MODULE_NAME, BiShareNapi::Init)

// 新的入口：bishare_module.cpp
extern "C" __attribute__((constructor)) void RegisterBiShareModule(void) {
    napi_module_register(&bishareModule);
}
```

## 🔍 迁移检查清单

### ✅ 代码迁移
- [ ] 移动文件到新目录结构
- [ ] 重构BiShareNapi类
- [ ] 实现BiShareFacade
- [ ] 实现ServiceManager
- [ ] 实现CallbackManager
- [ ] 实现OperationFactory
- [ ] 实现领域服务类
- [ ] 实现基础设施组件

### ✅ 构建配置
- [ ] 更新CMakeLists.txt
- [ ] 更新包含路径
- [ ] 更新链接配置
- [ ] 测试编译

### ✅ 功能验证
- [ ] 验证所有API功能正常
- [ ] 验证事件回调机制
- [ ] 验证异步操作
- [ ] 验证错误处理

### ✅ 性能测试
- [ ] 对比迁移前后性能
- [ ] 内存使用情况
- [ ] 响应时间测试

## 🚨 注意事项

### 1. 向后兼容性
- 保持JavaScript API接口不变
- 确保现有功能正常工作
- 渐进式迁移，避免破坏性变更

### 2. 错误处理
- 统一错误处理机制
- 保持错误信息的一致性
- 确保异常安全

### 3. 内存管理
- 使用智能指针管理对象生命周期
- 避免内存泄漏
- 注意循环引用问题

### 4. 线程安全
- 确保多线程环境下的安全性
- 使用适当的同步机制
- 避免死锁

## 📈 迁移收益

### 1. 代码质量提升
- 更清晰的代码结构
- 更好的可维护性
- 更高的可测试性

### 2. 开发效率提升
- 更容易添加新功能
- 更容易定位和修复问题
- 更好的团队协作

### 3. 系统稳定性提升
- 更好的错误隔离
- 更强的容错能力
- 更稳定的性能表现

## 🔧 工具和脚本

可以创建一些辅助脚本来帮助迁移：

```bash
#!/bin/bash
# migrate_files.sh - 文件迁移脚本

# 创建新目录结构
mkdir -p core/{facade,managers,operations}
mkdir -p domain/{device,recording,network}
mkdir -p infrastructure/{async,logging,threading}
mkdir -p interfaces/{napi,service}

# 移动文件
# ... 具体的移动命令
```
