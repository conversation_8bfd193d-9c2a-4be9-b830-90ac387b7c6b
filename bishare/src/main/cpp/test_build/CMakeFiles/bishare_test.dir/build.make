# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/local/Cellar/cmake/3.27.6/bin/cmake

# The command to remove a file.
RM = /usr/local/Cellar/cmake/3.27.6/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/DevEcoStudioProjects/ScreenDemo/bishare/src/main/cpp/test_build

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/DevEcoStudioProjects/ScreenDemo/bishare/src/main/cpp/test_build

# Include any dependencies generated for this target.
include CMakeFiles/bishare_test.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/bishare_test.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/bishare_test.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/bishare_test.dir/flags.make

CMakeFiles/bishare_test.dir/test_build.cpp.o: CMakeFiles/bishare_test.dir/flags.make
CMakeFiles/bishare_test.dir/test_build.cpp.o: test_build.cpp
CMakeFiles/bishare_test.dir/test_build.cpp.o: CMakeFiles/bishare_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/DevEcoStudioProjects/ScreenDemo/bishare/src/main/cpp/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/bishare_test.dir/test_build.cpp.o"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/bishare_test.dir/test_build.cpp.o -MF CMakeFiles/bishare_test.dir/test_build.cpp.o.d -o CMakeFiles/bishare_test.dir/test_build.cpp.o -c /Users/<USER>/DevEcoStudioProjects/ScreenDemo/bishare/src/main/cpp/test_build/test_build.cpp

CMakeFiles/bishare_test.dir/test_build.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/bishare_test.dir/test_build.cpp.i"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/DevEcoStudioProjects/ScreenDemo/bishare/src/main/cpp/test_build/test_build.cpp > CMakeFiles/bishare_test.dir/test_build.cpp.i

CMakeFiles/bishare_test.dir/test_build.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/bishare_test.dir/test_build.cpp.s"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/DevEcoStudioProjects/ScreenDemo/bishare/src/main/cpp/test_build/test_build.cpp -o CMakeFiles/bishare_test.dir/test_build.cpp.s

# Object files for target bishare_test
bishare_test_OBJECTS = \
"CMakeFiles/bishare_test.dir/test_build.cpp.o"

# External object files for target bishare_test
bishare_test_EXTERNAL_OBJECTS =

bishare_test: CMakeFiles/bishare_test.dir/test_build.cpp.o
bishare_test: CMakeFiles/bishare_test.dir/build.make
bishare_test: CMakeFiles/bishare_test.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/DevEcoStudioProjects/ScreenDemo/bishare/src/main/cpp/test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable bishare_test"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/bishare_test.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/bishare_test.dir/build: bishare_test
.PHONY : CMakeFiles/bishare_test.dir/build

CMakeFiles/bishare_test.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/bishare_test.dir/cmake_clean.cmake
.PHONY : CMakeFiles/bishare_test.dir/clean

CMakeFiles/bishare_test.dir/depend:
	cd /Users/<USER>/DevEcoStudioProjects/ScreenDemo/bishare/src/main/cpp/test_build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/DevEcoStudioProjects/ScreenDemo/bishare/src/main/cpp/test_build /Users/<USER>/DevEcoStudioProjects/ScreenDemo/bishare/src/main/cpp/test_build /Users/<USER>/DevEcoStudioProjects/ScreenDemo/bishare/src/main/cpp/test_build /Users/<USER>/DevEcoStudioProjects/ScreenDemo/bishare/src/main/cpp/test_build /Users/<USER>/DevEcoStudioProjects/ScreenDemo/bishare/src/main/cpp/test_build/CMakeFiles/bishare_test.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/bishare_test.dir/depend

