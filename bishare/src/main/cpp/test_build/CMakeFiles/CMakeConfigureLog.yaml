
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/Cellar/cmake/3.27.6/share/cmake/Modules/CMakeDetermineSystem.cmake:211 (message)"
      - "CMakeLists.txt:3 (project)"
    message: |
      The system is: Darwin - 22.6.0 - x86_64
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/Cellar/cmake/3.27.6/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/usr/local/Cellar/cmake/3.27.6/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/usr/local/Cellar/cmake/3.27.6/share/cmake/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
      Compiler: /Library/Developer/CommandLineTools/usr/bin/cc 
      Build flags: 
      Id flags:  
      
      The output was:
      1
      ld: library not found for -lSystem
      clang: error: linker command failed with exit code 1 (use -v to see invocation)
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/Cellar/cmake/3.27.6/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/usr/local/Cellar/cmake/3.27.6/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/usr/local/Cellar/cmake/3.27.6/share/cmake/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: /Library/Developer/CommandLineTools/usr/bin/cc 
      Build flags: 
      Id flags: -c 
      
      The output was:
      0
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CMakeCCompilerId.o"
      
      The C compiler identification is AppleClang, found in:
        /Users/<USER>/DevEcoStudioProjects/ScreenDemo/bishare/src/main/cpp/test_build/CMakeFiles/3.27.6/CompilerIdC/CMakeCCompilerId.o
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/Cellar/cmake/3.27.6/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/usr/local/Cellar/cmake/3.27.6/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/usr/local/Cellar/cmake/3.27.6/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: /Library/Developer/CommandLineTools/usr/bin/c++ 
      Build flags: 
      Id flags:  
      
      The output was:
      1
      ld: library not found for -lc++
      clang: error: linker command failed with exit code 1 (use -v to see invocation)
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/Cellar/cmake/3.27.6/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/usr/local/Cellar/cmake/3.27.6/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/usr/local/Cellar/cmake/3.27.6/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: /Library/Developer/CommandLineTools/usr/bin/c++ 
      Build flags: 
      Id flags: -c 
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CMakeCXXCompilerId.o"
      
      The CXX compiler identification is AppleClang, found in:
        /Users/<USER>/DevEcoStudioProjects/ScreenDemo/bishare/src/main/cpp/test_build/CMakeFiles/3.27.6/CompilerIdCXX/CMakeCXXCompilerId.o
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/Cellar/cmake/3.27.6/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:57 (try_compile)"
      - "/usr/local/Cellar/cmake/3.27.6/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "/Users/<USER>/DevEcoStudioProjects/ScreenDemo/bishare/src/main/cpp/test_build/CMakeFiles/CMakeScratch/TryCompile-dHjK21"
      binary: "/Users/<USER>/DevEcoStudioProjects/ScreenDemo/bishare/src/main/cpp/test_build/CMakeFiles/CMakeScratch/TryCompile-dHjK21"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: "/Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/DevEcoStudioProjects/ScreenDemo/bishare/src/main/cpp/test_build/CMakeFiles/CMakeScratch/TryCompile-dHjK21'
        
        Run Build Command(s): /usr/local/Cellar/cmake/3.27.6/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_e5f41/fast
        /Library/Developer/CommandLineTools/usr/bin/make  -f CMakeFiles/cmTC_e5f41.dir/build.make CMakeFiles/cmTC_e5f41.dir/build
        Building C object CMakeFiles/cmTC_e5f41.dir/CMakeCCompilerABI.c.o
        /Library/Developer/CommandLineTools/usr/bin/cc   -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk   -v -Wl,-v -MD -MT CMakeFiles/cmTC_e5f41.dir/CMakeCCompilerABI.c.o -MF CMakeFiles/cmTC_e5f41.dir/CMakeCCompilerABI.c.o.d -o CMakeFiles/cmTC_e5f41.dir/CMakeCCompilerABI.c.o -c /usr/local/Cellar/cmake/3.27.6/share/cmake/Modules/CMakeCCompilerABI.c
        Apple clang version 12.0.5 (clang-1205.0.22.9)
        Target: x86_64-apple-darwin22.6.0
        Thread model: posix
        InstalledDir: /Library/Developer/CommandLineTools/usr/bin
        clang: warning: -Wl,-v: 'linker' input unused [-Wunused-command-line-argument]
         "/Library/Developer/CommandLineTools/usr/bin/clang" -cc1 -triple x86_64-apple-macosx11.3.0 -Wdeprecated-objc-isa-usage -Werror=deprecated-objc-isa-usage -Werror=implicit-function-declaration -emit-obj -mrelax-all -disable-free -disable-llvm-verifier -discard-value-names -main-file-name CMakeCCompilerABI.c -mrelocation-model pic -pic-level 2 -mframe-pointer=all -fno-strict-return -fno-rounding-math -munwind-tables -target-sdk-version=11.3 -fvisibility-inlines-hidden-static-local-var -target-cpu penryn -debugger-tuning=lldb -target-linker-version 650.9 -v -resource-dir /Library/Developer/CommandLineTools/usr/lib/clang/12.0.5 -dependency-file CMakeFiles/cmTC_e5f41.dir/CMakeCCompilerABI.c.o.d -skip-unused-modulemap-deps -MT CMakeFiles/cmTC_e5f41.dir/CMakeCCompilerABI.c.o -sys-header-deps -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk -internal-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/usr/local/include -internal-isystem /Library/Developer/CommandLineTools/usr/lib/clang/12.0.5/include -internal-externc-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/usr/include -internal-externc-isystem /Library/Developer/CommandLineTools/usr/include -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -fdebug-compilation-dir /Users/<USER>/DevEcoStudioProjects/ScreenDemo/bishare/src/main/cpp/test_build/CMakeFiles/CMakeScratch/TryCompile-dHjK21 -ferror-limit 19 -stack-protector 1 -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fmax-type-align=16 -fcommon -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation -fno-odr-hash-protocols -mllvm -disable-aligned-alloc-awareness=1 -o CMakeFiles/cmTC_e5f41.dir/CMakeCCompilerABI.c.o -x c /usr/local/Cellar/cmake/3.27.6/share/cmake/Modules/CMakeCCompilerABI.c
        clang -cc1 version 12.0.5 (clang-1205.0.22.9) default target x86_64-apple-darwin22.6.0
        ignoring nonexistent directory "/Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/usr/local/include"
        ignoring nonexistent directory "/Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/Library/Frameworks"
        #include "..." search starts here:
        #include <...> search starts here:
         /Library/Developer/CommandLineTools/usr/lib/clang/12.0.5/include
         /Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/usr/include
         /Library/Developer/CommandLineTools/usr/include
         /Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/System/Library/Frameworks (framework directory)
        End of search list.
        Linking C executable cmTC_e5f41
        /usr/local/Cellar/cmake/3.27.6/bin/cmake -E cmake_link_script CMakeFiles/cmTC_e5f41.dir/link.txt --verbose=1
        /Library/Developer/CommandLineTools/usr/bin/cc  -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk -Wl,-search_paths_first -Wl,-headerpad_max_install_names  -v -Wl,-v CMakeFiles/cmTC_e5f41.dir/CMakeCCompilerABI.c.o -o cmTC_e5f41 
        Apple clang version 12.0.5 (clang-1205.0.22.9)
        Target: x86_64-apple-darwin22.6.0
        Thread model: posix
        InstalledDir: /Library/Developer/CommandLineTools/usr/bin
         "/Library/Developer/CommandLineTools/usr/bin/ld" -demangle -lto_library /Library/Developer/CommandLineTools/usr/lib/libLTO.dylib -dynamic -arch x86_64 -platform_version macos 11.3.0 11.3 -syslibroot /Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk -o cmTC_e5f41 -search_paths_first -headerpad_max_install_names -v CMakeFiles/cmTC_e5f41.dir/CMakeCCompilerABI.c.o -lSystem /Library/Developer/CommandLineTools/usr/lib/clang/12.0.5/lib/darwin/libclang_rt.osx.a
        @(#)PROGRAM:ld  PROJECT:ld64-650.9
        BUILD 00:19:30 Mar 17 2021
        configured to support archs: armv6 armv7 armv7s arm64 arm64e arm64_32 i386 x86_64 x86_64h armv6m armv7k armv7m armv7em
        Library search paths:
        	/Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/usr/lib
        Framework search paths:
        	/Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/System/Library/Frameworks/
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/Cellar/cmake/3.27.6/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:127 (message)"
      - "/usr/local/Cellar/cmake/3.27.6/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Parsed C implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [/Library/Developer/CommandLineTools/usr/lib/clang/12.0.5/include]
          add: [/Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/usr/include]
          add: [/Library/Developer/CommandLineTools/usr/include]
        end of search list found
        collapse include dir [/Library/Developer/CommandLineTools/usr/lib/clang/12.0.5/include] ==> [/Library/Developer/CommandLineTools/usr/lib/clang/12.0.5/include]
        collapse include dir [/Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/usr/include] ==> [/Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/usr/include]
        collapse include dir [/Library/Developer/CommandLineTools/usr/include] ==> [/Library/Developer/CommandLineTools/usr/include]
        implicit include dirs: [/Library/Developer/CommandLineTools/usr/lib/clang/12.0.5/include;/Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/usr/include;/Library/Developer/CommandLineTools/usr/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/Cellar/cmake/3.27.6/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:152 (message)"
      - "/usr/local/Cellar/cmake/3.27.6/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        ignore line: [Change Dir: '/Users/<USER>/DevEcoStudioProjects/ScreenDemo/bishare/src/main/cpp/test_build/CMakeFiles/CMakeScratch/TryCompile-dHjK21']
        ignore line: []
        ignore line: [Run Build Command(s): /usr/local/Cellar/cmake/3.27.6/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_e5f41/fast]
        ignore line: [/Library/Developer/CommandLineTools/usr/bin/make  -f CMakeFiles/cmTC_e5f41.dir/build.make CMakeFiles/cmTC_e5f41.dir/build]
        ignore line: [Building C object CMakeFiles/cmTC_e5f41.dir/CMakeCCompilerABI.c.o]
        ignore line: [/Library/Developer/CommandLineTools/usr/bin/cc   -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk   -v -Wl -v -MD -MT CMakeFiles/cmTC_e5f41.dir/CMakeCCompilerABI.c.o -MF CMakeFiles/cmTC_e5f41.dir/CMakeCCompilerABI.c.o.d -o CMakeFiles/cmTC_e5f41.dir/CMakeCCompilerABI.c.o -c /usr/local/Cellar/cmake/3.27.6/share/cmake/Modules/CMakeCCompilerABI.c]
        ignore line: [Apple clang version 12.0.5 (clang-1205.0.22.9)]
        ignore line: [Target: x86_64-apple-darwin22.6.0]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: /Library/Developer/CommandLineTools/usr/bin]
        ignore line: [clang: warning: -Wl -v: 'linker' input unused [-Wunused-command-line-argument]]
        ignore line: [ "/Library/Developer/CommandLineTools/usr/bin/clang" -cc1 -triple x86_64-apple-macosx11.3.0 -Wdeprecated-objc-isa-usage -Werror=deprecated-objc-isa-usage -Werror=implicit-function-declaration -emit-obj -mrelax-all -disable-free -disable-llvm-verifier -discard-value-names -main-file-name CMakeCCompilerABI.c -mrelocation-model pic -pic-level 2 -mframe-pointer=all -fno-strict-return -fno-rounding-math -munwind-tables -target-sdk-version=11.3 -fvisibility-inlines-hidden-static-local-var -target-cpu penryn -debugger-tuning=lldb -target-linker-version 650.9 -v -resource-dir /Library/Developer/CommandLineTools/usr/lib/clang/12.0.5 -dependency-file CMakeFiles/cmTC_e5f41.dir/CMakeCCompilerABI.c.o.d -skip-unused-modulemap-deps -MT CMakeFiles/cmTC_e5f41.dir/CMakeCCompilerABI.c.o -sys-header-deps -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk -internal-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/usr/local/include -internal-isystem /Library/Developer/CommandLineTools/usr/lib/clang/12.0.5/include -internal-externc-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/usr/include -internal-externc-isystem /Library/Developer/CommandLineTools/usr/include -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -fdebug-compilation-dir /Users/<USER>/DevEcoStudioProjects/ScreenDemo/bishare/src/main/cpp/test_build/CMakeFiles/CMakeScratch/TryCompile-dHjK21 -ferror-limit 19 -stack-protector 1 -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fmax-type-align=16 -fcommon -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation -fno-odr-hash-protocols -mllvm -disable-aligned-alloc-awareness=1 -o CMakeFiles/cmTC_e5f41.dir/CMakeCCompilerABI.c.o -x c /usr/local/Cellar/cmake/3.27.6/share/cmake/Modules/CMakeCCompilerABI.c]
        ignore line: [clang -cc1 version 12.0.5 (clang-1205.0.22.9) default target x86_64-apple-darwin22.6.0]
        ignore line: [ignoring nonexistent directory "/Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/usr/local/include"]
        ignore line: [ignoring nonexistent directory "/Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/Library/Frameworks"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ /Library/Developer/CommandLineTools/usr/lib/clang/12.0.5/include]
        ignore line: [ /Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/usr/include]
        ignore line: [ /Library/Developer/CommandLineTools/usr/include]
        ignore line: [ /Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/System/Library/Frameworks (framework directory)]
        ignore line: [End of search list.]
        ignore line: [Linking C executable cmTC_e5f41]
        ignore line: [/usr/local/Cellar/cmake/3.27.6/bin/cmake -E cmake_link_script CMakeFiles/cmTC_e5f41.dir/link.txt --verbose=1]
        ignore line: [/Library/Developer/CommandLineTools/usr/bin/cc  -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk -Wl -search_paths_first -Wl -headerpad_max_install_names  -v -Wl -v CMakeFiles/cmTC_e5f41.dir/CMakeCCompilerABI.c.o -o cmTC_e5f41 ]
        ignore line: [Apple clang version 12.0.5 (clang-1205.0.22.9)]
        ignore line: [Target: x86_64-apple-darwin22.6.0]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: /Library/Developer/CommandLineTools/usr/bin]
        link line: [ "/Library/Developer/CommandLineTools/usr/bin/ld" -demangle -lto_library /Library/Developer/CommandLineTools/usr/lib/libLTO.dylib -dynamic -arch x86_64 -platform_version macos 11.3.0 11.3 -syslibroot /Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk -o cmTC_e5f41 -search_paths_first -headerpad_max_install_names -v CMakeFiles/cmTC_e5f41.dir/CMakeCCompilerABI.c.o -lSystem /Library/Developer/CommandLineTools/usr/lib/clang/12.0.5/lib/darwin/libclang_rt.osx.a]
          arg [/Library/Developer/CommandLineTools/usr/bin/ld] ==> ignore
          arg [-demangle] ==> ignore
          arg [-lto_library] ==> ignore, skip following value
          arg [/Library/Developer/CommandLineTools/usr/lib/libLTO.dylib] ==> skip value of -lto_library
          arg [-dynamic] ==> ignore
          arg [-arch] ==> ignore
          arg [x86_64] ==> ignore
          arg [-platform_version] ==> ignore
          arg [macos] ==> ignore
          arg [11.3.0] ==> ignore
          arg [11.3] ==> ignore
          arg [-syslibroot] ==> ignore
          arg [/Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk] ==> ignore
          arg [-o] ==> ignore
          arg [cmTC_e5f41] ==> ignore
          arg [-search_paths_first] ==> ignore
          arg [-headerpad_max_install_names] ==> ignore
          arg [-v] ==> ignore
          arg [CMakeFiles/cmTC_e5f41.dir/CMakeCCompilerABI.c.o] ==> ignore
          arg [-lSystem] ==> lib [System]
          arg [/Library/Developer/CommandLineTools/usr/lib/clang/12.0.5/lib/darwin/libclang_rt.osx.a] ==> lib [/Library/Developer/CommandLineTools/usr/lib/clang/12.0.5/lib/darwin/libclang_rt.osx.a]
        Library search paths: [;/Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/usr/lib]
        Framework search paths: [;/Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/System/Library/Frameworks/]
        remove lib [System]
        remove lib [/Library/Developer/CommandLineTools/usr/lib/clang/12.0.5/lib/darwin/libclang_rt.osx.a]
        collapse library dir [/Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/usr/lib] ==> [/Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/usr/lib]
        collapse framework dir [/Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/System/Library/Frameworks/] ==> [/Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/System/Library/Frameworks]
        implicit libs: []
        implicit objs: []
        implicit dirs: [/Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/usr/lib]
        implicit fwks: [/Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/System/Library/Frameworks]
      
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/Cellar/cmake/3.27.6/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:57 (try_compile)"
      - "/usr/local/Cellar/cmake/3.27.6/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "/Users/<USER>/DevEcoStudioProjects/ScreenDemo/bishare/src/main/cpp/test_build/CMakeFiles/CMakeScratch/TryCompile-WmTPog"
      binary: "/Users/<USER>/DevEcoStudioProjects/ScreenDemo/bishare/src/main/cpp/test_build/CMakeFiles/CMakeScratch/TryCompile-WmTPog"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: "/Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/DevEcoStudioProjects/ScreenDemo/bishare/src/main/cpp/test_build/CMakeFiles/CMakeScratch/TryCompile-WmTPog'
        
        Run Build Command(s): /usr/local/Cellar/cmake/3.27.6/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_be629/fast
        /Library/Developer/CommandLineTools/usr/bin/make  -f CMakeFiles/cmTC_be629.dir/build.make CMakeFiles/cmTC_be629.dir/build
        Building CXX object CMakeFiles/cmTC_be629.dir/CMakeCXXCompilerABI.cpp.o
        /Library/Developer/CommandLineTools/usr/bin/c++   -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk   -v -Wl,-v -MD -MT CMakeFiles/cmTC_be629.dir/CMakeCXXCompilerABI.cpp.o -MF CMakeFiles/cmTC_be629.dir/CMakeCXXCompilerABI.cpp.o.d -o CMakeFiles/cmTC_be629.dir/CMakeCXXCompilerABI.cpp.o -c /usr/local/Cellar/cmake/3.27.6/share/cmake/Modules/CMakeCXXCompilerABI.cpp
        Apple clang version 12.0.5 (clang-1205.0.22.9)
        Target: x86_64-apple-darwin22.6.0
        Thread model: posix
        InstalledDir: /Library/Developer/CommandLineTools/usr/bin
        clang: warning: -Wl,-v: 'linker' input unused [-Wunused-command-line-argument]
         "/Library/Developer/CommandLineTools/usr/bin/clang" -cc1 -triple x86_64-apple-macosx11.3.0 -Wdeprecated-objc-isa-usage -Werror=deprecated-objc-isa-usage -Werror=implicit-function-declaration -emit-obj -mrelax-all -disable-free -disable-llvm-verifier -discard-value-names -main-file-name CMakeCXXCompilerABI.cpp -mrelocation-model pic -pic-level 2 -mframe-pointer=all -fno-strict-return -fno-rounding-math -munwind-tables -target-sdk-version=11.3 -fvisibility-inlines-hidden-static-local-var -target-cpu penryn -debugger-tuning=lldb -target-linker-version 650.9 -v -resource-dir /Library/Developer/CommandLineTools/usr/lib/clang/12.0.5 -dependency-file CMakeFiles/cmTC_be629.dir/CMakeCXXCompilerABI.cpp.o.d -skip-unused-modulemap-deps -MT CMakeFiles/cmTC_be629.dir/CMakeCXXCompilerABI.cpp.o -sys-header-deps -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk -stdlib=libc++ -internal-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/usr/include/c++/v1 -internal-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/usr/local/include -internal-isystem /Library/Developer/CommandLineTools/usr/lib/clang/12.0.5/include -internal-externc-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/usr/include -internal-externc-isystem /Library/Developer/CommandLineTools/usr/include -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -fdeprecated-macro -fdebug-compilation-dir /Users/<USER>/DevEcoStudioProjects/ScreenDemo/bishare/src/main/cpp/test_build/CMakeFiles/CMakeScratch/TryCompile-WmTPog -ferror-limit 19 -stack-protector 1 -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fcxx-exceptions -fexceptions -fmax-type-align=16 -fcommon -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation -fno-odr-hash-protocols -mllvm -disable-aligned-alloc-awareness=1 -o CMakeFiles/cmTC_be629.dir/CMakeCXXCompilerABI.cpp.o -x c++ /usr/local/Cellar/cmake/3.27.6/share/cmake/Modules/CMakeCXXCompilerABI.cpp
        clang -cc1 version 12.0.5 (clang-1205.0.22.9) default target x86_64-apple-darwin22.6.0
        ignoring nonexistent directory "/Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/usr/local/include"
        ignoring nonexistent directory "/Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/Library/Frameworks"
        #include "..." search starts here:
        #include <...> search starts here:
         /Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/usr/include/c++/v1
         /Library/Developer/CommandLineTools/usr/lib/clang/12.0.5/include
         /Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/usr/include
         /Library/Developer/CommandLineTools/usr/include
         /Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/System/Library/Frameworks (framework directory)
        End of search list.
        Linking CXX executable cmTC_be629
        /usr/local/Cellar/cmake/3.27.6/bin/cmake -E cmake_link_script CMakeFiles/cmTC_be629.dir/link.txt --verbose=1
        /Library/Developer/CommandLineTools/usr/bin/c++  -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk -Wl,-search_paths_first -Wl,-headerpad_max_install_names  -v -Wl,-v CMakeFiles/cmTC_be629.dir/CMakeCXXCompilerABI.cpp.o -o cmTC_be629 
        Apple clang version 12.0.5 (clang-1205.0.22.9)
        Target: x86_64-apple-darwin22.6.0
        Thread model: posix
        InstalledDir: /Library/Developer/CommandLineTools/usr/bin
         "/Library/Developer/CommandLineTools/usr/bin/ld" -demangle -lto_library /Library/Developer/CommandLineTools/usr/lib/libLTO.dylib -dynamic -arch x86_64 -platform_version macos 11.3.0 11.3 -syslibroot /Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk -o cmTC_be629 -search_paths_first -headerpad_max_install_names -v CMakeFiles/cmTC_be629.dir/CMakeCXXCompilerABI.cpp.o -lc++ -lSystem /Library/Developer/CommandLineTools/usr/lib/clang/12.0.5/lib/darwin/libclang_rt.osx.a
        @(#)PROGRAM:ld  PROJECT:ld64-650.9
        BUILD 00:19:30 Mar 17 2021
        configured to support archs: armv6 armv7 armv7s arm64 arm64e arm64_32 i386 x86_64 x86_64h armv6m armv7k armv7m armv7em
        Library search paths:
        	/Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/usr/lib
        Framework search paths:
        	/Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/System/Library/Frameworks/
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/Cellar/cmake/3.27.6/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:127 (message)"
      - "/usr/local/Cellar/cmake/3.27.6/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Parsed CXX implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [/Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/usr/include/c++/v1]
          add: [/Library/Developer/CommandLineTools/usr/lib/clang/12.0.5/include]
          add: [/Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/usr/include]
          add: [/Library/Developer/CommandLineTools/usr/include]
        end of search list found
        collapse include dir [/Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/usr/include/c++/v1] ==> [/Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/usr/include/c++/v1]
        collapse include dir [/Library/Developer/CommandLineTools/usr/lib/clang/12.0.5/include] ==> [/Library/Developer/CommandLineTools/usr/lib/clang/12.0.5/include]
        collapse include dir [/Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/usr/include] ==> [/Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/usr/include]
        collapse include dir [/Library/Developer/CommandLineTools/usr/include] ==> [/Library/Developer/CommandLineTools/usr/include]
        implicit include dirs: [/Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/usr/include/c++/v1;/Library/Developer/CommandLineTools/usr/lib/clang/12.0.5/include;/Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/usr/include;/Library/Developer/CommandLineTools/usr/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/Cellar/cmake/3.27.6/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:152 (message)"
      - "/usr/local/Cellar/cmake/3.27.6/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        ignore line: [Change Dir: '/Users/<USER>/DevEcoStudioProjects/ScreenDemo/bishare/src/main/cpp/test_build/CMakeFiles/CMakeScratch/TryCompile-WmTPog']
        ignore line: []
        ignore line: [Run Build Command(s): /usr/local/Cellar/cmake/3.27.6/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_be629/fast]
        ignore line: [/Library/Developer/CommandLineTools/usr/bin/make  -f CMakeFiles/cmTC_be629.dir/build.make CMakeFiles/cmTC_be629.dir/build]
        ignore line: [Building CXX object CMakeFiles/cmTC_be629.dir/CMakeCXXCompilerABI.cpp.o]
        ignore line: [/Library/Developer/CommandLineTools/usr/bin/c++   -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk   -v -Wl -v -MD -MT CMakeFiles/cmTC_be629.dir/CMakeCXXCompilerABI.cpp.o -MF CMakeFiles/cmTC_be629.dir/CMakeCXXCompilerABI.cpp.o.d -o CMakeFiles/cmTC_be629.dir/CMakeCXXCompilerABI.cpp.o -c /usr/local/Cellar/cmake/3.27.6/share/cmake/Modules/CMakeCXXCompilerABI.cpp]
        ignore line: [Apple clang version 12.0.5 (clang-1205.0.22.9)]
        ignore line: [Target: x86_64-apple-darwin22.6.0]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: /Library/Developer/CommandLineTools/usr/bin]
        ignore line: [clang: warning: -Wl -v: 'linker' input unused [-Wunused-command-line-argument]]
        ignore line: [ "/Library/Developer/CommandLineTools/usr/bin/clang" -cc1 -triple x86_64-apple-macosx11.3.0 -Wdeprecated-objc-isa-usage -Werror=deprecated-objc-isa-usage -Werror=implicit-function-declaration -emit-obj -mrelax-all -disable-free -disable-llvm-verifier -discard-value-names -main-file-name CMakeCXXCompilerABI.cpp -mrelocation-model pic -pic-level 2 -mframe-pointer=all -fno-strict-return -fno-rounding-math -munwind-tables -target-sdk-version=11.3 -fvisibility-inlines-hidden-static-local-var -target-cpu penryn -debugger-tuning=lldb -target-linker-version 650.9 -v -resource-dir /Library/Developer/CommandLineTools/usr/lib/clang/12.0.5 -dependency-file CMakeFiles/cmTC_be629.dir/CMakeCXXCompilerABI.cpp.o.d -skip-unused-modulemap-deps -MT CMakeFiles/cmTC_be629.dir/CMakeCXXCompilerABI.cpp.o -sys-header-deps -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk -stdlib=libc++ -internal-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/usr/include/c++/v1 -internal-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/usr/local/include -internal-isystem /Library/Developer/CommandLineTools/usr/lib/clang/12.0.5/include -internal-externc-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/usr/include -internal-externc-isystem /Library/Developer/CommandLineTools/usr/include -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -fdeprecated-macro -fdebug-compilation-dir /Users/<USER>/DevEcoStudioProjects/ScreenDemo/bishare/src/main/cpp/test_build/CMakeFiles/CMakeScratch/TryCompile-WmTPog -ferror-limit 19 -stack-protector 1 -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fcxx-exceptions -fexceptions -fmax-type-align=16 -fcommon -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation -fno-odr-hash-protocols -mllvm -disable-aligned-alloc-awareness=1 -o CMakeFiles/cmTC_be629.dir/CMakeCXXCompilerABI.cpp.o -x c++ /usr/local/Cellar/cmake/3.27.6/share/cmake/Modules/CMakeCXXCompilerABI.cpp]
        ignore line: [clang -cc1 version 12.0.5 (clang-1205.0.22.9) default target x86_64-apple-darwin22.6.0]
        ignore line: [ignoring nonexistent directory "/Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/usr/local/include"]
        ignore line: [ignoring nonexistent directory "/Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/Library/Frameworks"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ /Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/usr/include/c++/v1]
        ignore line: [ /Library/Developer/CommandLineTools/usr/lib/clang/12.0.5/include]
        ignore line: [ /Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/usr/include]
        ignore line: [ /Library/Developer/CommandLineTools/usr/include]
        ignore line: [ /Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/System/Library/Frameworks (framework directory)]
        ignore line: [End of search list.]
        ignore line: [Linking CXX executable cmTC_be629]
        ignore line: [/usr/local/Cellar/cmake/3.27.6/bin/cmake -E cmake_link_script CMakeFiles/cmTC_be629.dir/link.txt --verbose=1]
        ignore line: [/Library/Developer/CommandLineTools/usr/bin/c++  -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk -Wl -search_paths_first -Wl -headerpad_max_install_names  -v -Wl -v CMakeFiles/cmTC_be629.dir/CMakeCXXCompilerABI.cpp.o -o cmTC_be629 ]
        ignore line: [Apple clang version 12.0.5 (clang-1205.0.22.9)]
        ignore line: [Target: x86_64-apple-darwin22.6.0]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: /Library/Developer/CommandLineTools/usr/bin]
        link line: [ "/Library/Developer/CommandLineTools/usr/bin/ld" -demangle -lto_library /Library/Developer/CommandLineTools/usr/lib/libLTO.dylib -dynamic -arch x86_64 -platform_version macos 11.3.0 11.3 -syslibroot /Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk -o cmTC_be629 -search_paths_first -headerpad_max_install_names -v CMakeFiles/cmTC_be629.dir/CMakeCXXCompilerABI.cpp.o -lc++ -lSystem /Library/Developer/CommandLineTools/usr/lib/clang/12.0.5/lib/darwin/libclang_rt.osx.a]
          arg [/Library/Developer/CommandLineTools/usr/bin/ld] ==> ignore
          arg [-demangle] ==> ignore
          arg [-lto_library] ==> ignore, skip following value
          arg [/Library/Developer/CommandLineTools/usr/lib/libLTO.dylib] ==> skip value of -lto_library
          arg [-dynamic] ==> ignore
          arg [-arch] ==> ignore
          arg [x86_64] ==> ignore
          arg [-platform_version] ==> ignore
          arg [macos] ==> ignore
          arg [11.3.0] ==> ignore
          arg [11.3] ==> ignore
          arg [-syslibroot] ==> ignore
          arg [/Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk] ==> ignore
          arg [-o] ==> ignore
          arg [cmTC_be629] ==> ignore
          arg [-search_paths_first] ==> ignore
          arg [-headerpad_max_install_names] ==> ignore
          arg [-v] ==> ignore
          arg [CMakeFiles/cmTC_be629.dir/CMakeCXXCompilerABI.cpp.o] ==> ignore
          arg [-lc++] ==> lib [c++]
          arg [-lSystem] ==> lib [System]
          arg [/Library/Developer/CommandLineTools/usr/lib/clang/12.0.5/lib/darwin/libclang_rt.osx.a] ==> lib [/Library/Developer/CommandLineTools/usr/lib/clang/12.0.5/lib/darwin/libclang_rt.osx.a]
        Library search paths: [;/Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/usr/lib]
        Framework search paths: [;/Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/System/Library/Frameworks/]
        remove lib [System]
        remove lib [/Library/Developer/CommandLineTools/usr/lib/clang/12.0.5/lib/darwin/libclang_rt.osx.a]
        collapse library dir [/Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/usr/lib] ==> [/Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/usr/lib]
        collapse framework dir [/Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/System/Library/Frameworks/] ==> [/Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/System/Library/Frameworks]
        implicit libs: [c++]
        implicit objs: []
        implicit dirs: [/Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/usr/lib]
        implicit fwks: [/Library/Developer/CommandLineTools/SDKs/MacOSX11.3.sdk/System/Library/Frameworks]
      
      
...
