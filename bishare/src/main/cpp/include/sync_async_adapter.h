#ifndef SYNC_ASYNC_ADAPTER_H
#define SYNC_ASYNC_ADAPTER_H

#include <napi/native_api.h>
#include <functional>
#include <string>
#include "async_executor.h"

namespace OHOS {
    namespace BiShare {
        namespace Infrastructure {

            /**
             * 同步/异步适配器
             * 
             * 功能：
             * 1. 包装现有同步方法为异步执行
             * 2. 自动检测调用模式（同步/异步）
             * 3. 保持现有代码接口不变
             * 4. 提供统一的错误处理
             */
            class SyncAsyncAdapter {
            public:
                // 同步执行函数类型
                using SyncFunction = std::function<napi_value(napi_env, napi_callback_info)>;
                
                // 异步执行函数类型（后台线程）
                using AsyncExecuteFunction = std::function<void(AsyncWorkData*)>;
                
                // 异步完成函数类型（主线程）
                using AsyncCompleteFunction = std::function<napi_value(napi_env, AsyncWorkData*)>;

                /**
                 * 智能执行模式 - 自动检测同步/异步
                 * 
                 * @param env NAPI环境
                 * @param info 回调信息
                 * @param resourceName 资源名称
                 * @param syncFunc 同步执行函数
                 * @param asyncExecuteFunc 异步执行函数（可选）
                 * @param asyncCompleteFunc 异步完成函数（可选）
                 * @return 执行结果
                 */
                static napi_value SmartExecute(
                    napi_env env, 
                    napi_callback_info info,
                    const std::string& resourceName,
                    SyncFunction syncFunc,
                    AsyncExecuteFunction asyncExecuteFunc = nullptr,
                    AsyncCompleteFunction asyncCompleteFunc = nullptr
                );

                /**
                 * 强制同步执行
                 */
                static napi_value ExecuteSync(
                    napi_env env, 
                    napi_callback_info info,
                    SyncFunction syncFunc
                );

                /**
                 * 强制异步执行
                 */
                static napi_value ExecuteAsync(
                    napi_env env, 
                    napi_callback_info info,
                    const std::string& resourceName,
                    AsyncExecuteFunction asyncExecuteFunc,
                    AsyncCompleteFunction asyncCompleteFunc
                );

                /**
                 * 包装现有同步方法为异步执行
                 * 
                 * 这个方法将现有的同步函数包装成异步执行，
                 * 在后台线程中调用同步函数，避免阻塞主线程
                 */
                static napi_value WrapSyncAsAsync(
                    napi_env env, 
                    napi_callback_info info,
                    const std::string& resourceName,
                    SyncFunction syncFunc
                );

            private:
                /**
                 * 检查是否有回调参数（异步模式）
                 */
                static bool HasCallbackParameter(napi_env env, napi_callback_info info);

                /**
                 * 创建异步执行函数（包装同步函数）
                 */
                static AsyncExecuteFunction CreateAsyncExecuteWrapper(SyncFunction syncFunc);

                /**
                 * 创建异步完成函数（处理同步函数结果）
                 */
                static AsyncCompleteFunction CreateAsyncCompleteWrapper();
            };

            /**
             * 便捷宏定义
             */
            #define SMART_EXECUTE(resourceName, syncFunc) \
                SyncAsyncAdapter::SmartExecute(env, info, resourceName, syncFunc)

            #define SMART_EXECUTE_WITH_ASYNC(resourceName, syncFunc, asyncExecuteFunc, asyncCompleteFunc) \
                SyncAsyncAdapter::SmartExecute(env, info, resourceName, syncFunc, asyncExecuteFunc, asyncCompleteFunc)

            #define WRAP_SYNC_AS_ASYNC(resourceName, syncFunc) \
                SyncAsyncAdapter::WrapSyncAsAsync(env, info, resourceName, syncFunc)

        } // namespace Infrastructure
    } // namespace BiShare
} // namespace OHOS

#endif // SYNC_ASYNC_ADAPTER_H
