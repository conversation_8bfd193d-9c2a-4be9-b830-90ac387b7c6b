#ifndef BISHARE_OPERATIONS_H
#define BISHARE_OPERATIONS_H

#include <napi/native_api.h>
#include <string>
#include <memory>
#include <functional>
#include <map>

#include "bishare-define.h"

namespace OHOS {
    namespace BiShare {

        // 前向声明
        class BiShareNapi;

        // 异步工作数据结构
        struct AsyncWorkData {
            // 基本信息
            std::string workName;
            napi_env env;
            napi_async_work work;
            napi_deferred deferred;
            napi_ref callbackRef;
            
            // 结果信息
            bstatus_t result;
            std::string errorMessage;
            std::string successMessage;
            
            // 参数数据联合体
            union {
                struct {
                    bool boolParam1;
                    bool boolParam2;
                    int intParam1;
                    int intParam2;
                    int intParam3;
                    int intParam4;
                    long longParam1;
                    long longParam2;
                    long longParam3;
                    long longParam4;
                    int priority;
                    std::string stringParam1;
                    std::string stringParam2;
                    std::string stringParam3;
                } data;
            };
            
            AsyncWorkData() : result(BS_OK) {
                memset(&data, 0, sizeof(data));
            }
        };

        // 抽象操作基类 - 使用模板方法模式
        class BiShareAsyncOperation {
        public:
            BiShareAsyncOperation(const std::string& operationName) 
                : operationName_(operationName) {}
            
            virtual ~BiShareAsyncOperation() = default;

            // 模板方法 - 定义操作执行流程
            napi_value Execute(napi_env env, napi_callback_info info);

        protected:
            // 纯虚函数 - 子类必须实现
            virtual bool ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) = 0;
            virtual void ExecuteOperation(napi_env env, AsyncWorkData* workData) = 0;
            virtual napi_value CreateResult(napi_env env, AsyncWorkData* workData) = 0;

            // 可重写的钩子方法
            virtual bool ValidateArguments(AsyncWorkData* workData) { return true; }
            virtual void OnOperationComplete(AsyncWorkData* workData) {}

            // 辅助方法
            napi_value CreateAsyncWork(napi_env env, AsyncWorkData* workData,
                                     napi_async_execute_callback execute,
                                     napi_async_complete_callback complete);

        private:
            std::string operationName_;
            
            // 静态回调函数
            static void ExecuteCallback(napi_env env, void* data);
            static void CompleteCallback(napi_env env, napi_status status, void* data);
        };

        // 服务管理操作基类
        class BiShareServiceOperation : public BiShareAsyncOperation {
        public:
            BiShareServiceOperation(const std::string& operationName) 
                : BiShareAsyncOperation(operationName) {}

        protected:
            // 通用的服务状态检查
            bool CheckServiceInitialized(AsyncWorkData* workData);
            napi_value CreateStandardResult(napi_env env, AsyncWorkData* workData);
        };

        // 设备管理操作基类
        class BiShareDeviceOperation : public BiShareServiceOperation {
        public:
            BiShareDeviceOperation(const std::string& operationName) 
                : BiShareServiceOperation(operationName) {}

        protected:
            std::shared_ptr<class BiShareDeviceManager> GetDeviceManager();
        };

        // 录制管理操作基类
        class BiShareRecordOperation : public BiShareServiceOperation {
        public:
            BiShareRecordOperation(const std::string& operationName) 
                : BiShareServiceOperation(operationName) {}

        protected:
            std::shared_ptr<class BiShareRecordManager> GetRecordManager();
        };

        // 操作工厂类 - 使用工厂模式
        class BiShareOperationFactory {
        public:
            // 操作类型枚举
            enum class OperationType {
                INITIALIZE,
                RELEASE,
                DISCOVER_DEVICES,
                GET_DISCOVERED_DEVICES,
                SET_DEVICE_MODEL,
                GET_DEVICE_MODEL,
                RESET_DEVICE_MODEL,
                SET_DEVICE_INFO,
                FIND_REMOTE_DEVICE,
                START_CAPTURE,
                SET_SIZE,
                SCREENSHOT,
                START_SCREEN_RECORD,
                STOP_SCREEN_RECORD,
                SET_DEFAULT_AUDIO_OUTPUT_DEVICE,
                GET_ROOT_PATH,
                GET_CURRENT_DIRECTOR,
                ON_EVENT
            };

            // 创建操作实例
            static std::unique_ptr<BiShareAsyncOperation> CreateOperation(OperationType type);
            
            // 注册自定义操作创建器
            using OperationCreator = std::function<std::unique_ptr<BiShareAsyncOperation>()>;
            static void RegisterOperationCreator(OperationType type, OperationCreator creator);

        private:
            static std::map<OperationType, OperationCreator> creators_;
        };

        // 操作注册宏 - 简化操作类的注册
        #define REGISTER_OPERATION(type, className) \
            BiShareOperationFactory::RegisterOperationCreator( \
                BiShareOperationFactory::OperationType::type, \
                []() -> std::unique_ptr<BiShareAsyncOperation> { \
                    return std::make_unique<className>(); \
                })

    } // namespace BiShare
} // namespace OHOS

#endif // BISHARE_OPERATIONS_H
