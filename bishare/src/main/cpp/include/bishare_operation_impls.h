#ifndef BISHARE_OPERATION_IMPLS_H
#define BISHARE_OPERATION_IMPLS_H

#include "bishare_operations.h"

namespace OHOS {
    namespace BiShare {

        // 服务初始化操作
        class InitializeOperation : public BiShareServiceOperation {
        public:
            InitializeOperation() : BiShareServiceOperation("Initialize") {}

        protected:
            bool ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) override;
            void ExecuteOperation(napi_env env, AsyncWorkData* workData) override;
            napi_value CreateResult(napi_env env, AsyncWorkData* workData) override;
        };

        // 服务释放操作
        class ReleaseOperation : public BiShareServiceOperation {
        public:
            ReleaseOperation() : BiShareServiceOperation("Release") {}

        protected:
            bool ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) override;
            void ExecuteOperation(napi_env env, AsyncWorkData* workData) override;
            napi_value CreateResult(napi_env env, AsyncWorkData* workData) override;
        };

        // 设备发现操作
        class DiscoverDevicesOperation : public BiShareDeviceOperation {
        public:
            DiscoverDevicesOperation() : BiShareDeviceOperation("DiscoverDevices") {}

        protected:
            bool ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) override;
            void ExecuteOperation(napi_env env, AsyncWorkData* workData) override;
            napi_value CreateResult(napi_env env, AsyncWorkData* workData) override;
        };

        // 获取已发现设备操作
        class GetDiscoveredDevicesOperation : public BiShareDeviceOperation {
        public:
            GetDiscoveredDevicesOperation() : BiShareDeviceOperation("GetDiscoveredDevices") {}

        protected:
            bool ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) override;
            void ExecuteOperation(napi_env env, AsyncWorkData* workData) override;
            napi_value CreateResult(napi_env env, AsyncWorkData* workData) override;
        };

        // 设置设备模型操作
        class SetDeviceModelOperation : public BiShareDeviceOperation {
        public:
            SetDeviceModelOperation() : BiShareDeviceOperation("SetDeviceModel") {}

        protected:
            bool ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) override;
            void ExecuteOperation(napi_env env, AsyncWorkData* workData) override;
            napi_value CreateResult(napi_env env, AsyncWorkData* workData) override;
        };

        // 获取设备模型操作
        class GetDeviceModelOperation : public BiShareDeviceOperation {
        public:
            GetDeviceModelOperation() : BiShareDeviceOperation("GetDeviceModel") {}

        protected:
            bool ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) override;
            void ExecuteOperation(napi_env env, AsyncWorkData* workData) override;
            napi_value CreateResult(napi_env env, AsyncWorkData* workData) override;
        };

        // 重置设备模型操作
        class ResetDeviceModelOperation : public BiShareDeviceOperation {
        public:
            ResetDeviceModelOperation() : BiShareDeviceOperation("ResetDeviceModel") {}

        protected:
            bool ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) override;
            void ExecuteOperation(napi_env env, AsyncWorkData* workData) override;
            napi_value CreateResult(napi_env env, AsyncWorkData* workData) override;
        };

        // 设置设备信息操作
        class SetDeviceInfoOperation : public BiShareDeviceOperation {
        public:
            SetDeviceInfoOperation() : BiShareDeviceOperation("SetDeviceInfo") {}

        protected:
            bool ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) override;
            void ExecuteOperation(napi_env env, AsyncWorkData* workData) override;
            napi_value CreateResult(napi_env env, AsyncWorkData* workData) override;
        };

        // 查找远程设备操作
        class FindRemoteDeviceOperation : public BiShareDeviceOperation {
        public:
            FindRemoteDeviceOperation() : BiShareDeviceOperation("FindRemoteDevice") {}

        protected:
            bool ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) override;
            void ExecuteOperation(napi_env env, AsyncWorkData* workData) override;
            napi_value CreateResult(napi_env env, AsyncWorkData* workData) override;
        };

        // 开始捕获操作
        class StartCaptureOperation : public BiShareRecordOperation {
        public:
            StartCaptureOperation() : BiShareRecordOperation("StartCapture") {}

        protected:
            bool ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) override;
            void ExecuteOperation(napi_env env, AsyncWorkData* workData) override;
            napi_value CreateResult(napi_env env, AsyncWorkData* workData) override;
        };

        // 设置尺寸操作
        class SetSizeOperation : public BiShareRecordOperation {
        public:
            SetSizeOperation() : BiShareRecordOperation("SetSize") {}

        protected:
            bool ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) override;
            void ExecuteOperation(napi_env env, AsyncWorkData* workData) override;
            napi_value CreateResult(napi_env env, AsyncWorkData* workData) override;
        };

        // 截图操作
        class ScreenshotOperation : public BiShareRecordOperation {
        public:
            ScreenshotOperation() : BiShareRecordOperation("Screenshot") {}

        protected:
            bool ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) override;
            void ExecuteOperation(napi_env env, AsyncWorkData* workData) override;
            napi_value CreateResult(napi_env env, AsyncWorkData* workData) override;
        };

        // 开始屏幕录制操作
        class StartScreenRecordOperation : public BiShareRecordOperation {
        public:
            StartScreenRecordOperation() : BiShareRecordOperation("StartScreenRecord") {}

        protected:
            bool ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) override;
            void ExecuteOperation(napi_env env, AsyncWorkData* workData) override;
            napi_value CreateResult(napi_env env, AsyncWorkData* workData) override;
        };

        // 停止屏幕录制操作
        class StopScreenRecordOperation : public BiShareRecordOperation {
        public:
            StopScreenRecordOperation() : BiShareRecordOperation("StopScreenRecord") {}

        protected:
            bool ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) override;
            void ExecuteOperation(napi_env env, AsyncWorkData* workData) override;
            napi_value CreateResult(napi_env env, AsyncWorkData* workData) override;
        };

        // 设置默认音频输出设备操作
        class SetDefaultAudioOutputDeviceOperation : public BiShareRecordOperation {
        public:
            SetDefaultAudioOutputDeviceOperation() : BiShareRecordOperation("SetDefaultAudioOutputDevice") {}

        protected:
            bool ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) override;
            void ExecuteOperation(napi_env env, AsyncWorkData* workData) override;
            napi_value CreateResult(napi_env env, AsyncWorkData* workData) override;
        };

        // 获取根路径操作 - 同步操作
        class GetRootPathOperation : public BiShareDeviceOperation {
        public:
            GetRootPathOperation() : BiShareDeviceOperation("GetRootPath") {}

            // 重写Execute方法为同步执行
            napi_value Execute(napi_env env, napi_callback_info info) override;

        protected:
            bool ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) override;
            void ExecuteOperation(napi_env env, AsyncWorkData* workData) override;
            napi_value CreateResult(napi_env env, AsyncWorkData* workData) override;
        };

        // 获取当前目录操作 - 同步操作
        class GetCurrentDirectorOperation : public BiShareDeviceOperation {
        public:
            GetCurrentDirectorOperation() : BiShareDeviceOperation("GetCurrentDirector") {}

            // 重写Execute方法为同步执行
            napi_value Execute(napi_env env, napi_callback_info info) override;

        protected:
            bool ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) override;
            void ExecuteOperation(napi_env env, AsyncWorkData* workData) override;
            napi_value CreateResult(napi_env env, AsyncWorkData* workData) override;
        };

        // 清除已发现设备操作
        class ClearDiscoveredDevicesOperation : public BiShareDeviceOperation {
        public:
            ClearDiscoveredDevicesOperation() : BiShareDeviceOperation("ClearDiscoveredDevices") {}

        protected:
            bool ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) override;
            void ExecuteOperation(napi_env env, AsyncWorkData* workData) override;
            napi_value CreateResult(napi_env env, AsyncWorkData* workData) override;
        };

        // 设置网络信息操作
        class SetNetworkInfoOperation : public BiShareServiceOperation {
        public:
            SetNetworkInfoOperation() : BiShareServiceOperation("SetNetworkInfo") {}

        protected:
            bool ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) override;
            void ExecuteOperation(napi_env env, AsyncWorkData* workData) override;
            napi_value CreateResult(napi_env env, AsyncWorkData* workData) override;
        };

        // 获取当前目录操作
        class GetCurrentDirectoryOperation : public BiShareDeviceOperation {
        public:
            GetCurrentDirectoryOperation() : BiShareDeviceOperation("GetCurrentDirectory") {}

            // 重写Execute方法为同步执行
            napi_value Execute(napi_env env, napi_callback_info info) override;

        protected:
            bool ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) override;
            void ExecuteOperation(napi_env env, AsyncWorkData* workData) override;
            napi_value CreateResult(napi_env env, AsyncWorkData* workData) override;
        };

        // 事件监听操作 - 特殊操作
        class OnEventOperation : public BiShareServiceOperation {
        public:
            OnEventOperation() : BiShareServiceOperation("OnEvent") {}

            // 重写Execute方法为同步执行
            napi_value Execute(napi_env env, napi_callback_info info) override;

        protected:
            bool ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) override;
            void ExecuteOperation(napi_env env, AsyncWorkData* workData) override;
            napi_value CreateResult(napi_env env, AsyncWorkData* workData) override;
        };

        // 取消事件监听操作
        class OffEventOperation : public BiShareServiceOperation {
        public:
            OffEventOperation() : BiShareServiceOperation("OffEvent") {}

            // 重写Execute方法为同步执行
            napi_value Execute(napi_env env, napi_callback_info info) override;

        protected:
            bool ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) override;
            void ExecuteOperation(napi_env env, AsyncWorkData* workData) override;
            napi_value CreateResult(napi_env env, AsyncWorkData* workData) override;
        };

        // 一次性事件监听操作
        class OnceEventOperation : public BiShareServiceOperation {
        public:
            OnceEventOperation() : BiShareServiceOperation("OnceEvent") {}

            // 重写Execute方法为同步执行
            napi_value Execute(napi_env env, napi_callback_info info) override;

        protected:
            bool ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) override;
            void ExecuteOperation(napi_env env, AsyncWorkData* workData) override;
            napi_value CreateResult(napi_env env, AsyncWorkData* workData) override;
        };

    } // namespace BiShare
} // namespace OHOS

#endif // BISHARE_OPERATION_IMPLS_H
