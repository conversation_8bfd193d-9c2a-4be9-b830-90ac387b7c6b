#ifndef OPERATION_FACTORY_H
#define OPERATION_FACTORY_H

#include <napi/native_api.h>
#include <memory>
#include <string>
#include <functional>

#include "bishare-define.h"

namespace OHOS {
    namespace BiShare {
        namespace Core {

            // 前向声明
            class ServiceManager;
            class CallbackManager;

            /**
             * 操作类型枚举
             */
            enum class OperationType {
                // 服务管理操作
                INIT_SERVICE,
                RELEASE_SERVICE,

                // 设备操作
                SET_DEVICE_INFO,
                SET_DEVICE_MODEL,
                GET_DEVICE_MODEL,
                RESET_DEVICE_MODEL,
                START_DISCOVERY,
                STOP_DISCOVERY,
                CLEAR_DISCOVERED_DEVICES,
                GET_DISCOVERED_DEVICES,
                FIND_REMOTE_DEVICE,

                // 录制操作
                START_SCREEN_RECORD,
                STOP_SCREEN_RECORD,
                START_CAPTURE,
                SET_SIZE,
                SET_DEFAULT_AUDIO_OUTPUT_DEVICE,
                SCREENSHOT,

                // 网络操作
                SET_NETWORK_INFO,
                GET_ROOT_PATH,
                GET_CURRENT_DIRECTORY,

                // 事件操作
                ON_EVENT,
                OFF_EVENT,
                ONCE_EVENT
            };

            /**
             * 操作参数结构
             */
            struct OperationParams {
                // 字符串参数
                std::string stringParam1;
                std::string stringParam2;
                std::string stringParam3;

                // 整数参数
                int32_t intParam1 = 0;
                int32_t intParam2 = 0;
                int32_t intParam3 = 0;
                int32_t intParam4 = 0;

                // 长整数参数
                int64_t longParam1 = 0;
                int64_t longParam2 = 0;
                int64_t longParam3 = 0;
                int64_t longParam4 = 0;

                // 布尔参数
                bool boolParam1 = false;
                bool boolParam2 = false;

                // 枚举参数
                network_type_t networkType = NotNetwork;
                log_priority_t logPriority = LOG_INFO;

                // 回调参数
                napi_ref callback = nullptr;
            };

            /**
             * 操作结果结构
             */
            struct OperationResult {
                bstatus_t status = BS_OK;
                std::string errorMessage;
                std::string stringResult;
                int32_t intResult = 0;
                bool boolResult = false;
            };

            /**
             * 操作接口
             */
            class IOperation {
            public:
                virtual ~IOperation() = default;

                /**
                 * 执行操作
                 */
                virtual OperationResult Execute(const OperationParams& params) = 0;

                /**
                 * 获取操作类型
                 */
                virtual OperationType GetType() const = 0;

                /**
                 * 获取操作名称
                 */
                virtual std::string GetName() const = 0;
            };

            /**
             * 操作工厂 - 创建和管理操作对象
             * 
             * 职责：
             * 1. 根据操作类型创建相应的操作对象
             * 2. 管理操作对象的生命周期
             * 3. 提供操作的统一执行接口
             * 4. 处理操作的参数解析和结果封装
             */
            class OperationFactory {
            public:
                OperationFactory(std::shared_ptr<ServiceManager> serviceManager,
                               std::shared_ptr<CallbackManager> callbackManager);
                ~OperationFactory();

                /**
                 * 创建操作对象
                 */
                std::unique_ptr<IOperation> CreateOperation(OperationType type);

                /**
                 * 执行操作
                 */
                OperationResult ExecuteOperation(OperationType type, const OperationParams& params);

                /**
                 * 从NAPI参数解析操作参数
                 */
                OperationParams ParseOperationParams(napi_env env, napi_callback_info info, OperationType type);

                /**
                 * 将操作结果转换为NAPI值
                 */
                napi_value ConvertResultToNapi(napi_env env, const OperationResult& result, OperationType type);

                /**
                 * 获取操作类型名称
                 */
                static std::string GetOperationTypeName(OperationType type);

            private:
                std::shared_ptr<ServiceManager> serviceManager_;
                std::shared_ptr<CallbackManager> callbackManager_;

                // 内部辅助方法
                OperationParams ParseServiceOperationParams(napi_env env, napi_callback_info info, OperationType type);
                OperationParams ParseDeviceOperationParams(napi_env env, napi_callback_info info, OperationType type);
                OperationParams ParseRecordingOperationParams(napi_env env, napi_callback_info info, OperationType type);
                OperationParams ParseNetworkOperationParams(napi_env env, napi_callback_info info, OperationType type);
                OperationParams ParseEventOperationParams(napi_env env, napi_callback_info info, OperationType type);
            };

        } // namespace Core
    } // namespace BiShare
} // namespace OHOS

#endif // OPERATION_FACTORY_H
