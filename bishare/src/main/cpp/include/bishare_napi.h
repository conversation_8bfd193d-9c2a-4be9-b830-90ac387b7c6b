#ifndef BISHARE_NAPI_H
#define BISHARE_NAPI_H

#include <napi/native_api.h>
#include <pthread.h>
#include <string>
#include <vector>
#include <map>
#include <atomic>
#include <memory>

#include "bishare-define.h"
#include "bishare-service.h"

namespace OHOS {
    namespace BiShare {

        // Forward declarations
        class BiShareCallbacks;
        class BiShareDeviceManager;
        class BiShareRecordManager;
        class BiShareOperationFactory;
        class BiShareAsyncOperation;

        // Main NAPI class for binding - 重构为轻量级的门面类
        class BiShareNapi {
        public:
            /**
             * Initialize NAPI module
             */
            static napi_value Init(napi_env env, napi_value exports);

            /**
             * Constructor
             */
            BiShareNapi();

            /**
             * Destructor
             */
            ~BiShareNapi();

            // Constants for property names
            static constexpr const char *PROPERTY_VERSION = "VERSION";
            static constexpr const char *PROPERTY_LOG_PRIORITY = "LOG_PRIORITY";

            // 获取单例实例
            static BiShareNapi* GetInstance();

            // 获取管理器实例
            std::shared_ptr<BiShareCallbacks> GetCallbacks() const { return callbacks_; }
            std::shared_ptr<BiShareDeviceManager> GetDeviceManager() const { return deviceManager_; }
            std::shared_ptr<BiShareRecordManager> GetRecordManager() const { return recordManager_; }

            // 检查初始化状态
            static bool IsInitialized() { return isInitialized_.load(); }
            static void SetInitialized(bool initialized) { isInitialized_.store(initialized); }

        private:
            // Singleton instance
            static std::unique_ptr<BiShareNapi> instance_;
            static std::atomic<bool> isInitialized_;

            // Managers for different features
            std::shared_ptr<BiShareCallbacks> callbacks_;
            std::shared_ptr<BiShareDeviceManager> deviceManager_;
            std::shared_ptr<BiShareRecordManager> recordManager_;
            std::shared_ptr<BiShareOperationFactory> operationFactory_;

            // Event reference tracking for cleanup
            struct EventInfo {
                napi_env env;
                napi_ref callbackRef;
            };
            std::map<int, std::vector<EventInfo>> eventCallbacks_;
            pthread_mutex_t eventCallbacksMutex_;

            // Static methods for NAPI binding
            static napi_value CreateBiShareInstance(napi_env env, napi_callback_info info);
            static napi_value Destructor(napi_env env, void *nativeObject);

            // API methods
            static napi_value Initialize(napi_env env, napi_callback_info info);
            static napi_value Release(napi_env env, napi_callback_info info);
            static napi_value DiscoverDevices(napi_env env, napi_callback_info info);
            static napi_value ClearDiscoveredDevices(napi_env env, napi_callback_info info);
            static napi_value GetDiscoveredDevices(napi_env env, napi_callback_info info);
            static napi_value StartScreenRecord(napi_env env, napi_callback_info info);
            static napi_value StopScreenRecord(napi_env env, napi_callback_info info);
            static napi_value StartCapture(napi_env env, napi_callback_info info);
            static napi_value SetDeviceInfo(napi_env env, napi_callback_info info);
            static napi_value SetDeviceModel(napi_env env, napi_callback_info info);
            static napi_value GetDeviceModel(napi_env env, napi_callback_info info);
            static napi_value ResetDeviceModel(napi_env env, napi_callback_info info);
            static napi_value GetRootPath(napi_env env, napi_callback_info info);
            static napi_value GetCurrentDirector(napi_env env, napi_callback_info info);
            static napi_value SetSize(napi_env env, napi_callback_info info);
            static napi_value SetDefaultAudioOutputDevice(napi_env env, napi_callback_info info);
            static napi_value Screenshot(napi_env env, napi_callback_info info);
            static napi_value FindRemoteDevice(napi_env env, napi_callback_info info);
            static napi_value SetNetworkInfo(napi_env env, napi_callback_info info);

            // Event management
            static napi_value On(napi_env env, napi_callback_info info);
            static napi_value Off(napi_env env, napi_callback_info info);
            static napi_value Once(napi_env env, napi_callback_info info);

            // Helper methods
            static void ExecuteAsyncWork(napi_env env, napi_callback_info info, const std::string &workName,
                                         napi_async_execute_callback execute, napi_async_complete_callback complete,
                                         void *data);
            static void CreatePromise(napi_env env, napi_callback_info info, const std::string &workName,
                                      napi_async_execute_callback execute, napi_async_complete_callback complete,
                                      void *data, napi_deferred *deferred);

            // Async methods
            static napi_value PromiseOrCallback(napi_env env, napi_callback_info info, const std::string &workName,
                                                napi_async_execute_callback execute,
                                                napi_async_complete_callback complete, void *data);

            // Friend classes
            friend class BiShareCallbacks;
            friend class BiShareDeviceManager;
            friend class BiShareRecordManager;
        };

    } // namespace BiShare
} // namespace OHOS

#endif // BISHARE_NAPI_H