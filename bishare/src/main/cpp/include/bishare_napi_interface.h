#ifndef BISHARE_NAPI_INTERFACE_H
#define BISHARE_NAPI_INTERFACE_H

#include <napi/native_api.h>
#include <memory>
#include <functional>
#include <string>

namespace OHOS {
    namespace BiShare {
        namespace Interfaces {

            // 前向声明
            namespace Core {
                class BiShareFacade;
            }

            /**
             * BiShare NAPI接口 - 提供JavaScript绑定
             * 
             * 职责：
             * 1. 提供JavaScript可调用的API接口
             * 2. 处理NAPI参数解析和结果转换
             * 3. 管理异步操作的生命周期
             * 4. 协调门面类和JavaScript层的交互
             */
            class BiShareNapiInterface {
            public:
                /**
                 * 模块初始化
                 */
                static napi_value Init(napi_env env, napi_value exports);

                // 服务管理方法
                static napi_value InitService(napi_env env, napi_callback_info info);
                static napi_value ReleaseService(napi_env env, napi_callback_info info);

                // 设备管理方法
                static napi_value SetDeviceInfo(napi_env env, napi_callback_info info);
                static napi_value SetDeviceModel(napi_env env, napi_callback_info info);
                static napi_value GetDeviceModel(napi_env env, napi_callback_info info);
                static napi_value ResetDeviceModel(napi_env env, napi_callback_info info);
                static napi_value StartDiscovery(napi_env env, napi_callback_info info);
                static napi_value StopDiscovery(napi_env env, napi_callback_info info);
                static napi_value ClearDiscoveredDevices(napi_env env, napi_callback_info info);
                static napi_value GetDiscoveredDevices(napi_env env, napi_callback_info info);
                static napi_value FindRemoteDevice(napi_env env, napi_callback_info info);

                // 录制管理方法
                static napi_value StartScreenRecord(napi_env env, napi_callback_info info);
                static napi_value StopScreenRecord(napi_env env, napi_callback_info info);
                static napi_value StartCapture(napi_env env, napi_callback_info info);
                static napi_value SetSize(napi_env env, napi_callback_info info);
                static napi_value SetDefaultAudioOutputDevice(napi_env env, napi_callback_info info);
                static napi_value Screenshot(napi_env env, napi_callback_info info);

                // 网络管理方法
                static napi_value SetNetworkInfo(napi_env env, napi_callback_info info);
                static napi_value GetRootPath(napi_env env, napi_callback_info info);
                static napi_value GetCurrentDirectory(napi_env env, napi_callback_info info);

                // 事件管理方法
                static napi_value On(napi_env env, napi_callback_info info);
                static napi_value Off(napi_env env, napi_callback_info info);
                static napi_value Once(napi_env env, napi_callback_info info);

            private:
                /**
                 * 获取门面实例
                 */
                static Core::BiShareFacade& GetFacade();

                /**
                 * 创建异步工作
                 */
                static napi_value CreateAsyncWork(napi_env env, napi_callback_info info, 
                                                const char* resourceName,
                                                std::function<void(napi_env, void*)> executeCallback,
                                                std::function<void(napi_env, napi_status, void*)> completeCallback);

                /**
                 * 解析回调参数
                 */
                static napi_ref ParseCallback(napi_env env, napi_value callback);

                /**
                 * 创建错误对象
                 */
                static napi_value CreateError(napi_env env, const std::string& message);

                /**
                 * 创建成功结果
                 */
                static napi_value CreateSuccessResult(napi_env env, napi_value data = nullptr);

                /**
                 * 验证参数数量
                 */
                static bool ValidateArgCount(napi_env env, size_t argc, size_t expected);

                /**
                 * 获取字符串参数
                 */
                static std::string GetStringArg(napi_env env, napi_value arg);

                /**
                 * 获取整数参数
                 */
                static int32_t GetInt32Arg(napi_env env, napi_value arg);

                /**
                 * 获取布尔参数
                 */
                static bool GetBoolArg(napi_env env, napi_value arg);

                /**
                 * 创建字符串值
                 */
                static napi_value CreateStringValue(napi_env env, const std::string& str);

                /**
                 * 创建整数值
                 */
                static napi_value CreateInt32Value(napi_env env, int32_t value);
            };

        } // namespace Interfaces
    } // namespace BiShare
} // namespace OHOS

#endif // BISHARE_NAPI_INTERFACE_H
