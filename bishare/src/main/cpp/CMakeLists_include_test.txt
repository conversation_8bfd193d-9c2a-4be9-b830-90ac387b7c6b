cmake_minimum_required(VERSION 3.5)

project(BiShareIncludeTest)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 包含目录 - 只需要include目录和第三方库
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/biservice/include
)

# 编译头文件结构测试程序
add_executable(include_test 
    test_include_structure.cpp
    infrastructure/bishare_status_codes.cpp
)

# 设置编译选项
target_compile_options(include_test PRIVATE
    -Wall
    -Wextra
    -std=c++17
)

# 如果是Debug模式，添加调试信息
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(include_test PRIVATE -g -O0)
else()
    target_compile_options(include_test PRIVATE -O2)
endif()

# 打印一些信息
message(STATUS "构建 BiShare 头文件结构测试")
message(STATUS "C++ 标准: ${CMAKE_CXX_STANDARD}")
message(STATUS "构建类型: ${CMAKE_BUILD_TYPE}")
message(STATUS "编译器: ${CMAKE_CXX_COMPILER_ID}")
message(STATUS "包含目录: ${CMAKE_CURRENT_SOURCE_DIR}/include")
