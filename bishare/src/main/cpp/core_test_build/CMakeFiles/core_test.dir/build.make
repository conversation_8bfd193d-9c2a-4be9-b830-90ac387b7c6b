# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/local/Cellar/cmake/3.27.6/bin/cmake

# The command to remove a file.
RM = /usr/local/Cellar/cmake/3.27.6/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/DevEcoStudioProjects/ScreenDemo/bishare/src/main/cpp/core_test_build

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/DevEcoStudioProjects/ScreenDemo/bishare/src/main/cpp/core_test_build

# Include any dependencies generated for this target.
include CMakeFiles/core_test.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/core_test.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/core_test.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/core_test.dir/flags.make

CMakeFiles/core_test.dir/test_core_compilation.cpp.o: CMakeFiles/core_test.dir/flags.make
CMakeFiles/core_test.dir/test_core_compilation.cpp.o: test_core_compilation.cpp
CMakeFiles/core_test.dir/test_core_compilation.cpp.o: CMakeFiles/core_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/DevEcoStudioProjects/ScreenDemo/bishare/src/main/cpp/core_test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/core_test.dir/test_core_compilation.cpp.o"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/core_test.dir/test_core_compilation.cpp.o -MF CMakeFiles/core_test.dir/test_core_compilation.cpp.o.d -o CMakeFiles/core_test.dir/test_core_compilation.cpp.o -c /Users/<USER>/DevEcoStudioProjects/ScreenDemo/bishare/src/main/cpp/core_test_build/test_core_compilation.cpp

CMakeFiles/core_test.dir/test_core_compilation.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/core_test.dir/test_core_compilation.cpp.i"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/DevEcoStudioProjects/ScreenDemo/bishare/src/main/cpp/core_test_build/test_core_compilation.cpp > CMakeFiles/core_test.dir/test_core_compilation.cpp.i

CMakeFiles/core_test.dir/test_core_compilation.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/core_test.dir/test_core_compilation.cpp.s"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/DevEcoStudioProjects/ScreenDemo/bishare/src/main/cpp/core_test_build/test_core_compilation.cpp -o CMakeFiles/core_test.dir/test_core_compilation.cpp.s

CMakeFiles/core_test.dir/types/bishare_status_codes.cpp.o: CMakeFiles/core_test.dir/flags.make
CMakeFiles/core_test.dir/types/bishare_status_codes.cpp.o: types/bishare_status_codes.cpp
CMakeFiles/core_test.dir/types/bishare_status_codes.cpp.o: CMakeFiles/core_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/DevEcoStudioProjects/ScreenDemo/bishare/src/main/cpp/core_test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/core_test.dir/types/bishare_status_codes.cpp.o"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/core_test.dir/types/bishare_status_codes.cpp.o -MF CMakeFiles/core_test.dir/types/bishare_status_codes.cpp.o.d -o CMakeFiles/core_test.dir/types/bishare_status_codes.cpp.o -c /Users/<USER>/DevEcoStudioProjects/ScreenDemo/bishare/src/main/cpp/core_test_build/types/bishare_status_codes.cpp

CMakeFiles/core_test.dir/types/bishare_status_codes.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/core_test.dir/types/bishare_status_codes.cpp.i"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/DevEcoStudioProjects/ScreenDemo/bishare/src/main/cpp/core_test_build/types/bishare_status_codes.cpp > CMakeFiles/core_test.dir/types/bishare_status_codes.cpp.i

CMakeFiles/core_test.dir/types/bishare_status_codes.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/core_test.dir/types/bishare_status_codes.cpp.s"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/DevEcoStudioProjects/ScreenDemo/bishare/src/main/cpp/core_test_build/types/bishare_status_codes.cpp -o CMakeFiles/core_test.dir/types/bishare_status_codes.cpp.s

# Object files for target core_test
core_test_OBJECTS = \
"CMakeFiles/core_test.dir/test_core_compilation.cpp.o" \
"CMakeFiles/core_test.dir/types/bishare_status_codes.cpp.o"

# External object files for target core_test
core_test_EXTERNAL_OBJECTS =

core_test: CMakeFiles/core_test.dir/test_core_compilation.cpp.o
core_test: CMakeFiles/core_test.dir/types/bishare_status_codes.cpp.o
core_test: CMakeFiles/core_test.dir/build.make
core_test: CMakeFiles/core_test.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/DevEcoStudioProjects/ScreenDemo/bishare/src/main/cpp/core_test_build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Linking CXX executable core_test"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/core_test.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/core_test.dir/build: core_test
.PHONY : CMakeFiles/core_test.dir/build

CMakeFiles/core_test.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/core_test.dir/cmake_clean.cmake
.PHONY : CMakeFiles/core_test.dir/clean

CMakeFiles/core_test.dir/depend:
	cd /Users/<USER>/DevEcoStudioProjects/ScreenDemo/bishare/src/main/cpp/core_test_build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/DevEcoStudioProjects/ScreenDemo/bishare/src/main/cpp/core_test_build /Users/<USER>/DevEcoStudioProjects/ScreenDemo/bishare/src/main/cpp/core_test_build /Users/<USER>/DevEcoStudioProjects/ScreenDemo/bishare/src/main/cpp/core_test_build /Users/<USER>/DevEcoStudioProjects/ScreenDemo/bishare/src/main/cpp/core_test_build /Users/<USER>/DevEcoStudioProjects/ScreenDemo/bishare/src/main/cpp/core_test_build/CMakeFiles/core_test.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/core_test.dir/depend

