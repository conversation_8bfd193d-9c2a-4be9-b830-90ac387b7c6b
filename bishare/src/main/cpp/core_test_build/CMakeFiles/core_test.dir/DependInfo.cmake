
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/Users/<USER>/DevEcoStudioProjects/ScreenDemo/bishare/src/main/cpp/core_test_build/test_core_compilation.cpp" "CMakeFiles/core_test.dir/test_core_compilation.cpp.o" "gcc" "CMakeFiles/core_test.dir/test_core_compilation.cpp.o.d"
  "/Users/<USER>/DevEcoStudioProjects/ScreenDemo/bishare/src/main/cpp/core_test_build/types/bishare_status_codes.cpp" "CMakeFiles/core_test.dir/types/bishare_status_codes.cpp.o" "gcc" "CMakeFiles/core_test.dir/types/bishare_status_codes.cpp.o.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
