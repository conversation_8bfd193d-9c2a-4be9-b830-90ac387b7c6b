# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.27

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/local/Cellar/cmake/3.27.6/bin/cmake

# The command to remove a file.
RM = /usr/local/Cellar/cmake/3.27.6/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/DevEcoStudioProjects/ScreenDemo/bishare/src/main/cpp/core_test_build

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/DevEcoStudioProjects/ScreenDemo/bishare/src/main/cpp/core_test_build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/usr/local/Cellar/cmake/3.27.6/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/usr/local/Cellar/cmake/3.27.6/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/DevEcoStudioProjects/ScreenDemo/bishare/src/main/cpp/core_test_build/CMakeFiles /Users/<USER>/DevEcoStudioProjects/ScreenDemo/bishare/src/main/cpp/core_test_build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/DevEcoStudioProjects/ScreenDemo/bishare/src/main/cpp/core_test_build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named core_test

# Build rule for target.
core_test: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 core_test
.PHONY : core_test

# fast build rule for target.
core_test/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/core_test.dir/build.make CMakeFiles/core_test.dir/build
.PHONY : core_test/fast

test_core_compilation.o: test_core_compilation.cpp.o
.PHONY : test_core_compilation.o

# target to build an object file
test_core_compilation.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/core_test.dir/build.make CMakeFiles/core_test.dir/test_core_compilation.cpp.o
.PHONY : test_core_compilation.cpp.o

test_core_compilation.i: test_core_compilation.cpp.i
.PHONY : test_core_compilation.i

# target to preprocess a source file
test_core_compilation.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/core_test.dir/build.make CMakeFiles/core_test.dir/test_core_compilation.cpp.i
.PHONY : test_core_compilation.cpp.i

test_core_compilation.s: test_core_compilation.cpp.s
.PHONY : test_core_compilation.s

# target to generate assembly for a file
test_core_compilation.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/core_test.dir/build.make CMakeFiles/core_test.dir/test_core_compilation.cpp.s
.PHONY : test_core_compilation.cpp.s

types/bishare_status_codes.o: types/bishare_status_codes.cpp.o
.PHONY : types/bishare_status_codes.o

# target to build an object file
types/bishare_status_codes.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/core_test.dir/build.make CMakeFiles/core_test.dir/types/bishare_status_codes.cpp.o
.PHONY : types/bishare_status_codes.cpp.o

types/bishare_status_codes.i: types/bishare_status_codes.cpp.i
.PHONY : types/bishare_status_codes.i

# target to preprocess a source file
types/bishare_status_codes.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/core_test.dir/build.make CMakeFiles/core_test.dir/types/bishare_status_codes.cpp.i
.PHONY : types/bishare_status_codes.cpp.i

types/bishare_status_codes.s: types/bishare_status_codes.cpp.s
.PHONY : types/bishare_status_codes.s

# target to generate assembly for a file
types/bishare_status_codes.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/core_test.dir/build.make CMakeFiles/core_test.dir/types/bishare_status_codes.cpp.s
.PHONY : types/bishare_status_codes.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... core_test"
	@echo "... test_core_compilation.o"
	@echo "... test_core_compilation.i"
	@echo "... test_core_compilation.s"
	@echo "... types/bishare_status_codes.o"
	@echo "... types/bishare_status_codes.i"
	@echo "... types/bishare_status_codes.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

