#include "bishare_status_codes.h"
#include <string>

/**
 * 扩展状态码错误消息实现
 */

const char* get_extended_error_message(bstatus_t status) {
    // 直接使用原始的错误消息，因为扩展状态码都映射到原始状态码
    const char* originalMsg = err2str(status);

    // 为映射的状态码提供更具体的错误消息
    switch (status) {
        case BS_PARAMS_ERROR:  // BS_INVALID_PARAM 映射到这里
            return "参数无效";
        case BS_NOT_FOUND:     // BS_NOT_SUPPORTED 映射到这里
            return "操作不支持";
        case BS_OPS_ERROR:     // BS_ALREADY_STARTED, BS_CONNECTION_CLOSED, BS_PERMISSION_DENIED, BS_DEVICE_BUSY, BS_ERROR 映射到这里
            return "操作错误";
        case BS_NOT_INIT:      // BS_INIT_FAILED, BS_SERVICE_NOT_RUNNING 映射到这里
            return "未初始化或服务未运行";
        case BS_ALLOCATE_ERROR: // BS_INSUFFICIENT_RESOURCES 映射到这里
            return "资源不足";
        case BS_SEND_ERROR:    // BS_NETWORK_ERROR 映射到这里
            return "网络错误";
        case BS_OK:
            return "成功";
        default:
            return originalMsg ? originalMsg : "未知错误";
    }
}
