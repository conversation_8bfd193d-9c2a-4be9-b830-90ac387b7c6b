#include "bishare_status_codes.h"
#include <string>

/**
 * 扩展状态码错误消息实现
 */

const char* get_extended_error_message(bstatus_t status) {
    // 首先尝试使用原始的错误消息
    if (status < BS_MAX) {
        return err2str(status);
    }
    
    // 对于扩展的状态码，提供自定义消息
    switch (status) {
        case BS_INVALID_PARAM:
            return "Invalid parameters";
        case BS_NOT_SUPPORTED:
            return "Operation not supported";
        case BS_ALREADY_STARTED:
            return "Operation already started";
        case BS_INIT_FAILED:
            return "Initialization failed";
        case BS_CONNECTION_CLOSED:
            return "Connection closed";
        case BS_SERVICE_NOT_RUNNING:
            return "Service not running";
        case BS_INSUFFICIENT_RESOURCES:
            return "Insufficient resources";
        case BS_PERMISSION_DENIED:
            return "Permission denied";
        case BS_DEVICE_BUSY:
            return "Device busy";
        case BS_NETWORK_ERROR:
            return "Network error";
        default:
            return "Unknown error";
    }
}
