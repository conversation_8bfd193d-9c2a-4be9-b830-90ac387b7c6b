#ifndef MOCK_NATIVE_API_H
#define MOCK_NATIVE_API_H

// Mock NAPI header for compilation testing on non-OpenHarmony platforms

#include <stdint.h>
#include <stddef.h>

#ifdef __cplusplus
extern "C" {
#endif

// NAPI types
typedef struct napi_env__ *napi_env;
typedef struct napi_value__ *napi_value;
typedef struct napi_ref__ *napi_ref;
typedef struct napi_deferred__ *napi_deferred;
typedef struct napi_callback_info__ *napi_callback_info;
typedef struct napi_async_work__ *napi_async_work;

// NAPI status
typedef enum {
    napi_ok,
    napi_invalid_arg,
    napi_object_expected,
    napi_string_expected,
    napi_name_expected,
    napi_function_expected,
    napi_number_expected,
    napi_boolean_expected,
    napi_array_expected,
    napi_generic_failure,
    napi_pending_exception,
    napi_cancelled,
    napi_escape_called_twice,
    napi_handle_scope_mismatch,
    napi_callback_scope_mismatch,
    napi_queue_full,
    napi_closing,
    napi_bigint_expected,
    napi_date_expected,
    napi_arraybuffer_expected,
    napi_detachable_arraybuffer_expected,
    napi_would_deadlock
} napi_status;

// NAPI value types
typedef enum {
    napi_undefined,
    napi_null,
    napi_boolean,
    napi_number,
    napi_string,
    napi_symbol,
    napi_object,
    napi_function,
    napi_external,
    napi_bigint
} napi_valuetype;

// NAPI property attributes
typedef enum {
    napi_default = 0,
    napi_writable = 1 << 0,
    napi_enumerable = 1 << 1,
    napi_configurable = 1 << 2,
    napi_static = 1 << 10
} napi_property_attributes;

// NAPI callback
typedef napi_value (*napi_callback)(napi_env env, napi_callback_info info);

// NAPI async work callbacks
typedef void (*napi_async_execute_callback)(napi_env env, void* data);
typedef void (*napi_async_complete_callback)(napi_env env, napi_status status, void* data);

// NAPI property descriptor
typedef struct {
    const char* utf8name;
    napi_value name;
    napi_callback method;
    napi_callback getter;
    napi_callback setter;
    napi_value value;
    napi_property_attributes attributes;
    void* data;
} napi_property_descriptor;

// NAPI module descriptor
typedef struct {
    int nm_version;
    unsigned int nm_flags;
    const char* nm_filename;
    napi_callback nm_register_func;
    const char* nm_modname;
    void* nm_priv;
    void* reserved[4];
} napi_module;

// Constants
#define NAPI_AUTO_LENGTH SIZE_MAX

// Mock function declarations (empty implementations for compilation)
napi_status napi_create_object(napi_env env, napi_value* result);
napi_status napi_create_string_utf8(napi_env env, const char* str, size_t length, napi_value* result);
napi_status napi_create_int32(napi_env env, int32_t value, napi_value* result);
napi_status napi_create_function(napi_env env, const char* utf8name, size_t length, napi_callback cb, void* data, napi_value* result);
napi_status napi_create_reference(napi_env env, napi_value value, uint32_t initial_refcount, napi_ref* result);
napi_status napi_create_promise(napi_env env, napi_deferred* deferred, napi_value* promise);
napi_status napi_create_async_work(napi_env env, napi_value async_resource, napi_value async_resource_name, napi_async_execute_callback execute, napi_async_complete_callback complete, void* data, napi_async_work* result);
napi_status napi_create_error(napi_env env, napi_value code, napi_value msg, napi_value* result);

napi_status napi_get_cb_info(napi_env env, napi_callback_info cbinfo, size_t* argc, napi_value* argv, napi_value* thisArg, void** data);
napi_status napi_get_value_string_utf8(napi_env env, napi_value value, char* buf, size_t bufsize, size_t* result);
napi_status napi_get_value_int32(napi_env env, napi_value value, int32_t* result);
napi_status napi_get_value_bool(napi_env env, napi_value value, bool* result);
napi_status napi_get_reference_value(napi_env env, napi_ref ref, napi_value* result);
napi_status napi_get_global(napi_env env, napi_value* result);
napi_status napi_get_undefined(napi_env env, napi_value* result);
napi_status napi_get_null(napi_env env, napi_value* result);
napi_status napi_get_boolean(napi_env env, bool value, napi_value* result);

napi_status napi_set_named_property(napi_env env, napi_value object, const char* utf8name, napi_value value);
napi_status napi_define_properties(napi_env env, napi_value object, size_t property_count, const napi_property_descriptor* properties);

napi_status napi_typeof(napi_env env, napi_value value, napi_valuetype* result);
napi_status napi_strict_equals(napi_env env, napi_value lhs, napi_value rhs, bool* result);

napi_status napi_call_function(napi_env env, napi_value recv, napi_value func, size_t argc, const napi_value* argv, napi_value* result);

napi_status napi_queue_async_work(napi_env env, napi_async_work work);
napi_status napi_delete_async_work(napi_env env, napi_async_work work);
napi_status napi_delete_reference(napi_env env, napi_ref ref);

napi_status napi_resolve_deferred(napi_env env, napi_deferred deferred, napi_value resolution);
napi_status napi_reject_deferred(napi_env env, napi_deferred deferred, napi_value rejection);

napi_status napi_throw_error(napi_env env, const char* code, const char* msg);

napi_status napi_create_arraybuffer(napi_env env, size_t byte_length, void** data, napi_value* result);

// Module registration
void napi_module_register(napi_module* mod);

// Macro for module definition
#define NAPI_MODULE(modname, regfunc) \
    static napi_module _module = { \
        1, \
        0, \
        __FILE__, \
        regfunc, \
        #modname, \
        0, \
        {0}, \
    }; \
    void __attribute__((constructor)) _register_##modname(void) { \
        napi_module_register(&_module); \
    }

#ifdef __cplusplus
}
#endif

#endif // MOCK_NATIVE_API_H
