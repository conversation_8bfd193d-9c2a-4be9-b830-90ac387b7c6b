#include "napi/native_api.h"
#include <cstring>
#include <iostream>

// Mock implementations for NAPI functions
// These are empty implementations for compilation testing only

napi_status napi_create_object(napi_env env, napi_value* result) {
    *result = nullptr;
    return napi_ok;
}

napi_status napi_create_string_utf8(napi_env env, const char* str, size_t length, napi_value* result) {
    *result = nullptr;
    return napi_ok;
}

napi_status napi_create_int32(napi_env env, int32_t value, napi_value* result) {
    *result = nullptr;
    return napi_ok;
}

napi_status napi_create_function(napi_env env, const char* utf8name, size_t length, napi_callback cb, void* data, napi_value* result) {
    *result = nullptr;
    return napi_ok;
}

napi_status napi_create_reference(napi_env env, napi_value value, uint32_t initial_refcount, napi_ref* result) {
    *result = nullptr;
    return napi_ok;
}

napi_status napi_create_promise(napi_env env, napi_deferred* deferred, napi_value* promise) {
    *deferred = nullptr;
    *promise = nullptr;
    return napi_ok;
}

napi_status napi_create_async_work(napi_env env, napi_value async_resource, napi_value async_resource_name, napi_async_execute_callback execute, napi_async_complete_callback complete, void* data, napi_async_work* result) {
    *result = nullptr;
    return napi_ok;
}

napi_status napi_create_error(napi_env env, napi_value code, napi_value msg, napi_value* result) {
    *result = nullptr;
    return napi_ok;
}

napi_status napi_create_arraybuffer(napi_env env, size_t byte_length, void** data, napi_value* result) {
    *data = nullptr;
    *result = nullptr;
    return napi_ok;
}

napi_status napi_get_cb_info(napi_env env, napi_callback_info cbinfo, size_t* argc, napi_value* argv, napi_value* thisArg, void** data) {
    if (argc) *argc = 0;
    if (thisArg) *thisArg = nullptr;
    if (data) *data = nullptr;
    return napi_ok;
}

napi_status napi_get_value_string_utf8(napi_env env, napi_value value, char* buf, size_t bufsize, size_t* result) {
    if (result) *result = 0;
    if (buf && bufsize > 0) buf[0] = '\0';
    return napi_ok;
}

napi_status napi_get_value_int32(napi_env env, napi_value value, int32_t* result) {
    if (result) *result = 0;
    return napi_ok;
}

napi_status napi_get_value_bool(napi_env env, napi_value value, bool* result) {
    if (result) *result = false;
    return napi_ok;
}

napi_status napi_get_reference_value(napi_env env, napi_ref ref, napi_value* result) {
    *result = nullptr;
    return napi_ok;
}

napi_status napi_get_global(napi_env env, napi_value* result) {
    *result = nullptr;
    return napi_ok;
}

napi_status napi_get_undefined(napi_env env, napi_value* result) {
    *result = nullptr;
    return napi_ok;
}

napi_status napi_get_null(napi_env env, napi_value* result) {
    *result = nullptr;
    return napi_ok;
}

napi_status napi_get_boolean(napi_env env, bool value, napi_value* result) {
    *result = nullptr;
    return napi_ok;
}

napi_status napi_set_named_property(napi_env env, napi_value object, const char* utf8name, napi_value value) {
    return napi_ok;
}

napi_status napi_define_properties(napi_env env, napi_value object, size_t property_count, const napi_property_descriptor* properties) {
    return napi_ok;
}

napi_status napi_typeof(napi_env env, napi_value value, napi_valuetype* result) {
    if (result) *result = napi_undefined;
    return napi_ok;
}

napi_status napi_strict_equals(napi_env env, napi_value lhs, napi_value rhs, bool* result) {
    if (result) *result = false;
    return napi_ok;
}

napi_status napi_call_function(napi_env env, napi_value recv, napi_value func, size_t argc, const napi_value* argv, napi_value* result) {
    if (result) *result = nullptr;
    return napi_ok;
}

napi_status napi_queue_async_work(napi_env env, napi_async_work work) {
    return napi_ok;
}

napi_status napi_delete_async_work(napi_env env, napi_async_work work) {
    return napi_ok;
}

napi_status napi_delete_reference(napi_env env, napi_ref ref) {
    return napi_ok;
}

napi_status napi_resolve_deferred(napi_env env, napi_deferred deferred, napi_value resolution) {
    return napi_ok;
}

napi_status napi_reject_deferred(napi_env env, napi_deferred deferred, napi_value rejection) {
    return napi_ok;
}

napi_status napi_throw_error(napi_env env, const char* code, const char* msg) {
    std::cerr << "NAPI Error [" << (code ? code : "UNKNOWN") << "]: " << (msg ? msg : "Unknown error") << std::endl;
    return napi_ok;
}

void napi_module_register(napi_module* mod) {
    std::cout << "Mock: Registering module " << (mod && mod->nm_modname ? mod->nm_modname : "unknown") << std::endl;
}
