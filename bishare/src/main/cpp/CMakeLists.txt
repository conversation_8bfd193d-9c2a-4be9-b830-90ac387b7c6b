# CMake最低版本要求
cmake_minimum_required(VERSION 3.4.1)

# 项目名称和语言 (CXX代表C++)
project(bishare_napi CXX)

# 设置C++标准 (推荐使用C++17，但C++14也可以)
set(CMAKE_CXX_STANDARD 14)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF) # 关闭编译器特定的扩展，以增强可移植性

# --- RPATH 设置 ---
# RPATH (运行时搜索路径) 告诉动态链接器在哪里查找共享库。
# "$ORIGIN" 是一个特殊变量，表示“包含此可执行文件或库的目录”。
# 这对于HAR包至关重要，因为所有ABI相关的.so文件通常都在同一个目录下。
set(CMAKE_SKIP_BUILD_RPATH FALSE)             # 确保在构建时设置RPATH
set(CMAKE_BUILD_WITH_INSTALL_RPATH TRUE)      # 构建时使用INSTALL_RPATH的值
set(CMAKE_INSTALL_RPATH "$ORIGIN")            # 对于安装目标，默认RPATH为$ORIGIN
# 对于macOS平台 (虽然主要针对OpenHarmony，但保持完整性)
if(APPLE)
    set(CMAKE_MACOSX_RPATH TRUE)
endif()


# --- ABI特定的预编译库路径 ---
# OHOS_ARCH 由OpenHarmony构建系统设置 (例如 arm64-v8a, armeabi-v7a)
set(PREBUILT_LIBS_BASE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/biservice/libs)
set(PREBUILT_LIBS_ABI_DIR ${PREBUILT_LIBS_BASE_DIR}/${OHOS_ARCH}) # e.g., thirdparty/biservice/libs/arm64-v8a

# --- cJSON 预编译库路径 ---
set(CJSON_PREBUILT_LIBS_BASE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/cjson/libs)
set(CJSON_PREBUILT_LIBS_ABI_DIR ${CJSON_PREBUILT_LIBS_BASE_DIR}/${OHOS_ARCH})

# --- bishare_napi 的源文件列表 ---
# 使用GLOB_RECURSE自动收集所有源文件
file(GLOB_RECURSE CORE_SOURCES "core/*.cpp")
file(GLOB_RECURSE DOMAIN_SOURCES "domain/*.cpp")
file(GLOB_RECURSE INFRASTRUCTURE_SOURCES "infrastructure/*.cpp")
file(GLOB_RECURSE INTERFACES_SOURCES "interfaces/*.cpp")

# 主模块入口文件
set(MAIN_SOURCES
    bishare_module.cpp
)

# Mock NAPI实现（用于非OpenHarmony平台编译测试）
set(MOCK_SOURCES
    mock_napi/mock_napi_impl.cpp
)

# 合并所有源文件
set(SOURCES
    ${MAIN_SOURCES}
    ${CORE_SOURCES}
    ${DOMAIN_SOURCES}
    ${INFRASTRUCTURE_SOURCES}
    ${INTERFACES_SOURCES}
    ${MOCK_SOURCES}
)

# --- 定义你的主NAPI库 (bishare_napi) ---
add_library(bishare_napi SHARED ${SOURCES})

# 为 bishare_napi 目标显式设置 RPATH，确保它能在运行时找到同目录下的依赖
set_target_properties(bishare_napi PROPERTIES INSTALL_RPATH "$ORIGIN")


# --- 为 bishare_napi 添加头文件包含目录 ---
target_include_directories(bishare_napi PRIVATE
    # 新的分层架构目录
    ${CMAKE_CURRENT_SOURCE_DIR}/core                              # 核心层头文件
    ${CMAKE_CURRENT_SOURCE_DIR}/domain                            # 领域层头文件
    ${CMAKE_CURRENT_SOURCE_DIR}/infrastructure                    # 基础设施层头文件
    ${CMAKE_CURRENT_SOURCE_DIR}/interfaces                        # 接口层头文件

    # Mock NAPI头文件（用于非OpenHarmony平台编译测试）
    ${CMAKE_CURRENT_SOURCE_DIR}/mock_napi                         # Mock NAPI头文件

    # 第三方库头文件
    ${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/biservice/include      # bishare-service, bishare-crypto-plugin 等库的头文件
    ${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/cjson/include          # cJSON 的头文件

    # 兼容性：保留原有include目录（如果还有文件的话）
    ${CMAKE_CURRENT_SOURCE_DIR}/include
)


# --- 定义 IMPORTED 预编译库 ---
# IMPORTED 目标告诉 CMake 这些库已经存在，不需要编译。
# (可以考虑移除 GLOBAL，因为都在同一 CMakeLists.txt 文件中)
# 1. libbishare-service.so (共享库)
add_library(bishare-service SHARED IMPORTED) # Removed GLOBAL (optional)
set_target_properties(bishare-service PROPERTIES
    IMPORTED_LOCATION "${PREBUILT_LIBS_ABI_DIR}/libbishare-service.so"
)

# 2. libbishare-crypto-plugin.so (共享库)
add_library(bishare-crypto-plugin SHARED IMPORTED) # Removed GLOBAL (optional)
set_target_properties(bishare-crypto-plugin PROPERTIES
    IMPORTED_LOCATION "${PREBUILT_LIBS_ABI_DIR}/libbishare-crypto-plugin.so"
)

# 3. liblog4cpp.a (静态库)
add_library(log4cpp STATIC IMPORTED) # Removed GLOBAL (optional)
set_target_properties(log4cpp PROPERTIES
    IMPORTED_LOCATION "${PREBUILT_LIBS_ABI_DIR}/liblog4cpp.a"
)

# 4. libcjson.a (静态库)
add_library(cjson_prebuilt STATIC IMPORTED) # Removed GLOBAL (optional)
set_target_properties(cjson_prebuilt PROPERTIES
    IMPORTED_LOCATION "${CJSON_PREBUILT_LIBS_ABI_DIR}/libcjson.a"
)


# --- 链接库到 bishare_napi ---
# PRIVATE: 依赖项仅用于 bishare_napi 的实现，不暴露给 bishare_napi 的使用者。
# PUBLIC: 如果 bishare_napi 的头文件包含了依赖库的头文件或类型，则使用 PUBLIC。
# INTERFACE: 如果 bishare_napi 自身不使用，但其使用者需要链接该依赖，则使用 INTERFACE。
target_link_libraries(bishare_napi PRIVATE
    bishare-service      # 链接 libbishare-service.so。这将提示构建系统打包此.so文件。
    bishare-crypto-plugin        # 链接 libbishare-crypto-plugin.so。这将提示构建系统打包此.so文件。
    log4cpp           # 链接 liblog4cpp.a。其代码将被合并到 libbishare_napi.so 中。
    cjson_prebuilt    # <--- 链接预编译的 cJSON 静态库

    # OpenHarmony 系统库
    libhilog_ndk.z.so       # 日志库
    libace_napi.z.so     # NAPI框架库 (注意：系统库通常不带 .so 后缀，.z 表示系统库)
    # libc++.so       # C++标准库，通常由工具链自动链接。如果遇到链接问题，可以尝试显式添加。
                      # 对于OpenHarmony NDK，可能是 libc++_shared.so 或 libc++_static.a，
                      # 但通常不需要手动链接，除非有特定需求或问题。
)


