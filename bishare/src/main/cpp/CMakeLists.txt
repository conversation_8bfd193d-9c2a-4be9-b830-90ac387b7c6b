# CMake最低版本要求
cmake_minimum_required(VERSION 3.4.1)

# 项目名称和语言 (CXX代表C++)
project(bishare_napi CXX)

# 设置C++标准 (推荐使用C++17，但C++14也可以)
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF) # 关闭编译器特定的扩展，以增强可移植性

# 平台检测
if(DEFINED OHOS_ARCH)
    set(IS_OHOS_PLATFORM TRUE)
    message(STATUS "构建目标平台: OpenHarmony (${OHOS_ARCH})")
else()
    set(IS_OHOS_PLATFORM FALSE)
    message(STATUS "构建目标平台: ${CMAKE_SYSTEM_NAME} (非OpenHarmony平台，仅用于编译测试)")
endif()

# --- RPATH 设置 ---
# RPATH (运行时搜索路径) 告诉动态链接器在哪里查找共享库。
# "$ORIGIN" 是一个特殊变量，表示“包含此可执行文件或库的目录”。
# 这对于HAR包至关重要，因为所有ABI相关的.so文件通常都在同一个目录下。
set(CMAKE_SKIP_BUILD_RPATH FALSE)             # 确保在构建时设置RPATH
set(CMAKE_BUILD_WITH_INSTALL_RPATH TRUE)      # 构建时使用INSTALL_RPATH的值
set(CMAKE_INSTALL_RPATH "$ORIGIN")            # 对于安装目标，默认RPATH为$ORIGIN
# 对于macOS平台 (虽然主要针对OpenHarmony，但保持完整性)
if(APPLE)
    set(CMAKE_MACOSX_RPATH TRUE)
endif()


# --- ABI特定的预编译库路径 ---
# OHOS_ARCH 由OpenHarmony构建系统设置 (例如 arm64-v8a, armeabi-v7a)
set(PREBUILT_LIBS_BASE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/biservice/libs)
set(PREBUILT_LIBS_ABI_DIR ${PREBUILT_LIBS_BASE_DIR}/${OHOS_ARCH}) # e.g., thirdparty/biservice/libs/arm64-v8a

# --- cJSON 预编译库路径 ---
set(CJSON_PREBUILT_LIBS_BASE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/cjson/libs)
set(CJSON_PREBUILT_LIBS_ABI_DIR ${CJSON_PREBUILT_LIBS_BASE_DIR}/${OHOS_ARCH})

# --- bishare_napi 的源文件列表 ---
# 根据平台条件收集源文件
if(IS_OHOS_PLATFORM)
    # OpenHarmony平台：包含所有源文件
    file(GLOB_RECURSE CORE_SOURCES "core/*.cpp")
    file(GLOB_RECURSE DOMAIN_SOURCES "domain/*.cpp")
    file(GLOB_RECURSE INFRASTRUCTURE_SOURCES "infrastructure/*.cpp")
    file(GLOB_RECURSE INTERFACES_SOURCES "interfaces/*.cpp")

    # 主模块入口文件
    set(MAIN_SOURCES
        bishare_module.cpp
    )
else()
    # 非OpenHarmony平台：排除NAPI相关文件，仅编译核心逻辑
    file(GLOB_RECURSE CORE_SOURCES "core/*.cpp")
    file(GLOB_RECURSE DOMAIN_SOURCES "domain/*.cpp")
    file(GLOB_RECURSE INFRASTRUCTURE_SOURCES "infrastructure/*.cpp")

    # 排除NAPI接口层
    # file(GLOB_RECURSE INTERFACES_SOURCES "interfaces/*.cpp")

    # 不包含主模块入口文件（因为它依赖NAPI）
    set(MAIN_SOURCES "")
endif()

# 类型定义文件（所有平台都需要）
set(TYPES_SOURCES
    types/bishare_status_codes.cpp
)

# 合并所有源文件
set(SOURCES
    ${MAIN_SOURCES}
    ${CORE_SOURCES}
    ${DOMAIN_SOURCES}
    ${INFRASTRUCTURE_SOURCES}
    ${INTERFACES_SOURCES}
    ${TYPES_SOURCES}
)

# --- 定义主库 ---
if(IS_OHOS_PLATFORM)
    # OpenHarmony平台：构建NAPI共享库
    add_library(bishare_napi SHARED ${SOURCES})
    # 为 bishare_napi 目标显式设置 RPATH，确保它能在运行时找到同目录下的依赖
    set_target_properties(bishare_napi PROPERTIES INSTALL_RPATH "$ORIGIN")
else()
    # 非OpenHarmony平台：构建静态库用于测试
    add_library(bishare_core STATIC ${SOURCES})
    set_target_properties(bishare_core PROPERTIES
        CXX_STANDARD 17
        CXX_STANDARD_REQUIRED ON
    )
endif()


# --- 添加头文件包含目录 ---
if(IS_OHOS_PLATFORM)
    # OpenHarmony平台的头文件包含
    target_include_directories(bishare_napi PRIVATE
        # 新的分层架构目录
        ${CMAKE_CURRENT_SOURCE_DIR}/core                              # 核心层头文件
        ${CMAKE_CURRENT_SOURCE_DIR}/domain                            # 领域层头文件
        ${CMAKE_CURRENT_SOURCE_DIR}/infrastructure                    # 基础设施层头文件
        ${CMAKE_CURRENT_SOURCE_DIR}/interfaces                        # 接口层头文件

        # 类型定义头文件
        ${CMAKE_CURRENT_SOURCE_DIR}/types                             # 类型定义头文件

        # 第三方库头文件
        ${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/biservice/include      # bishare-service, bishare-crypto-plugin 等库的头文件
        ${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/cjson/include          # cJSON 的头文件

        # 兼容性：保留原有include目录（如果还有文件的话）
        ${CMAKE_CURRENT_SOURCE_DIR}/include
    )
else()
    # 非OpenHarmony平台的头文件包含（排除NAPI相关）
    target_include_directories(bishare_core PRIVATE
        # 新的分层架构目录
        ${CMAKE_CURRENT_SOURCE_DIR}/core                              # 核心层头文件
        ${CMAKE_CURRENT_SOURCE_DIR}/domain                            # 领域层头文件
        ${CMAKE_CURRENT_SOURCE_DIR}/infrastructure                    # 基础设施层头文件

        # 类型定义头文件
        ${CMAKE_CURRENT_SOURCE_DIR}/types                             # 类型定义头文件

        # 第三方库头文件
        ${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/biservice/include      # bishare-service, bishare-crypto-plugin 等库的头文件
        ${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/cjson/include          # cJSON 的头文件
    )
endif()


# --- 定义 IMPORTED 预编译库和链接 ---
if(IS_OHOS_PLATFORM)
    # OpenHarmony平台：定义预编译库
    # 1. libbishare-service.so (共享库)
    add_library(bishare-service SHARED IMPORTED)
    set_target_properties(bishare-service PROPERTIES
        IMPORTED_LOCATION "${PREBUILT_LIBS_ABI_DIR}/libbishare-service.so"
    )

    # 2. libbishare-crypto-plugin.so (共享库)
    add_library(bishare-crypto-plugin SHARED IMPORTED)
    set_target_properties(bishare-crypto-plugin PROPERTIES
        IMPORTED_LOCATION "${PREBUILT_LIBS_ABI_DIR}/libbishare-crypto-plugin.so"
    )

    # 3. liblog4cpp.a (静态库)
    add_library(log4cpp STATIC IMPORTED)
    set_target_properties(log4cpp PROPERTIES
        IMPORTED_LOCATION "${PREBUILT_LIBS_ABI_DIR}/liblog4cpp.a"
    )

    # 4. libcjson.a (静态库)
    add_library(cjson_prebuilt STATIC IMPORTED)
    set_target_properties(cjson_prebuilt PROPERTIES
        IMPORTED_LOCATION "${CJSON_PREBUILT_LIBS_ABI_DIR}/libcjson.a"
    )

    # 链接库到 bishare_napi
    target_link_libraries(bishare_napi PRIVATE
        bishare-service      # 链接 libbishare-service.so
        bishare-crypto-plugin        # 链接 libbishare-crypto-plugin.so
        log4cpp           # 链接 liblog4cpp.a
        cjson_prebuilt    # 链接预编译的 cJSON 静态库

        # OpenHarmony 系统库
        libhilog_ndk.z.so       # 日志库
        libace_napi.z.so     # NAPI框架库
    )
else()
    # 非OpenHarmony平台：不链接OpenHarmony特定的库
    message(STATUS "非OpenHarmony平台，跳过OpenHarmony特定库的链接")
endif()


