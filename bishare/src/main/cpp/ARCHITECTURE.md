# BiShare C++ 架构设计

## 🏗️ 整体架构

本项目采用分层架构设计，遵循SOLID原则和领域驱动设计（DDD）理念。

```
bishare/src/main/cpp/
├── core/                    # 核心层
│   ├── facade/             # 门面模式
│   ├── managers/           # 管理器
│   └── operations/         # 操作工厂
├── domain/                 # 领域层
│   ├── device/            # 设备领域
│   ├── recording/         # 录制领域
│   └── network/           # 网络领域
├── infrastructure/        # 基础设施层
│   ├── async/            # 异步处理
│   ├── logging/          # 日志系统
│   └── threading/        # 线程管理
├── interfaces/           # 接口层
│   ├── napi/            # NAPI接口
│   └── service/         # 服务接口
├── utils/               # 工具类
├── thirdparty/         # 第三方库
└── types/              # 类型定义
```

## 📋 分层说明

### 1. 接口层 (Interfaces)
- **职责**: 提供外部接口，处理与外部系统的交互
- **组件**:
  - `BiShareNapiInterface`: JavaScript绑定接口
  - 服务接口定义

### 2. 核心层 (Core)
- **职责**: 协调各个组件，提供统一的业务入口
- **组件**:
  - `BiShareFacade`: 门面类，统一外部接口
  - `ServiceManager`: 服务管理器
  - `CallbackManager`: 回调管理器
  - `OperationFactory`: 操作工厂

### 3. 领域层 (Domain)
- **职责**: 实现具体的业务逻辑
- **组件**:
  - `DeviceService`: 设备管理服务
  - `RecordingService`: 录制管理服务
  - `NetworkService`: 网络管理服务

### 4. 基础设施层 (Infrastructure)
- **职责**: 提供技术支撑和通用功能
- **组件**:
  - `AsyncExecutor`: 异步执行器
  - 日志系统
  - 线程管理

## 🎯 设计模式

### 1. 门面模式 (Facade Pattern)
- `BiShareFacade` 作为统一入口点
- 简化复杂子系统的接口
- 降低客户端与子系统的耦合

### 2. 工厂模式 (Factory Pattern)
- `OperationFactory` 创建操作对象
- 支持运行时动态创建
- 便于扩展新的操作类型

### 3. 单例模式 (Singleton Pattern)
- `BiShareFacade` 确保全局唯一实例
- 提供全局访问点

### 4. 策略模式 (Strategy Pattern)
- 不同的操作实现不同的策略
- 运行时可切换算法

## 🔄 数据流

```
JavaScript Layer
       ↓
BiShareNapiInterface (接口层)
       ↓
BiShareFacade (核心层)
       ↓
ServiceManager (核心层)
       ↓
Domain Services (领域层)
       ↓
Native BiShare Service
```

## 📦 依赖关系

### 依赖方向
- 接口层 → 核心层
- 核心层 → 领域层
- 领域层 → 基础设施层
- 所有层 → 工具层

### 依赖注入
- 使用构造函数注入
- 通过共享指针管理生命周期
- 避免循环依赖

## 🚀 优势

### 1. 高内聚低耦合
- 每个模块职责单一明确
- 模块间依赖关系清晰
- 便于单元测试

### 2. 可扩展性
- 新增功能只需添加新的服务类
- 操作工厂支持动态注册
- 遵循开闭原则

### 3. 可维护性
- 分层架构便于理解和维护
- 代码组织清晰
- 便于团队协作

### 4. 可测试性
- 依赖注入便于Mock
- 单一职责便于单元测试
- 接口抽象便于集成测试

## 🔧 使用示例

### 添加新的操作
1. 在 `OperationType` 枚举中添加新类型
2. 创建对应的操作类实现 `IOperation` 接口
3. 在 `OperationFactory` 中注册新操作
4. 在 `BiShareNapiInterface` 中添加对应的NAPI方法

### 添加新的领域服务
1. 在 `domain/` 目录下创建新的服务类
2. 在 `ServiceManager` 中注册和管理新服务
3. 在相关操作中使用新服务

## 📝 注意事项

1. **线程安全**: 所有共享状态都需要考虑线程安全
2. **内存管理**: 使用智能指针管理对象生命周期
3. **错误处理**: 统一的错误处理机制
4. **日志记录**: 关键操作都需要记录日志
5. **性能考虑**: 避免不必要的对象创建和拷贝
