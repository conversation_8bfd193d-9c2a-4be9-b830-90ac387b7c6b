#ifndef ASYNC_EXECUTOR_H
#define ASYNC_EXECUTOR_H

#include <napi/native_api.h>
#include <functional>
#include <memory>
#include <string>
#include <future>

namespace OHOS {
    namespace BiShare {
        namespace Infrastructure {

            /**
             * 异步工作数据
             */
            struct AsyncWorkData {
                napi_env env;
                napi_async_work work;
                napi_deferred deferred;
                napi_ref callback;
                std::string resourceName;
                std::string errorMessage;
                void* userData;

                AsyncWorkData(napi_env e, const std::string& name) 
                    : env(e), work(nullptr), deferred(nullptr), callback(nullptr), 
                      resourceName(name), userData(nullptr) {}

                ~AsyncWorkData() {
                    if (work) {
                        napi_delete_async_work(env, work);
                    }
                    if (callback) {
                        napi_delete_reference(env, callback);
                    }
                }
            };

            /**
             * 异步执行器 - 处理异步操作的执行
             * 
             * 职责：
             * 1. 提供统一的异步操作执行框架
             * 2. 管理异步工作的生命周期
             * 3. 处理Promise和回调两种模式
             * 4. 提供错误处理和结果转换
             */
            class AsyncExecutor {
            public:
                using ExecuteFunction = std::function<void(AsyncWorkData*)>;
                using CompleteFunction = std::function<napi_value(napi_env, AsyncWorkData*)>;

                /**
                 * 执行异步操作（Promise模式）
                 */
                static napi_value ExecuteAsync(napi_env env, const std::string& resourceName,
                                             ExecuteFunction executeFunc, CompleteFunction completeFunc,
                                             void* userData = nullptr);

                /**
                 * 执行异步操作（回调模式）
                 */
                static napi_value ExecuteAsyncWithCallback(napi_env env, napi_value callback,
                                                         const std::string& resourceName,
                                                         ExecuteFunction executeFunc, 
                                                         CompleteFunction completeFunc,
                                                         void* userData = nullptr);

                /**
                 * 执行异步操作（自动检测模式）
                 */
                static napi_value ExecuteAsyncAuto(napi_env env, napi_callback_info info,
                                                 const std::string& resourceName,
                                                 ExecuteFunction executeFunc, 
                                                 CompleteFunction completeFunc,
                                                 void* userData = nullptr);

                /**
                 * 创建Promise
                 */
                static napi_value CreatePromise(napi_env env, napi_deferred* deferred);

                /**
                 * 解析Promise（成功）
                 */
                static void ResolvePromise(napi_env env, napi_deferred deferred, napi_value result);

                /**
                 * 拒绝Promise（失败）
                 */
                static void RejectPromise(napi_env env, napi_deferred deferred, const std::string& error);

                /**
                 * 调用回调函数
                 */
                static void InvokeCallback(napi_env env, napi_ref callback, napi_value error, napi_value result);

                /**
                 * 创建错误对象
                 */
                static napi_value CreateError(napi_env env, const std::string& message);

            private:
                /**
                 * 异步执行回调
                 */
                static void ExecuteCallback(napi_env env, void* data);

                /**
                 * 异步完成回调
                 */
                static void CompleteCallback(napi_env env, napi_status status, void* data);

                /**
                 * 内部数据结构
                 */
                struct InternalAsyncData {
                    AsyncWorkData* workData;
                    ExecuteFunction executeFunc;
                    CompleteFunction completeFunc;
                    bool usePromise;

                    InternalAsyncData(AsyncWorkData* wd, ExecuteFunction ef, CompleteFunction cf, bool promise)
                        : workData(wd), executeFunc(ef), completeFunc(cf), usePromise(promise) {}
                };
            };

            /**
             * 异步操作辅助宏
             */
            #define ASYNC_EXECUTE(resourceName, executeCode, completeCode) \
                AsyncExecutor::ExecuteAsyncAuto(env, info, resourceName, \
                    [](AsyncWorkData* workData) { executeCode }, \
                    [](napi_env env, AsyncWorkData* workData) -> napi_value { completeCode })

            #define ASYNC_EXECUTE_WITH_USERDATA(resourceName, userData, executeCode, completeCode) \
                AsyncExecutor::ExecuteAsyncAuto(env, info, resourceName, \
                    [](AsyncWorkData* workData) { executeCode }, \
                    [](napi_env env, AsyncWorkData* workData) -> napi_value { completeCode }, \
                    userData)

        } // namespace Infrastructure
    } // namespace BiShare
} // namespace OHOS

#endif // ASYNC_EXECUTOR_H
