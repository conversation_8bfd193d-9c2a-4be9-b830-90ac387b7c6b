cmake_minimum_required(VERSION 3.5)

project(BiShareArchitectureTest)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 编译测试程序
add_executable(bishare_test test_build.cpp)

# 设置编译选项
target_compile_options(bishare_test PRIVATE
    -Wall
    -Wextra
    -std=c++17
)

# 如果是Debug模式，添加调试信息
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(bishare_test PRIVATE -g -O0)
else()
    target_compile_options(bishare_test PRIVATE -O2)
endif()

# 打印一些信息
message(STATUS "Building BiShare Architecture Test")
message(STATUS "C++ Standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "Build Type: ${CMAKE_BUILD_TYPE}")
message(STATUS "Compiler: ${CMAKE_CXX_COMPILER_ID}")
