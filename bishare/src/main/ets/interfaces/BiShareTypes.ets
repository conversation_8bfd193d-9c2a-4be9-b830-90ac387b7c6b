/**
 * BiShare type definitions
 */

/**
 * Blog priority levels
 */
export enum BlogPriority {
  EMERG = 0,
  FATAL = 1,
  ALERT = 2,
  CRIT = 3,
  ERROR = 4,
  WARN = 5,
  NOTICE = 6,
  INFO = 7,
  DEBUG = 8
}

/**
 * Event types
 */
export enum EventType {
  DEVICE_INFO = 1,
  DEVICE_STATUS = 2,
  CONNECT_STATUS = 3,
  KEY_VALUE = 4,
  ERROR_INFO = 5,
  MEDIA_CENTER = 6,
  SCREEN_RECORD = 7,
  FILE_ACTION = 8,
  WFD_EVENT = 9,
  ABILITY_ACTION = 10,
  DEVICE_INFO_LIST = 11,
  WINDOWS_INFO_LIST = 12,
  MONITORS_INFO_LIST = 13,
  LOG_INFO = 99
}

/**
 * Recording direction
 */
export enum Direction {
  NULL = 0,
  SEND = 1,
  RECV = 2
}

/**
 * Buffer types
 */
export enum BufferType {
  VIDEO = 1,
  AUDIO = 2
}

/**
 * BiShare status codes
 */
export enum BiShareStatus {
  OK = 0,
  NO_ERROR = 0,
  NOT_INIT = 1,
  NOT_FOUND = 2,
  PARAMS_ERROR = 3,
  LIBRARY_ERROR = 4,
  OPS_ERROR = 5,
  RELEASE_ERROR = 6,
  TIMEOUT = 7,
  ALLOCATE_ERROR = 8,
  OPEN_ERROR = 9,
  SEND_ERROR = 10,
  RECV_ERROR = 11
}

/**
 * Device information interface
 */
export interface DeviceInfo {
  id: string;
  name: string;
  address: string;
  model: string;
  pincode: string;
  status: number;
  connected: boolean;
}

/**
 * Event data interface
 */
export interface EventData {
  type: EventType;
  data: string;
}

/**
 * Packet data interface
 */
export interface PacketData {
  session: number;
  bufferType: BufferType;
  buffer: ArrayBuffer;
  width: number;
  height: number;
  timestamp: number;
}

/**
 * Initialization options
 */
export interface InitOptions {
  isConsole: boolean;
  isFile: boolean;
  filePath: string;
  priority?: BlogPriority;
}

/**
 * Recording options
 */
export interface RecordingOptions {
  session: number;
  displayId: number;
  direction: Direction;
}

/**
 * Screenshot options
 */
export interface ScreenshotOptions {
  filePath: string;
  top: number;
  bottom: number;
  left: number;
  right: number;
}

/**
 * Size options
 */
export interface SizeOptions {
  screenWidth: number;
  screenHeight: number;
  videoWidth: number;
  videoHeight: number;
}

/**
 * Device information options
 */
export interface DeviceInfoOptions {
  name: string;
  password: string;
}

/**
 * 网络类型
 */
export enum NetworkType {
  NotNetwork = 0,
  Ethernet = 1,
  Wlan = 2,
}

/**
 * 设置本机网络参数
 */
export interface NetworkInfoOptions {
  networkType: NetworkType;
  /**
   * 本机IP
   */
  addr: string;
  /**
   * 本机MAC
   */
  mac: string;
}

/**
 * Event callback type
 */
export type EventCallback = (data: EventData) => void;

/**
 * Standard callback type
 */
export type Callback<T> = (error: Error | null, result: T) => void;