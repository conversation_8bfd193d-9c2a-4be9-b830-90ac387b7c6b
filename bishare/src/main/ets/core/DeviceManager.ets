import createLogger from '../utils/Logger';
import { DeviceInfo, DeviceInfoOptions, Callback, NetworkInfoOptions } from '../interfaces/BiShareTypes';
import {
  clearDiscoveredDevices,
  discoverDevices, findRemoteDevice,
  getCurrentDirector,
  getDeviceModel,
  getDiscoveredDevices,
  getRootPath,
  resetDeviceModel,
  setDeviceInfo,
  setDeviceModel,
  setNetworkInfo
} from 'libbishare_napi.so';

const logger = createLogger('BiShare.DeviceManager');

/**
 * Device Manager class - handles device operations
 */
export default class DeviceManager {
  private static instance: DeviceManager;
  private deviceList: Map<string, DeviceInfo>;

  /**
   * Private constructor to implement Singleton pattern
   */
  private constructor() {
    this.deviceList = new Map<string, DeviceInfo>();
    logger.info('DeviceManager created');
  }

  /**
   * Get DeviceManager instance (Singleton)
   * @returns DeviceManager instance
   */
  static getInstance(): DeviceManager {
    if (!DeviceManager.instance) {
      DeviceManager.instance = new DeviceManager();
    }
    return DeviceManager.instance;
  }

  /**
   * Discover devices
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  discoverDevices(callback?: Callback<boolean>): Promise<boolean> | void {
    if (callback) {
      discoverDevices((error: Error | null, result: boolean) => {
        if (error) {
          logger.error(`Failed to discover devices with callback: ${error.message}`);
        } else {
          logger.info(`Device discovery started with callback: ${result}`);
        }
        callback(error, result);
      });
    } else {
      return new Promise<boolean>((resolve, reject) => {
        discoverDevices((error: Error | null, result: boolean) => {
          if (error) {
            logger.error(`Failed to discover devices: ${error.message}`);
            reject(error);
          } else {
            logger.info(`Device discovery started: ${result}`);
            resolve(result);
          }
        });
      });
    }
  }

  /**
   * Clear discovered devices
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  clearDiscoveredDevices(callback?: Callback<boolean>): Promise<boolean> | void {
    if (callback) {
      clearDiscoveredDevices((error: Error | null, result: boolean) => {
        if (error) {
          logger.error(`Failed to clear discovered devices with callback: ${error.message}`);
        } else {
          // Clear local device list
          this.deviceList.clear();
          logger.info('Cleared discovered devices with callback');
        }
        callback(error, result);
      });
    } else {
      return new Promise<boolean>((resolve, reject) => {
        clearDiscoveredDevices((error: Error | null, result: boolean) => {
          if (error) {
            logger.error(`Failed to clear discovered devices: ${error.message}`);
            reject(error);
          } else {
            // Clear local device list
            this.deviceList.clear();
            logger.info('Cleared discovered devices');
            resolve(result);
          }
        });
      });
    }
  }

  /**
   * Get discovered devices
   * @param callback Optional callback function
   * @returns Promise<DeviceInfo[]> if no callback is provided
   */
  getDiscoveredDevices(callback?: Callback<DeviceInfo[]>): Promise<DeviceInfo[]> | void {
    // Function to convert the device list map to array
    const getDeviceArray = (): DeviceInfo[] => {
      return Array.from(this.deviceList.values());
    };

    if (callback) {
      getDiscoveredDevices((error: Error | null, result: boolean) => {
        if (error) {
          logger.error(`Failed to get discovered devices with callback: ${error.message}`);
          callback(error, []);
        } else {
          const deviceArr = getDeviceArray();
          logger.info(`Retrieved discovered devices with callback: ${deviceArr}`);
          callback(null, deviceArr);
        }
      });
    } else {
      return new Promise<DeviceInfo[]>((resolve, reject) => {
        getDiscoveredDevices((error: Error | null, result: boolean) => {
          if (error) {
            logger.error(`Failed to get discovered devices: ${error.message}`);
            reject(error);
          } else {
            const deviceArr = getDeviceArray();
            logger.info(`Retrieved discovered devices: ${deviceArr}`);
            resolve(deviceArr);
          }
        });
      });
    }
  }

  /**
   * Set device information
   * @param options Device information options
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  setDeviceInfo(options: DeviceInfoOptions, callback?: Callback<boolean>): Promise<boolean> | void {
    if (callback) {
      setDeviceInfo(options.name, options.password, (error: Error | null, result: boolean) => {
        if (error) {
          logger.error(`Failed to set device info with callback: ${error.message}`);
        } else {
          logger.info(`Set device info: name with callback=${options.name}`);
        }
        callback(error, result);
      });
    } else {
      return new Promise<boolean>((resolve, reject) => {
        setDeviceInfo(options.name, options.password, (error: Error | null, result: boolean) => {
          if (error) {
            logger.error(`Failed to set device info: ${error.message}`);
            reject(error);
          } else {
            logger.info(`Set device info: name=${options.name}`);
            resolve(result);
          }
        });
      });
    }
  }

  /**
   * Set device model
   * @param model Device model
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  setDeviceModel(model: string, callback?: Callback<boolean>): Promise<boolean> | void {
    if (callback) {
      setDeviceModel(model, (error: Error | null, result: boolean) => {
        if (error) {
          logger.error(`Failed to set device model with callback: ${error.message}`);
        } else {
          logger.info(`Set device model with callback: ${model}`);
        }
        callback(error, result);
      });
    } else {
      return new Promise<boolean>((resolve, reject) => {
        setDeviceModel(model, (error: Error | null, result: boolean) => {
          if (error) {
            logger.error(`Failed to set device model: ${error.message}`);
            reject(error);
          } else {
            logger.info(`Set device model: ${model}`);
            resolve(result);
          }
        });
      });
    }
  }

  /**
   * Get device model
   * @returns Device model
   */
  getDeviceModel(): string {
    try {
      const model = getDeviceModel() as string;
      logger.info(`Retrieved device model: ${model}`);
      return model;
    } catch (error) {
      logger.error(`Failed to get device model: ${error.message}`);
      return '';
    }
  }

  /**
   * Reset device model
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  resetDeviceModel(callback?: Callback<boolean>): Promise<boolean> | void {
    if (callback) {
      resetDeviceModel((error: Error | null, result: boolean) => {
        if (error) {
          logger.error(`Failed to reset device model with callback: ${error.message}`);
        } else {
          logger.info('Reset device model with callback');
        }
        callback(error, result);
      });
    } else {
      return new Promise<boolean>((resolve, reject) => {
        resetDeviceModel((error: Error | null, result: boolean) => {
          if (error) {
            logger.error(`Failed to reset device model: ${error.message}`);
            reject(error);
          } else {
            logger.info('Reset device model');
            resolve(result);
          }
        });
      });
    }
  }

  /**
   * Get root path
   * @returns Root path
   */
  getRootPath(): string {
    try {
      const path = getRootPath() as string;
      logger.info(`Retrieved root path: ${path}`);
      return path;
    } catch (error) {
      logger.error(`Failed to get root path: ${error.message}`);
      return '';
    }
  }

  /**
   * Get current directory
   * @returns Current directory
   */
  getCurrentDirector(): string {
    try {
      const path = getCurrentDirector() as string;
      logger.info(`Retrieved current directory: ${path}`);
      return path;
    } catch (error) {
      logger.error(`Failed to get current directory: ${error.message}`);
      return '';
    }
  }

  /**
   * Find a remote device
   * @param pincode Device PIN code
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  findRemoteDevice(pincode: string, callback?: Callback<boolean>): Promise<boolean> | void {
    if (callback) {
      findRemoteDevice(pincode, (error: Error | null, result: boolean) => {
        if (error) {
          logger.error(`Failed to find remote device with callback: ${error.message}`);
        } else {
          logger.info(`Finding remote device with pincode with callback: ${pincode}`);
        }
        callback(error, result);
      });
    } else {
      return new Promise<boolean>((resolve, reject) => {
        findRemoteDevice(pincode, (error: Error | null, result: boolean) => {
          if (error) {
            logger.error(`Failed to find remote device: ${error.message}`);
            reject(error);
          } else {
            logger.info(`Finding remote device with pincode: ${pincode}`);
            resolve(result);
          }
        });
      });
    }
  }

  /**
   * 设置本机网络参数
   * @param options 网络信息
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  setNetworkInfo(options: NetworkInfoOptions, callback?: Callback<boolean>): Promise<boolean> | void {
    if (callback) {
      setNetworkInfo(options.networkType || 0, options.addr, options.mac, (error: Error | null, result: boolean) => {
        if (error) {
          logger.error(`Failed to set network info with callback: ${error.message}`);
        } else {
          logger.info(`Setting Network Info with callback: ${JSON.stringify(options)}`);
        }
        callback(error, result);
      });
    } else {
      return new Promise<boolean>((resolve, reject) => {
        setNetworkInfo(options.networkType || 0, options.addr, options.mac, (error: Error | null, result: boolean) => {
          if (error) {
            logger.error(`Failed to set network info: ${error.message}`);
            reject(error);
          } else {
            logger.info(`Setting Network Info: ${JSON.stringify(options)}`);
            resolve(result);
          }
        });
      });
    }
  }

  /**
   * Add a device to the local device list
   * @param device Device information
   */
  addDevice(device: DeviceInfo): void {
    this.deviceList.set(device.id, device);
    logger.info(`Added device: ${device.name} (${device.id})`);
  }

  /**
   * Remove a device from the local device list
   * @param deviceId Device ID
   */
  removeDevice(deviceId: string): void {
    if (this.deviceList.has(deviceId)) {
      this.deviceList.delete(deviceId);
      logger.info(`Removed device: ${deviceId}`);
    }
  }

  /**
   * Update a device in the local device list
   * @param device Device information
   */
  updateDevice(device: DeviceInfo): void {
    if (this.deviceList.has(device.id)) {
      this.deviceList.set(device.id, device);
      logger.info(`Updated device: ${device.name} (${device.id})`);
    }
  }
}