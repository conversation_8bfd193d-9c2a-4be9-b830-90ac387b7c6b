/**
 * BiShare框架错误码定义
 * 
 * 这个文件定义了BiShare框架中使用的所有错误码常量，
 * 与C++层的错误码定义保持一致。
 */

/**
 * BiShare错误码枚举
 */
export enum BiShareErrorCode {
  /** 成功 */
  BS_OK = 0,
  
  /** 一般错误 */
  BS_ERROR = -1,
  
  /** 未初始化 */
  BS_NOT_INIT = -2,
  
  /** 无效参数 */
  BS_INVALID_PARAM = -3,
  
  /** 不支持的操作 */
  BS_NOT_SUPPORTED = -4,
  
  /** 权限拒绝 */
  BS_PERMISSION_DENIED = -5,
  
  /** 设备未找到 */
  BS_DEVICE_NOT_FOUND = -6,
  
  /** 连接失败 */
  BS_CONNECTION_FAILED = -7,
  
  /** 超时 */
  BS_TIMEOUT = -8,
  
  /** 内存不足 */
  BS_INSUFFICIENT_MEMORY = -9
}

/**
 * 错误码常量对象（与原生模块导出的常量对应）
 */
export const ErrorCodes = {
  /** 成功 */
  BS_OK: 0,
  
  /** 一般错误 */
  BS_ERROR: -1,
  
  /** 未初始化 */
  BS_NOT_INIT: -2,
  
  /** 无效参数 */
  BS_INVALID_PARAM: -3,
  
  /** 不支持的操作 */
  BS_NOT_SUPPORTED: -4,
  
  /** 权限拒绝 */
  BS_PERMISSION_DENIED: -5,
  
  /** 设备未找到 */
  BS_DEVICE_NOT_FOUND: -6,
  
  /** 连接失败 */
  BS_CONNECTION_FAILED: -7,
  
  /** 超时 */
  BS_TIMEOUT: -8,
  
  /** 内存不足 */
  BS_INSUFFICIENT_MEMORY: -9
} as const;

/**
 * 错误码类型
 */
export type ErrorCodeType = typeof ErrorCodes[keyof typeof ErrorCodes];

/**
 * 操作结果接口
 */
export interface BiShareResult {
  /** 操作是否成功 */
  success: boolean;
  
  /** 错误码 */
  code: ErrorCodeType;
  
  /** 错误或成功消息 */
  message: string;
  
  /** 附加数据（可选） */
  data?: any;
}

/**
 * 错误信息映射
 */
export const ErrorMessages: Record<ErrorCodeType, string> = {
  [ErrorCodes.BS_OK]: "操作成功",
  [ErrorCodes.BS_ERROR]: "一般错误",
  [ErrorCodes.BS_NOT_INIT]: "BiShare服务未初始化",
  [ErrorCodes.BS_INVALID_PARAM]: "无效参数",
  [ErrorCodes.BS_NOT_SUPPORTED]: "不支持的操作",
  [ErrorCodes.BS_PERMISSION_DENIED]: "权限拒绝",
  [ErrorCodes.BS_DEVICE_NOT_FOUND]: "设备未找到",
  [ErrorCodes.BS_CONNECTION_FAILED]: "连接失败",
  [ErrorCodes.BS_TIMEOUT]: "操作超时",
  [ErrorCodes.BS_INSUFFICIENT_MEMORY]: "内存不足"
};

/**
 * 错误码工具类
 */
export class BiShareErrorUtils {
  /**
   * 检查操作是否成功
   * @param code 错误码
   * @returns 是否成功
   */
  static isSuccess(code: ErrorCodeType): boolean {
    return code === ErrorCodes.BS_OK;
  }
  
  /**
   * 检查是否为错误
   * @param code 错误码
   * @returns 是否为错误
   */
  static isError(code: ErrorCodeType): boolean {
    return code !== ErrorCodes.BS_OK;
  }
  
  /**
   * 获取错误消息
   * @param code 错误码
   * @returns 错误消息
   */
  static getErrorMessage(code: ErrorCodeType): string {
    return ErrorMessages[code] || "未知错误";
  }
  
  /**
   * 检查是否为初始化相关错误
   * @param code 错误码
   * @returns 是否为初始化错误
   */
  static isInitError(code: ErrorCodeType): boolean {
    return code === ErrorCodes.BS_NOT_INIT;
  }
  
  /**
   * 检查是否为参数相关错误
   * @param code 错误码
   * @returns 是否为参数错误
   */
  static isParamError(code: ErrorCodeType): boolean {
    return code === ErrorCodes.BS_INVALID_PARAM;
  }
  
  /**
   * 检查是否为网络相关错误
   * @param code 错误码
   * @returns 是否为网络错误
   */
  static isNetworkError(code: ErrorCodeType): boolean {
    return code === ErrorCodes.BS_CONNECTION_FAILED || 
           code === ErrorCodes.BS_TIMEOUT;
  }
  
  /**
   * 检查是否为资源相关错误
   * @param code 错误码
   * @returns 是否为资源错误
   */
  static isResourceError(code: ErrorCodeType): boolean {
    return code === ErrorCodes.BS_INSUFFICIENT_MEMORY;
  }
  
  /**
   * 创建标准的操作结果
   * @param code 错误码
   * @param message 自定义消息（可选）
   * @param data 附加数据（可选）
   * @returns 操作结果
   */
  static createResult(code: ErrorCodeType, message?: string, data?: any): BiShareResult {
    return {
      success: this.isSuccess(code),
      code: code,
      message: message || this.getErrorMessage(code),
      data: data
    };
  }
  
  /**
   * 创建成功结果
   * @param message 成功消息（可选）
   * @param data 附加数据（可选）
   * @returns 成功结果
   */
  static createSuccessResult(message?: string, data?: any): BiShareResult {
    return this.createResult(ErrorCodes.BS_OK, message || "操作成功", data);
  }
  
  /**
   * 创建错误结果
   * @param code 错误码
   * @param message 错误消息（可选）
   * @returns 错误结果
   */
  static createErrorResult(code: ErrorCodeType, message?: string): BiShareResult {
    return this.createResult(code, message);
  }
}

/**
 * BiShare异常类
 */
export class BiShareError extends Error {
  public readonly code: ErrorCodeType;
  
  constructor(code: ErrorCodeType, message?: string) {
    super(message || BiShareErrorUtils.getErrorMessage(code));
    this.name = 'BiShareError';
    this.code = code;
  }
  
  /**
   * 检查是否为特定错误码
   * @param code 错误码
   * @returns 是否匹配
   */
  isCode(code: ErrorCodeType): boolean {
    return this.code === code;
  }
  
  /**
   * 检查是否为初始化错误
   * @returns 是否为初始化错误
   */
  isInitError(): boolean {
    return BiShareErrorUtils.isInitError(this.code);
  }
  
  /**
   * 检查是否为参数错误
   * @returns 是否为参数错误
   */
  isParamError(): boolean {
    return BiShareErrorUtils.isParamError(this.code);
  }
  
  /**
   * 检查是否为网络错误
   * @returns 是否为网络错误
   */
  isNetworkError(): boolean {
    return BiShareErrorUtils.isNetworkError(this.code);
  }
  
  /**
   * 检查是否为资源错误
   * @returns 是否为资源错误
   */
  isResourceError(): boolean {
    return BiShareErrorUtils.isResourceError(this.code);
  }
}
