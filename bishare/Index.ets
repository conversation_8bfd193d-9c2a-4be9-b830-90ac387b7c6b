
/**
 * BiShare模块统一导出文件
 *
 * 这个文件是BiShare模块的主要入口点，导出所有公共API、类型定义和管理器。
 *
 * 架构说明：
 * 1. NAPI层：直接从原生模块导出的底层API
 * 2. 管理层：基于NAPI封装的高级管理器
 * 3. 类型层：TypeScript类型定义和接口
 */

// ============================================================================
// NAPI底层API导出 (直接从原生模块导出)
// ============================================================================

// 从原生NAPI模块导出所有函数和常量
export {
  // 核心服务函数
  initialize,
  release,

  // 设备管理函数
  discoverDevices,
  clearDiscoveredDevices,
  getDiscoveredDevices,
  setDeviceInfo,
  setDeviceModel,
  getDeviceModel,
  resetDeviceModel,
  findRemoteDevice,

  // 录制管理函数
  startScreenRecord,
  stopScreenRecord,
  startCapture,
  screenshot,

  // 网络管理函数
  setNetworkInfo,
  getRootPath,
  getCurrentDirector,
  setSize,
  setDefaultAudioOutputDevice,

  // 事件管理函数
  on,
  off,
  once,

  // 常量导出
  VERSION,

  // 日志优先级常量
  LOG_PRIORITY_DEBUG,
  LOG_PRIORITY_INFO,
  LOG_PRIORITY_WARN,
  LOG_PRIORITY_ERROR,

  // 事件类型常量
  EVENT_DEVICE_INFO,
  EVENT_DEVICE_STATUS,
  EVENT_CONNECT_STATUS,

  // 错误码常量
  BS_OK,
  BS_ERROR,
  BS_NOT_INIT,
  BS_INVALID_PARAM,
  BS_NOT_SUPPORTED,
  BS_PERMISSION_DENIED,
  BS_DEVICE_NOT_FOUND,
  BS_CONNECTION_FAILED,
  BS_TIMEOUT,
  BS_INSUFFICIENT_MEMORY,

  // 类型定义导出
  EventData,
  RecordingOptions,
  ScreenshotOptions,
  DeviceInfo,
  NetworkInfoOptions,
  NetworkType,
  BlogPriority,
  InitOptions,
  Callback,
  EventCallback,
  EventType,
  RecordingManager
} from 'libbishare_napi.so';

// ============================================================================
// 高级管理器导出 (基于NAPI封装的管理层)
// ============================================================================

// 从ETS层导出类型定义（与NAPI层保持兼容）
export {
  EventType as EtsEventType,
  EventCallback as EtsEventCallback,
  Callback as EtsCallback,
  InitOptions as EtsInitOptions,
  DeviceInfoOptions,
  DeviceInfo as EtsDeviceInfo,
  RecordingOptions as EtsRecordingOptions,
  BlogPriority as EtsBlogPriority,
  EventData as EtsEventData,
  NetworkInfoOptions as EtsNetworkInfoOptions,
  NetworkType as EtsNetworkType,
  // 扩展类型
  BiShareError,
  BiShareResult,
  DeviceConnectionStatus,
  ExtendedDeviceInfo,
  RecordingStatus,
  RecordingInfo,
  NetworkStatus,
  NetworkInfo,
  BiShareEventType,
  BiShareEventData,
  BiShareEventListener,
  DeviceEventListener,
  RecordingEventListener,
  NetworkEventListener,
  FileEventListener,
  MessageEventListener
} from './src/main/ets/interfaces/BiShareTypes';

// 导出高级管理器
export { default as BiShareManager } from './src/main/ets/core/BiShareManager';
export { default as EtsRecordingManager } from './src/main/ets/core/RecordingManager';
export { default as EventManager } from './src/main/ets/core/EventManager';
export { default as EtsDeviceManager } from './src/main/ets/core/DeviceManager';

// ============================================================================
// 使用说明
// ============================================================================

/**
 * 使用方式：
 *
 * 1. 直接使用NAPI底层API：
 * ```typescript
 * import { initialize, discoverDevices, BS_OK } from '@ohos/bishare';
 *
 * // 初始化服务
 * const result = await initialize(true, true, "/data/logs", BlogPriority.INFO);
 * if (result === BS_OK) {
 *   console.log('初始化成功');
 * }
 * ```
 *
 * 2. 使用高级管理器：
 * ```typescript
 * import { BiShareManager, InitOptions } from '@ohos/bishare';
 *
 * const manager = BiShareManager.getInstance();
 * const options: InitOptions = {
 *   isConsole: true,
 *   isFile: true,
 *   filePath: "/data/logs",
 *   priority: BlogPriority.INFO
 * };
 * await manager.initialize(options);
 * ```
 *
 * 3. 混合使用（推荐）：
 * ```typescript
 * import {
 *   BiShareManager,     // 高级管理器
 *   initialize,         // 底层API
 *   BS_OK,             // 常量
 *   EventType          // 类型定义
 * } from '@ohos/bishare';
 * ```
 */
