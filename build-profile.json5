{"app": {"products": [{"name": "default", "signingConfig": "default", "compileSdkVersion": 10, "compatibleSdkVersion": 10, "runtimeOS": "OpenHarmony"}], "buildModeSet": [{"name": "debug"}, {"name": "release"}], "signingConfigs": [{"name": "default", "material": {"certpath": "C:\\Users\\<USER>\\.ohos\\config\\openharmony\\auto_ohos_default_boescreenprojection_boe.ts.boeshare.harmony.cer", "storePassword": "0000001B2F3F8187E2896825A0F56A1F672E8F7363131A6DB9543704DB6DE61592D3C93C91ECE747F6E987", "keyAlias": "debugKey", "keyPassword": "0000001BF5F421604D28A4827CCE39EDD8A94B81C5AA35F2792FFD2052B2BD82304CA0E72E4A20A55D9B01", "profile": "C:\\Users\\<USER>\\.ohos\\config\\openharmony\\auto_ohos_default_boescreenprojection_boe.ts.boeshare.harmony.p7b", "signAlg": "SHA256withECDSA", "storeFile": "C:\\Users\\<USER>\\.ohos\\config\\openharmony\\auto_ohos_default_boescreenprojection_boe.ts.boeshare.harmony.p12"}}]}, "modules": [{"name": "entry", "srcPath": "./entry", "targets": [{"name": "default", "applyToProducts": ["default"]}]}, {"name": "bishare", "srcPath": "./bishare"}]}